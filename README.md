# Truthkeep-Microchip

## A modern web-based data analytics software platform by Truthkeep.

![](screenshots/login.png)

![](screenshots/timeline.png)

<hr>

This repository contains a web application which downloads and analyzes data from
YouTube and Reddit using AI.

### README Contents:

- [Basic setup](#basic-setup)
- [Running tests](#running-tests)
- [Viewing API documentation](#api-documentation)
- [Making commits](#making-commits)
- [Deploying the application](#deployment)
- [Architecture](#architecture)
- [External resources](#external-resources)

## Basic Setup

Start by creating a `.env` file in the `api` directory. See `api/.env.example`
for the required variables.

Ensure Docker is installed and running.

Ensure you are running at least version 18 of Node.js.

### Start up backing services

```
cd api
docker-compose up
```

### Start up NestJS server

```
cd api
npm install
npm run setup-database
npm run start:dev
```

### Start up frontend

```
cd web
npm install
npm run dev
```

You can now open the app and log in using the admin credentials in your `.env`
file.

## Running tests

### E2E tests

End-to-end tests with [<PERSON><PERSON>](https://playwright.dev/) are found in the
`test` directory.

Convenience scripts for running tests:

```
# Runs development builds of frontend and backend
# Both have live reloading enabled
# Opens Playwright UI

./test.dev.sh
```

```
# Runs production builds of both frontend and backend
# NO live reloading will be enabled
# Runs headlines tests with Playwright

./test.prod.sh
```

### Backend e2e tests

The `api` directory contains its own [Jest](https://jestjs.io/) tests for
testing the backend end-to-end (with mocked 3rd party APIs).

```
cd api
npm run test:e2e
```

### Unit tests

Run unit tests for the backend:

```
cd api
npm test
```

Run unit tests for the frontend:

```
cd web
npm run vitest
```

## API documentation

Swagger docs are visible from the following URL when running the project:

```
http://localhost:3000/api
```

You can read more about Nest and Swagger here:
https://docs.nestjs.com/openapi/introduction.

## Making commits

Linting and formatting is enforced at commit time with
[husky](https://typicode.github.io/husky/).

We also use [lint-staged](https://www.npmjs.com/package/lint-staged) in order to
speed this up.

### Backend

To fix linting errors in the `api` directory you can use the following command:

```
# inside ./api

npm run format:fix
```

### Frontend

To fix linting errors in the `web` directory you can use the following command:

```
# inside ./web

npm run prettier:write
```

## Deployment

The branches `development`, `staging`, and `main` are deployed automatically by [aws-deployment.yml](.github/workflows/aws-deployment.yml).

## Architecture

### Backend

#### Database

Postgres is the database provider. In local development a [pgvector](https://github.com/pgvector/pgvector) container from [dockerhub](https://hub.docker.com/r/pgvector/pgvector) is used, while on AWS we use RDS hosted Postgres which provides the pgvector extension

We also use Prisma to manage database migrations and running queries. To change
the database schema first edit the file `prisma/schema.prisma` then run:

```
# inside ./api
npm run migrate:dev
```

You can read more about Prisma here: https://www.prisma.io

#### Authentication

The authentication implementation uses JWTs stored as
[HTTP only](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Set-Cookie#httponly)
cookies. Authentication tokens have a short expiry and can be replaced using a
refresh token.

#### Background jobs

Fetching and analyzing data is performed as background jobs with
[BullMQ](https://docs.bullmq.io/) (backed by Redis). You can read more about
integrating BullMQ with Nest [here](https://docs.nestjs.com/techniques/queues).

Additionally, we are using
[BullBoard](https://www.npmjs.com/package/@bull-board/nestjs) to make it easier
to view/retry/debug background jobs. You can access it here:

```
# Log in before visiting
http://localhost:3000/api/queues
```

### Frontend

The frontend in this project is based on the
[Mantine Vite Template](https://github.com/mantinedev/vite-template).

#### Storybook

This project includes [Storybook](https://storybook.js.org/) to make it easier
to develop beautiful components.

To run Storybook:

```
# inside ./web
npm run storybook
```

To build a production storybook bundle to `storybook-static`:

```
# inside ./web
npm run storybook:build
```

## External resources:

This project can be run locally or deployed on Amazon Web Services.

The following resources are required to successfully run the project with its
full capabilities.

#### [YouTube Data API](https://developers.google.com/youtube/v3)

The YouTube Data API is used to download metadata, including comments, on videos
from various YouTube channels. This API is administered through Google Cloud
Platform.

#### [yt-dlp](https://github.com/yt-dlp/yt-dlp)

yt-dlp provides additional data not provided by the YouTube data API, namely
video subtitles.

##### Getting un-blocked

YouTube may block our yt-dlp requests which require one or both of the following workarounds:

#### 1. Passing cookies to yt-dlp

First follow [this guide](<(https://github.com/yt-dlp/yt-dlp/wiki/FAQ#how-do-i-pass-cookies-to-yt-dlp)>) on extracting cookies from a real authenticated session on youtube.com.
Next, submit your cookies using the endpoint `/api/task/youtube/cookies`.
Now your session cookies will be used be yt-dlp to download subtitles.

#### 2. IP blocking

Even passing cookies may not be enough to get unblocked, so we also support proxying requests to another machine, e.g your local machine.

Create SOCKS5 proxy on your local machine on port `1080`

```
> ssh -D 1080 -N localhost
```

At the same time start a tcp tunnel on your local machine with ngrok (Requires an account with credit card on file, though it is free).

```
> ngrok tcp 1080
```

Finally, set the proxy URL with the endpoint `/api/yt-dlp-proxy`.
If we see the following output from ngrok:

```
Forwarding tcp://2.tcp.ngrok.io:12947 -> localhost:1080
```

Then the correct proxy URL is

```
socks5://2.tcp.ngrok.io:12947
```

#### [Reddit API](https://www.reddit.com/dev/api/)

The Reddit API provides data about posts and comments.
Create a Reddit account and go to the [apps](https://www.reddit.com/prefs/apps/) page to get API credentials

#### Salesforce forums

Threads from the [Microchip Community Forum](https://forum.microchip.com) and [AVR Freaks](https://www.avrfreaks.net/) forum are fetched from a custom Salesforce API.

#### All About Circuits forums

Threads from the [All About Circuits](https://forum.allaboutcircuits.com/) forum are scraped and cleaned by an external service deployed on AWS Lambda. This application accesses the cleaned data from a shared S3 bucket.

#### [OpenAI API](https://platform.openai.com/)

The OpenAI API is used to analyze data with AI.
