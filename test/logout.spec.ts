import { test, expect } from "@playwright/test";
import { loginAsAdmin } from "./util";

test("Clicking logout button successfully logouts the user and brings the user to the login page", async ({
  page,
}) => {
  await loginAsAdmin(page);
  await page.waitForURL("/timeline");

  const logoutResponsePromise = page.waitForResponse(
    (response) =>
      response.request().method() === "POST" &&
      response.url().endsWith("/logout")
  );

  await page.getByRole("button", { name: "Logout" }).click();
  const logoutResponse = await logoutResponsePromise;

  await page.waitForURL("**/login");

  expect(logoutResponse.status()).toBe(204);
  await expect(page.getByRole("textbox", { name: "Email" })).toBeVisible();
  await expect(page.getByRole("textbox", { name: "Password" })).toBeVisible();

  await page.goto("/timeline");
  await page.waitForURL("**/login");

  expect(page.url()).toContain("/login");
  await expect(page.getByRole("textbox", { name: "Email" })).toBeVisible();

  await expect(page.getByRole("textbox", { name: "Password" })).toBeVisible();
});
