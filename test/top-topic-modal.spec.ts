import { test, expect } from "@playwright/test";
import { loginAsAdmin } from "./util";

test("Open top topic modal from analytics page", async ({ page }) => {
  await loginAsAdmin(page);

  await page.goto("/analytics");
  await page.waitForURL("/analytics");

  await expect(
    page.locator(
      ".mantine-Loader-root:visible, .mantine-LoadingOverlay-root:visible"
    )
  ).toHaveCount(0);

  await page.waitForSelector("#industry-trending-topics-heading", {
    state: "visible",
  });

  const heading = page.locator("#industry-trending-topics-heading");
  await expect(heading).toBeVisible();

  const trendingTopicsRegion = page.locator(
    '[aria-labelledby="industry-trending-topics-section"]'
  );
  await expect(trendingTopicsRegion).toBeVisible();

  const topTopicSection = trendingTopicsRegion.getByRole("button").first();
  const topTopicName =
    (await topTopicSection.getByRole("heading").textContent()) ?? "";
  expect(topTopicName).toBeTruthy();
  await topTopicSection.click();

  const topTopicModal = await page.getByRole("dialog", {
    name: "Top Topic Breakdown",
  });
  await expect(topTopicModal).toBeVisible();
  await expect(topTopicModal.getByText(topTopicName)).toBeVisible();
});
