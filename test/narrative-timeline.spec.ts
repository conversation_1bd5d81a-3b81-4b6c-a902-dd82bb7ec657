import test, { expect } from "@playwright/test";
import { loginAsAdmin } from "./util";

test("Can view timeline filtered by a narrative", async ({ page }) => {
  await loginAsAdmin(page);

  await page.goto("/timeline/narrative/1");

  await expect(page.locator("h2")).toHaveText("seeded narrative #1");
});

test("Handles 404 error", async ({ page }) => {
  await loginAsAdmin(page);

  await page.goto("/timeline/narrative/this-narrative-does-not-exist");

  await expect(page.getByText("404: Narrative not found")).toBeVisible();
});
