import { test, expect } from "@playwright/test";
import { loginAsAdmin, readLatestEmailTo, waitFor } from "./util";
import { randomUUID } from "crypto";
import dotenv from "dotenv";
dotenv.config();

test("Can successfully onboard a user", async ({ page }) => {
  // Create a new user
  const email = `user-${randomUUID()}@example.com`;
  const firstname = `fname-${randomUUID()}`;
  const lastname = `lname-${randomUUID()}`;
  const randomStrongPassword = `StrongPW${randomUUID()}@`;

  await loginAsAdmin(page);

  await page.goto("/user-management");

  await page.getByRole("button", { name: "Add New User" }).click();
  await expect(page.getByText("First Name")).toBeVisible();

  await page.getByLabel("First Name").fill(firstname);
  await page.getByLabel("Last Name").fill(lastname);
  await page.getByLabel("Email").fill(email);

  await page.getByRole("button", { name: "Save" }).click();

  await expect(
    page.getByRole("button", { name: "Add New User" })
  ).toBeVisible();

  const row = page.locator("table tbody tr", { hasText: email });
  await expect(row).toBeVisible();
  await waitFor(async () => {
    return await row.getByText(`${firstname} ${lastname}`).isVisible();
  });

  // logout
  await page.getByRole("button", { name: "Logout" }).click();
  await page.waitForURL("**/login");

  // grab the latest email and go to the link in it
  const sentEmail = await waitFor(() => readLatestEmailTo(email));
  const text: string = sentEmail.text;
  const [onboardingLink] = text.match(/http\S+/) || [];
  if (!onboardingLink) throw new Error("no link in email");

  await page.goto(onboardingLink);

  // set up password
  await expect(page.getByText("Account Setup")).toBeVisible();
  await expect(page.getByRole("textbox", { name: "Name" })).toHaveValue(
    `${firstname} ${lastname}`
  );
  await expect(page.getByRole("textbox", { name: "Email" })).toHaveValue(email);
  await page
    .getByRole("textbox", { name: "New Password" })
    .fill(randomStrongPassword);
  await page
    .getByRole("textbox", { name: "Confirm Password" })
    .fill(randomStrongPassword);

  await page.getByRole("button", { name: "Join Truthkeep" }).click();
  await page.waitForURL("/login");
  // login with the new password
  await page.getByRole("textbox", { name: "Email" }).fill(email);
  await page
    .getByRole("textbox", { name: "Password" })
    .fill(randomStrongPassword);
  await page.getByRole("button", { name: "Login" }).click();
  await page.waitForURL("/timeline");
});
