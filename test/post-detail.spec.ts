import { test, expect } from "@playwright/test";
import { loginAsAdmin, waitFor } from "./util";
import { time } from "node:console";

test("Can visit post detail page", async ({ page, context }) => {
  await context.grantPermissions(["clipboard-read", "clipboard-write"]);

  await loginAsAdmin(page);
  await page.goto("/timeline");
  const timelineUrl = page.url();

  const linkButton = page.getByRole("button", { name: /copy link/ }).first();
  await linkButton.scrollIntoViewIfNeeded();
  await linkButton.click();

  let url: string;

  const clipboardAvailable = await page.evaluate(
    () => "clipboard" in navigator
  );
  if (clipboardAvailable) {
    url = await waitFor(async () =>
      page.evaluate(() => navigator.clipboard.readText())
    );
  } else {
    console.warn("Clipboard API not available in this environment");
    url = `${timelineUrl}/1`;
  }

  expect(url).toMatch(new RegExp(`${timelineUrl}/\\d+`));

  await page.goto(url);
  await expect(page.getByText(/Seeded.+summary/)).toBeVisible();
});

test("Handles 404 error", async ({ page, context }) => {
  await loginAsAdmin(page);
  await page.goto("/timeline/bad-id");

  await expect(page.getByText("404: Post not found")).toBeVisible();
  await page.getByText("Return to home").click();
  await page.waitForURL("/");
});
