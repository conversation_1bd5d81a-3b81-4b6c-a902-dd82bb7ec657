import { test, expect } from "@playwright/test";
import { loginAsUser } from "./util";
import { randomUUID } from "crypto";
import dotenv from "dotenv";
dotenv.config();

test("Can successfully change password on the settings page using the change password modal", async ({
  page,
}) => {
  const passwordResetTestEmail = "<EMAIL>";
  const randomStrongPassword = `StrongPW${randomUUID()}@`;
  const oldPasswword = process.env.ADMIN_PASS as string;

  await loginAsUser(page, passwordResetTestEmail, oldPasswword);

  await page.waitForURL("/timeline");

  await page.goto("/settings");
  await page.waitForURL("/settings");
  await page.getByRole("button", { name: "Change" }).click();

  await expect(page.getByText("Change Password")).toBeVisible();
  await expect(page.getByText("Current Password")).toBeVisible();
  await expect(page.getByText("Confirm New Password")).toBeVisible();

  await page
    .getByRole("textbox", { name: "Current Password" })
    .fill(oldPasswword);
  await page
    .getByRole("textbox", { name: "New Password", exact: true })
    .fill(randomStrongPassword);
  await page
    .getByRole("textbox", { name: "Confirm New Password", exact: true })
    .fill(randomStrongPassword);

  const modal = page.getByRole("dialog", { name: "Change Password" });
  const changeButton = modal.getByRole("button", { name: "Change" });
  await changeButton.click();

  await page.waitForURL("/login");

  await loginAsUser(page, passwordResetTestEmail, randomStrongPassword);
});
