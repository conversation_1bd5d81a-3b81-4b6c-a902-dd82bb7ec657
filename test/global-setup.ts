import { chromium, type FullConfig } from "@playwright/test";
import { waitFor } from "./util";

const STARTUP_TIMEOUT = 10000;

async function globalSetup(config: FullConfig) {
  const { baseURL } = config.projects[0].use;
  const healthcheckUrl = `${baseURL}/api/healthcheck`;
  console.log(`Waiting for ${healthcheckUrl} to be healthy...`);

  const isApiHealthly = await waitFor(async () => {
    const response = await fetch(healthcheckUrl);
    if (!response.ok) throw response;
    return response.ok;
  }, STARTUP_TIMEOUT);

  if (!isApiHealthly)
    throw new Error(`Healthcheck failed for ${healthcheckUrl}`);
}

export default globalSetup;
