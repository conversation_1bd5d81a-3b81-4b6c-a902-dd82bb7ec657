import { test, expect } from "@playwright/test";
import { loginAsAdmin } from "./util";

test("Edit User Modal Field Validation Works", async ({ page }) => {
  await loginAsAdmin(page);

  await page.goto("/user-management");
  await page.waitForURL("/user-management");

  const editButton = page.locator('img[alt="edit Icon"]:visible').first();
  await expect(editButton).toBeVisible();
  await editButton.click();

  const editModal = page.getByRole("dialog", { name: "Edit User" });
  await expect(editModal).toBeVisible();

  const firstNameInput = editModal.getByLabel("First Name");
  const lastNameInput = editModal.getByLabel("Last Name");
  const emailInput = editModal.getByLabel("Email");

  await firstNameInput.fill("");
  await lastNameInput.fill("");
  await emailInput.fill("");

  await editModal.getByRole("button", { name: "Save" }).click();

  await expect(editModal.getByText("First name is required")).toBeVisible();
  await expect(editModal.getByText("Last name is required")).toBeVisible();
  await expect(editModal.getByText("Invalid email")).toBeVisible();
});

test("Edit modal preloads the correct user data", async ({ page }) => {
  await loginAsAdmin(page);
  await page.goto("/user-management");
  await page.waitForURL("/user-management");
  await page.waitForSelector("table tbody tr");

  const firstRow = page.locator("table tbody tr").first();

  const nameText = (await firstRow.locator("td").nth(1).innerText()).trim();
  const emailText = (await firstRow.locator("td").nth(2).innerText()).trim();

  const [firstName, lastName] = nameText.split(" ");

  await firstRow.locator('button:has(img[alt="edit Icon"])').click();

  const editModal = page.getByRole("dialog", { name: "Edit User" });
  await expect(editModal).toBeVisible();

  const firstNameInput = editModal.locator('input[data-path="firstName"]');
  const lastNameInput = editModal.locator('input[data-path="lastName"]');
  const emailInput = editModal.locator('input[data-path="email"]');

  await expect(firstNameInput).toHaveValue(firstName);
  await expect(lastNameInput).toHaveValue(lastName);
  await expect(emailInput).toHaveValue(emailText);
});

test("Edit modal show confirmation modal when attempting to delete", async ({
  page,
}) => {
  await loginAsAdmin(page);
  await page.goto("/user-management");
  await page.waitForURL("/user-management");
  await page.waitForSelector("table tbody tr");

  const firstRow = page.locator("table tbody tr").first();
  const nameText = (await firstRow.locator("td").nth(1).innerText()).trim();

  await firstRow.locator('button:has(img[alt="edit Icon"])').click();

  const editModal = page.getByRole("dialog", { name: "Edit User" });
  await expect(editModal).toBeVisible();

  await editModal.getByRole("button", { name: "Delete User" }).click();

  const deleteModal = page.getByRole("dialog", { name: "Delete User" });
  await expect(deleteModal).toBeVisible();

  await expect(
    deleteModal.getByText("Are you sure you want to delete this user?")
  ).toBeVisible();
  await expect(deleteModal.getByText(nameText)).toBeVisible();
});

test("Edit modal show confirmation modal when you change the admin status", async ({
  page,
}) => {
  await loginAsAdmin(page);
  await page.goto("/user-management");
  await page.waitForURL("/user-management");
  await page.waitForSelector("table tbody tr");

  const firstRow = page.locator("table tbody tr").first();
  const nameText = (await firstRow.locator("td").nth(1).innerText()).trim();

  await firstRow.locator('button:has(img[alt="edit Icon"])').click();

  const editModal = page.getByRole("dialog", { name: "Edit User" });
  await expect(editModal).toBeVisible();

  const adminLabel = editModal.locator(
    'label:has-text("Admin Permissions"):visible'
  );
  await adminLabel.click();

  await editModal.getByRole("button", { name: "Save" }).click();

  await expect(
    editModal.getByText(
      "Are you sure you want to change this user's admin status?"
    )
  ).toBeVisible();
  await expect(editModal.getByText(nameText)).toBeVisible();
});
