import { test, expect } from "@playwright/test";
import { randomUUID } from "crypto";
import { loginAsAdmin } from "./util";

test("New User Modal Field Validation Works", async ({ page }) => {
  const email = `user-${randomUUID()}@example.com`;
  const firstname = `fname-${randomUUID()}`;
  const lastname = `lname-${randomUUID()}`;
  await loginAsAdmin(page);

  await page.goto("/user-management");
  await page.waitForURL("/user-management");

  await expect(page.getByText("Add New User")).toBeVisible();
  await page.getByRole("button", { name: "Add New User" }).click();
  await expect(page.getByText("First Name")).toBeVisible();
  await expect(page.getByText("Last Name")).toBeVisible();

  await page.getByRole("button", { name: "Save" }).click();

  await expect(page.getByText("First name is required")).toBeVisible();
  await expect(page.getByText("Last name is required")).toBeVisible();
  await expect(page.getByText("Invalid email")).toBeVisible();

  await page.getByLabel("First Name").fill(firstname);
  await page.getByLabel("Last Name").fill(lastname);
  await page.getByLabel("Email").fill("invalid-email");

  await page.getByRole("button", { name: "Save" }).click();

  await expect(page.getByText("Invalid email")).toBeVisible();

  await page.getByLabel("Email").fill(email);

  await page.getByRole("button", { name: "Save" }).click();

  await expect(page.getByText("First name is required")).not.toBeVisible();
  await expect(page.getByText("Last name is required")).not.toBeVisible();
  await expect(page.getByText("Invalid email")).not.toBeVisible();
});
