import { test, expect } from "@playwright/test";
import { loginAsAdmin } from "./util";

test("New Topic Modal Field Validation Works", async ({ page }) => {
  await loginAsAdmin(page);

  await page.goto("/topic-management");
  await page.waitForURL("/topic-management");

  await page.getByRole("button", { name: "Add Topic" }).click();

  const addTopicModal = page.getByRole("dialog", { name: "Add Topic" });
  await expect(addTopicModal).toBeVisible();

  await expect(addTopicModal.getByText("Topic Name")).toBeVisible();

  await addTopicModal.getByRole("button", { name: "Save" }).click();

  await expect(addTopicModal.getByText("Topic name is required")).toBeVisible();
});
