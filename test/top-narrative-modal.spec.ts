import { test, expect } from "@playwright/test";
import { loginAsAdmin } from "./util";

test("Open top narrative modal from analytics page", async ({ page }) => {
  await loginAsAdmin(page);

  await page.goto("/analytics");
  await page.waitForURL("/analytics");

  await expect(
    page.locator(
      ".mantine-Loader-root:visible, .mantine-LoadingOverlay-root:visible"
    )
  ).toHaveCount(0);

  await page.waitForSelector("#top-narratives-heading", {
    state: "visible",
  });

  const heading = page.locator("#top-narratives-heading");
  await expect(heading).toBeVisible();

  const topNarrativesRegion = page.locator(
    '[aria-labelledby="top-narratives-section"]'
  );
  await expect(topNarrativesRegion).toBeVisible();

  const topNarrativeButton = topNarrativesRegion.locator("button").first();
  await expect(topNarrativeButton).toBeVisible();

  const topNarrativeSection = topNarrativesRegion.getByRole("button").first();
  const topNarrativeName =
    (await topNarrativeSection.getByRole("heading").textContent()) ?? "";
  expect(topNarrativeName).toBeTruthy();
  await topNarrativeSection.click();

  const topNarrativeModal = await page.getByRole("dialog", {
    name: "Narrative Breakdown",
  });
  await expect(topNarrativeModal).toBeVisible();
  await expect(topNarrativeModal.getByText(topNarrativeName)).toBeVisible();
});
