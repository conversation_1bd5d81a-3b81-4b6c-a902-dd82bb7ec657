import { test, expect } from "@playwright/test";
import { loginAsAdmin, readLatestEmailTo, waitFor } from "./util";
import { randomUUID } from "crypto";
import dotenv from "dotenv";
dotenv.config();

test("Visiting forgot password page while already being authenticated brings you to home page", async ({
  page,
}) => {
  await loginAsAdmin(page);

  await page.waitForURL("/timeline");

  await page.goto("/forgot-password");
  await page.waitForURL("/timeline");
});

test("Can successfully reset password via forgot password flow", async ({
  page,
}) => {
  const randomStrongPassword = `StrongPW${randomUUID()}@`;
  const testEmail = "<EMAIL>";
  await page.goto("/forgot-password");

  await page.waitForURL("/forgot-password");
  await expect(page.getByText("Forgot Password")).toBeVisible();

  await page
    .getByRole("textbox", { name: "<PERSON>rna<PERSON> or Email" })
    .fill(testEmail);
  await page.getByRole("button", { name: "Next" }).click();

  await expect(
    page.getByText("Enter the verification code <NAME_EMAIL>")
  ).toBeVisible();
  const sentEmail = await waitFor(() => readLatestEmailTo(testEmail));
  const [code] = sentEmail.text.match(/\w\w\w\w-\w\w\w\w/);

  await page.getByRole("textbox", { name: "Verification Code" }).fill(code);
  await page.getByRole("button", { name: "Verify" }).click();

  await expect(page.getByText("Change Your Password")).toBeVisible();

  await expect(page.getByRole("textbox", { name: "Name" })).toHaveValue(
    "testFirstName testLastName"
  );
  await expect(page.getByRole("textbox", { name: "Email" })).toHaveValue(
    testEmail
  );
  await page
    .getByRole("textbox", { name: "New Password" })
    .fill(randomStrongPassword);
  await page
    .getByRole("textbox", { name: "Confirm Password" })
    .fill(randomStrongPassword);

  await page.getByRole("button", { name: "Reset" }).click();

  await page.waitForURL("/login");
  await page.getByRole("textbox", { name: "Email" }).fill(testEmail);
  await page
    .getByRole("textbox", { name: "Password" })
    .fill(randomStrongPassword);
  await page.getByRole("button", { name: "Login" }).click();
  await page.waitForURL("/timeline");
});

test("Shows warning for a weak password", async ({ page }) => {
  const weakPassword = `1234`;
  const testEmail = "<EMAIL>";
  await page.goto("/forgot-password");

  await page.waitForURL("/forgot-password");
  await expect(page.getByText("Forgot Password")).toBeVisible();

  await page
    .getByRole("textbox", { name: "Username or Email" })
    .fill(testEmail);
  await page.getByRole("button", { name: "Next" }).click();

  await expect(
    page.getByText("Enter the verification code <NAME_EMAIL>")
  ).toBeVisible();
  const sentEmail = await waitFor(() => readLatestEmailTo(testEmail));
  const [code] = sentEmail.text.match(/\w\w\w\w-\w\w\w\w/);

  await page.getByRole("textbox", { name: "Verification Code" }).fill(code);
  await page.getByRole("button", { name: "Verify" }).click();

  await expect(page.getByText("Change Your Password")).toBeVisible();
  await page.getByRole("textbox", { name: "New Password" }).fill(weakPassword);
  await page
    .getByRole("textbox", { name: "Confirm Password" })
    .fill(weakPassword);

  await page.getByRole("button", { name: "Reset" }).click();
  await expect(
    page.getByText("Password is not strong enough. Please try again.")
  ).toBeVisible();
});
