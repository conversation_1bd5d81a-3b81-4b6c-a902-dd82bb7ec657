import { test, expect } from "@playwright/test";
import { loginAsAdmin, loginAsUser } from "./util";

test(`Users belonging to a single tenant do not see the "Choose tenant" link`, async ({
  page,
}) => {
  // Mock user belonging to a single tenant
  await page.route("**/users/me", (route) => {
    return route.fulfill({
      status: 200,
      body: JSON.stringify({ tenants: [{ id: "1" }] }),
    });
  });

  await loginAsAdmin(page);

  await expect(
    page.getByRole("link", { name: "Change tenant" })
  ).not.toBeVisible();
});

test(`Users belonging to a multiple tenants see the "Choose tenant" link`, async ({
  page,
}) => {
  // Mock user belonging to multiple tenants
  await page.route("**/users/me", (route) => {
    return route.fulfill({
      status: 200,
      body: JSON.stringify({ tenants: [{ id: "1" }, { id: "2" }] }),
    });
  });

  await loginAsAdmin(page);

  await expect(page.getByRole("link", { name: "Change tenant" })).toBeVisible();
});

test(`Users belonging to a multiple tenants can switch tenants`, async ({
  page,
}) => {
  // Mock user belonging to multiple tenants
  await page.route("**/users/me", (route) => {
    return route.fulfill({
      status: 200,
      body: JSON.stringify({
        tenants: [
          { id: "1", name: "Tenant 1" },
          { id: "2", name: "Tenant 2" },
        ],
      }),
    });
  });

  await loginAsAdmin(page);

  // Go to tenant selection page
  await page.getByRole("link", { name: "Change tenant" }).click();

  await expect(page.getByRole("button", { name: "Tenant 1" })).toBeVisible();
  await expect(page.getByRole("button", { name: "Tenant 2" })).toBeVisible();

  // TODO test that clicking the buttons works
});
