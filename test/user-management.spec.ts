import { expect, test } from "@playwright/test";
import { waitFor } from "./util";
import { randomUUID } from "crypto";
import { loginAsAdmin } from "./util";

test("Can successfully add a new user and delete it", async ({ page }) => {
  const email = `user-${randomUUID()}@example.com`;
  const firstname = `fname-${randomUUID()}`;
  const lastname = `lname-${randomUUID()}`;

  await loginAsAdmin(page);

  await page.goto("/user-management");

  await page.getByRole("button", { name: "Add New User" }).click();
  await expect(page.getByText("First Name")).toBeVisible();

  await page.getByLabel("First Name").fill(firstname);
  await page.getByLabel("Last Name").fill(lastname);
  await page.getByLabel("Email").fill(email);

  await page.getByRole("button", { name: "Save" }).click();

  await expect(
    page.getByRole("button", { name: "Add New User" })
  ).toBeVisible();

  const row = page.locator("table tbody tr", { hasText: email });
  await expect(row).toBeVisible();
  await waitFor(async () => {
    return await row.getByText(`${firstname} ${lastname}`).isVisible();
  });

  const checkbox = row.getByRole("checkbox", { name: "Select row" });
  await checkbox.check();
  await expect(
    page.getByRole("button", { name: "Delete Selected (1)" })
  ).toBeVisible();
  await page.getByRole("button", { name: "Delete Selected (1)" }).click();

  await expect(
    page.getByRole("button", { name: "Delete", exact: true })
  ).toBeVisible();
  await page.getByRole("button", { name: "Delete", exact: true }).click();

  await expect(row).toHaveCount(0);
});

test("Only shows admin accounts when the View Admins Only checkbox is checked", async ({
  page,
}) => {
  await loginAsAdmin(page);

  await page.goto("/user-management");

  await page.getByRole("checkbox", { name: "View Admins only" }).check();

  // check for all isAdmin switch since they should all be true
  await waitFor(async () => {
    const switches = await page.locator('input[role="switch"]').all();

    if (switches.length === 0) return false;

    for (const sw of switches) {
      const checked = await sw.isChecked();
      if (!checked) return false;
    }

    return true;
  });
});

test("Can successfully edit a user and then also delete it using the edit modal", async ({
  page,
}) => {
  await loginAsAdmin(page);
  await page.goto("/user-management");

  await waitFor(async () => (await page.locator("table tbody tr").count()) > 0);

  const firstRow = page.locator("table tbody tr").first();
  const originalName = (await firstRow.locator("td").nth(1).innerText()).trim();
  const originalEmail = (
    await firstRow.locator("td").nth(2).innerText()
  ).trim();

  await firstRow.locator('button:has(img[alt="edit Icon"])').click();
  const editModal = page.getByRole("dialog", { name: "Edit User" });
  await expect(editModal).toBeVisible();

  const editedFirstName = `edited-fname-${randomUUID()}`;
  const editedLastName = `edited-lname-${randomUUID()}`;
  const editedEmail = `edited-email-${randomUUID()}@example.com`;

  await editModal.getByLabel("First Name").fill(editedFirstName);
  await editModal.getByLabel("Last Name").fill(editedLastName);
  await editModal.getByLabel("Email").fill(editedEmail);

  const selectInput = editModal.locator('input[aria-haspopup="listbox"]');
  await selectInput.click();

  const firstOption = page.getByRole("option").first();
  const addedGroup = (await firstOption.innerText()).trim();
  await firstOption.click();

  await selectInput.press("Escape");

  await editModal.getByRole("button", { name: "Save" }).click();
  await expect(editModal).toBeHidden();

  await expect(
    page.locator("table tbody tr").filter({ hasText: originalName })
  ).toHaveCount(0);
  await expect(
    page.locator("table tbody tr").filter({ hasText: originalEmail })
  ).toHaveCount(0);

  const updatedName = (await firstRow.locator("td").nth(1).innerText()).trim();
  const updatedEmail = (await firstRow.locator("td").nth(2).innerText()).trim();
  expect(updatedName).toBe(`${editedFirstName} ${editedLastName}`);
  expect(updatedEmail).toBe(editedEmail);

  const groupsCell = firstRow.locator("td").nth(3);
  await expect(groupsCell).toContainText(addedGroup);

  await firstRow.locator('button:has(img[alt="edit Icon"])').click();
  await expect(editModal).toBeVisible();

  await editModal.getByRole("button", { name: "Delete User" }).click();

  const deleteModal = page.getByRole("dialog", { name: "Delete User" });

  await expect(
    deleteModal.getByText("Are you sure you want to delete this user?")
  ).toBeVisible();

  await deleteModal.getByRole("button", { name: "Delete" }).click();

  await expect(deleteModal).toBeHidden();

  await expect(
    page.locator("table tbody tr").filter({ hasText: editedEmail })
  ).toHaveCount(0);
});
