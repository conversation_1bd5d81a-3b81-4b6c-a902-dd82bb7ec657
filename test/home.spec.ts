import { test, expect } from "@playwright/test";
import { waitFor } from "./util";

test("Visiting home page without being authenticated brings you to login page", async ({
  page,
}) => {
  const networkCalls: string[] = [];
  page.on("request", (request) => {
    networkCalls.push(request.url());
  });
  await page.route("**/refresh", async (route) => {
    await route.fulfill({
      status: 401,
    });
  });

  await page.goto("/timeline");
  await waitFor(() => networkCalls.find((url) => url.includes("/refresh")));
  await page.waitForURL("**/login");

  expect(page.url()).toContain("/login");
  await expect(page.getByRole("textbox", { name: "Email" })).toBeVisible();

  await expect(page.getByRole("textbox", { name: "Password" })).toBeVisible();
});
