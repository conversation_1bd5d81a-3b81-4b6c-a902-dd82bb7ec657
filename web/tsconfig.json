{"compilerOptions": {"types": ["node", "@testing-library/jest-dom", "vitest/globals"], "target": "ESNext", "useDefineForClassFields": true, "lib": ["DOM", "DOM.Iterable", "ESNext"], "allowJs": false, "skipLibCheck": true, "esModuleInterop": false, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "ESNext", "moduleResolution": "Node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "paths": {"@/*": ["./src/*"], "@test-utils": ["./test-utils"]}}, "include": ["src", "test-utils"]}