import { DateRangeOptions, FilterForm } from '@/types/timelineTypes';

export const formatDate = (d: Date): string => {
  const yyyy = d.getFullYear();
  const mm = String(d.getMonth() + 1).padStart(2, '0');
  const dd = String(d.getDate()).padStart(2, '0');
  return `${yyyy}-${mm}-${dd}`;
};

export const dateRangeConverter = (
  dateRange: DateRangeOptions
): { dateFrom?: string; dateTo?: string } => {
  const today = new Date();
  const dateTo = formatDate(today);

  switch (dateRange) {
    case 'Last week': {
      const from = new Date(today);
      from.setDate(from.getDate() - 7);
      return { dateFrom: formatDate(from), dateTo };
    }
    case 'Last month': {
      const from = new Date(today);
      from.setMonth(from.getMonth() - 1);
      return { dateFrom: formatDate(from), dateTo };
    }
    case 'Last 6 months': {
      const from = new Date(today);
      from.setMonth(from.getMonth() - 6);
      return { dateFrom: formatDate(from), dateTo };
    }
    case 'Last year': {
      const from = new Date(today);
      from.setFullYear(from.getFullYear() - 1);
      return { dateFrom: formatDate(from), dateTo };
    }
    case 'All time':
    default: {
      return {};
    }
  }
};

export function timeAgo(date: string): string {
  const now = new Date();
  const secondsDelta = (now.getTime() - new Date(date).getTime()) / 1000;

  const units = [
    { name: 'year', seconds: 60 * 60 * 24 * 365 },
    { name: 'month', seconds: 60 * 60 * 24 * 30 },
    { name: 'week', seconds: 60 * 60 * 24 * 7 },
    { name: 'day', seconds: 60 * 60 * 24 },
    { name: 'hour', seconds: 60 * 60 },
    { name: 'minute', seconds: 60 },
    { name: 'second', seconds: 1 },
  ];

  for (const unit of units) {
    const count = Math.floor(secondsDelta / unit.seconds);
    if (count >= 1) {
      return `${count} ${unit.name}${count > 1 ? 's' : ''} ago`;
    }
  }

  return 'just now';
}

export const customDateRangeConverter = (customDateRange: string) => {
  if (!customDateRange) return {};
  const [from, to] = customDateRange.split(' & ');
  return from && to ? { dateFrom: from, dateTo: to } : {};
};

export const getDateRangeParams = (filterValues?: FilterForm) => {
  if (!filterValues || !filterValues.dateRange) return {};
  return filterValues.dateRange !== 'custom' && !filterValues.customDateRange
    ? dateRangeConverter(filterValues.dateRange as DateRangeOptions)
    : customDateRangeConverter(filterValues.customDateRange ?? '');
};
