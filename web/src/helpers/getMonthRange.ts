export function getMonthRange(monthYear: string): string {
  const [monthName, yearStr] = monthYear.split(' ');
  const year = Number(yearStr);

  const monthIndex = new Date(`${monthName} 1, ${year}`).getMonth();

  const start = new Date(year, monthIndex, 1);

  const end = new Date(year, monthIndex + 1, 0);

  const format = (d: Date) => d.toISOString().split('T')[0];

  return `${format(start)} & ${format(end)}`;
}
