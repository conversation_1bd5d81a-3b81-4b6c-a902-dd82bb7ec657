import type { UseFormReturnType } from '@mantine/form';

export function scopeFormHelper<
  T extends Record<string, any>,
  const K extends readonly (keyof T)[],
>(form: UseFormReturnType<T>, keys: K): UseFormReturnType<Record<string, any>> {
  const pick = <U extends T>(obj: U) =>
    keys.reduce((acc, k) => {
      (acc as any)[k as string] = obj[k];
      return acc;
    }, {} as Partial<U>) as Record<string, any>;

  return {
    ...form,
    values: pick(form.values),
    errors: pick(form.errors as any),
    getInputProps: (path: any, opts?: any) => form.getInputProps(path, opts),
    setFieldValue: (path: any, value: any) => form.setFieldValue(path, value),
    clearFieldError: (path: any) => form.clearFieldError(path),
    reset: () => form.reset(),
  } as unknown as UseFormReturnType<Record<string, any>>;
}
