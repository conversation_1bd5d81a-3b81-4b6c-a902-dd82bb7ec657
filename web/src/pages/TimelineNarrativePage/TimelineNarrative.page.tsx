import { useEffect, useState } from 'react';
import { Link, useParams } from 'react-router-dom';
import { Flex, Loader, Text } from '@mantine/core';
import { narrativeService } from '@/api/narratives/narrativeService';
import { authGuardHOC } from '@/components/authGuardHOC';
import { Layout } from '@/components/Layout/Layout';
import { NarrativeProps } from '@/types/narrativeTypes';
import { useTimeline } from '../TimelinePage/useTimeline';
import TimelineNarrative from './TimelineNarrative';

const TimelinePage = () => {
  const params = useParams();
  const narrativeId = params.id;

  const {
    groupOptions,
    timelineResponseObject,
    filterForm,
    paginationObject,
    loading: timelineLoading,

    handlePageNavigation,
    toggleBookmark,
  } = useTimeline({
    narrativeIds: narrativeId ? [narrativeId] : undefined,
    formInitialValues: { relevance: [1, 5], dateRange: 'All time' },
  });

  const [narrative, setNarrative] = useState<NarrativeProps>();
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    setNarrative(undefined);

    if (!narrativeId) {
      setLoading(false);
      return;
    }
    setLoading(true);
    narrativeService
      .fetchNarrative(narrativeId)
      .then((response) => {
        setNarrative(response);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [params]);

  const isNotFoundError = !loading && !narrative;

  return (
    <Layout>
      <div className="h-full max-w-[1280px] px-[12px] md:px-[40px]">
        {loading && (
          <Flex w="100%" mih={300} align="center" justify="center">
            <Loader color="violet" />
          </Flex>
        )}
        {isNotFoundError && (
          <Flex direction="column" justify="center" align="center" className="my-40">
            <Text>404: Narrative not found</Text>
            <br />
            <Text>
              <Link to="/" replace className="underline text-blue-500">
                Return to home
              </Link>
            </Text>
          </Flex>
        )}
        {narrative && (
          <TimelineNarrative
            narrative={narrative}
            groupOptions={groupOptions}
            filterForm={filterForm}
            timelineResponseObject={timelineResponseObject}
            paginationObject={paginationObject}
            timelineLoading={timelineLoading}
            handlePageNavigation={handlePageNavigation}
            toggleBookmark={toggleBookmark}
          />
        )}
      </div>
    </Layout>
  );
};

export default authGuardHOC(TimelinePage);
