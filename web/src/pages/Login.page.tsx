import { useEffect, useState } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { <PERSON><PERSON>, Button, Flex, PasswordInput, useMantineTheme } from '@mantine/core';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import { Icon } from '@/components/icon/Icon';
import { Input } from '@/components/Input/Input';
import UnauthenticatedLayout from '@/components/Layout/UnauthenticatedLayout';
import { Heading } from '@/components/Texts/Heading/Heading';
import { Text } from '@/components/Texts/Text/Text';
import { useAppContext } from '@/context/AppContext';

export function LoginPage() {
  const [showBadLoginError, setShowBadLoginError] = useState(false);
  const form = useForm({
    initialValues: {
      username: '',
      password: '',
    },
    validate: {
      username: (value) => (/^\S+@\S+$/.test(value) ? null : 'Please enter a valid email'),
      password: (value) => (value.length > 0 ? null : 'Password is required'),
    },
  });

  const { initialize, userInfo, initialized } = useAppContext();
  const truthKeepTheme = useMantineTheme().other.truthKeep;
  const textTheme = useMantineTheme().other.text;

  const navigate = useNavigate();
  const location = useLocation();

  const handleSubmit = async (values: { username: string; password: string }) => {
    const validationResult = form.validate();
    if (!validationResult.hasErrors) {
      try {
        const res = await fetch('/api/login', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(values),
        });

        if (res.status === 204) {
          initialize();
          const from = location.state?.from || '/timeline';
          navigate(from, { replace: true });
          return;
        }

        if (res.status === 401) {
          setShowBadLoginError(true);
        }
      } catch {
        notifications.show({
          title: 'Error Logging In',
          message: 'There was an error while logging you in',
          autoClose: 3000,
          color: 'red',
        });
      }
    }
  };

  useEffect(() => {
    if (initialized && userInfo) {
      navigate('/timeline');
    }
  }, [userInfo, initialized, navigate]);

  return (
    <UnauthenticatedLayout>
      <Flex
        direction="column"
        w="100%"
        h="100%"
        gap={12}
        className="px-[28px] pt-[56px] pb-[68px] md:p-[40px]"
        style={{ backgroundColor: truthKeepTheme.backgroundColor }}
      >
        <Flex h="72px" align="center" gap={12} display={{ base: 'none', sm: 'flex' }}>
          <img src="/icons/tkLogoSmall.svg" alt="logo" />
          <Heading tagLevel="3" className="font-normal">
            Truthkeep
          </Heading>
        </Flex>
        <Flex w="100%" h="100%" align="center" justify="center">
          <Flex direction="column" w={289} h="100%" justify="space-between">
            <form
              noValidate
              onSubmit={form.onSubmit(handleSubmit)}
              className="h-full flex items-center"
            >
              <Flex direction="column" w="100%">
                <Text size="xl" bold className="mb-[24px] text-center">
                  Login
                </Text>
                {showBadLoginError && (
                  <Alert variant="light" color="red" className="mb-[24px]">
                    Invalid username or password. Please try again.
                  </Alert>
                )}
                <Flex direction="column" gap={16} className="mb-[24px]">
                  <Input
                    type="email"
                    label="Email"
                    placeholder="Your email"
                    leftSection={<Icon iconCategory="input" iconType="email" />}
                    required
                    {...form.getInputProps('username')}
                  />
                  <PasswordInput
                    withAsterisk
                    label="Password"
                    placeholder="Password"
                    leftSection={<Icon iconCategory="input" iconType="lock" />}
                    {...form.getInputProps('password')}
                  />
                </Flex>
                <Flex
                  justify="space-between"
                  align={{ base: 'start', sm: 'center' }}
                  direction={{ base: 'column', sm: 'row' }}
                  gap={{ base: 24, sm: 0 }}
                >
                  <Text size="md" style={{ color: textTheme.secondary }}>
                    <Link to="/forgot-password">Forgot password?</Link>
                  </Text>
                  <Button
                    type="submit"
                    bg="violet"
                    className="!font-[700] !text-[16px] !h-[37px] !w-full md:!w-auto md:!h-[30px]"
                  >
                    Login
                  </Button>
                </Flex>
              </Flex>
            </form>
            <Text size="sm" className="text-center mt-[56px] md:mt-0">
              Don’t have an account? Speak to the Marketing Team for access to Truthkeep.
            </Text>
          </Flex>
        </Flex>
      </Flex>
    </UnauthenticatedLayout>
  );
}
