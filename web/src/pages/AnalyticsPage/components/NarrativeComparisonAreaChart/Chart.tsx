import { AreaChart, AreaChartProps } from '@mantine/charts';
import { Flex, Loader } from '@mantine/core';
import { Text } from '@/components/Texts/Text/Text';

type ChartProps = {
  loading: boolean;
  isMissingFilters: boolean;
  onPointClick?: (pointData: any) => void;
} & AreaChartProps;

export default function Chart({ loading, isMissingFilters, onPointClick, ...props }: ChartProps) {
  return (
    <>
      {loading ? (
        <Flex w="100%" mih={300} align="center" justify="center">
          <Loader color="violet" />
        </Flex>
      ) : isMissingFilters ? (
        <Flex w="100%" mih={300} align="center" justify="center">
          <Text size="lg">Select at least one topic</Text>
        </Flex>
      ) : (
        <>
          <AreaChart
            {...props}
            h={300}
            curveType="linear"
            tickLine="none"
            gridAxis="y"
            withLegend
            legendProps={{
              wrapperStyle: {
                display: 'flex',
                left: '0',
              },
            }}
            styles={{
              legend: {
                gap: 24,
              },
              legendItem: {
                padding: 0,
              },
            }}
            dotProps={{
              r: 5,
              style: { cursor: 'pointer' },
              onClick: onPointClick,
            }}
            activeDotProps={{
              r: 5,
              style: { pointerEvents: 'none' },
              onClick: onPointClick,
            }}
          />
        </>
      )}
    </>
  );
}
