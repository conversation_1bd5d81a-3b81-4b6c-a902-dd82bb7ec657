import { Box, Flex } from '@mantine/core';
import { Text } from '@/components/Texts/Text/Text';
import { getScoreColors } from '@/helpers/getScoreColors';
import { SubredditSourceAnalyticsProps } from '@/types/analyticsTypes';

interface SourceAnalyticsHistogramCardProps {
  sourceAnalyticsData: SubredditSourceAnalyticsProps;
}

const SourceAnalyticsHistogramCard = ({
  sourceAnalyticsData,
}: SourceAnalyticsHistogramCardProps) => {
  const { name, count, sentiment, sentimentDistribution = [] } = sourceAnalyticsData;
  const total = count || 1;

  const redditLink = `https://reddit.com/r/${name}`;

  return (
    <div className="grid grid-cols-12 gap-[16px] items-center">
      <a
        href={redditLink}
        target="_blank"
        rel="noopener noreferrer"
        className="text-[#228BE6] underline col-span-4 text-nowrap"
      >
        {`r/${name}`}
      </a>
      <Flex direction="row" gap={6} className="col-span-2">
        <img
          src="/icons/miscIcons/commentIcon.svg"
          alt="commentIcon"
          className="w-[15px] h-[15px]"
        />
        <Text size="md" bold>
          {count}
        </Text>
      </Flex>
      <Flex className="col-span-2" align="center" justify="center">
        <Text
          bold
          size="md"
          className={`${getScoreColors(Math.round(sentiment ?? -1))} flex w-[48px] h-[48px] rounded-full items-center justify-center text-white`}
        >
          {Math.round(sentiment)}
        </Text>
      </Flex>
      {sentimentDistribution.length === 0 ? (
        <Flex align="center" className="col-span-4">
          <Text size="md" bold className="text-center">
            Distribution not <br /> available
          </Text>
        </Flex>
      ) : (
        <Flex direction="row" align="end" gap={4} style={{ height: 55 }} className="col-span-4">
          {sentimentDistribution.map((val, i) => {
            const percentage = (val / total) * 100;
            return (
              <Box
                key={i}
                className="w-[12px] rounded-md"
                style={{
                  height: `${percentage}%`,
                  backgroundColor: `${i === 0 ? '#F03E3E' : i === 1 ? '#F1A79E' : i === 2 ? '#CBCBCB' : i === 3 ? '#95CEAB' : '#12B886'}`,
                }}
              />
            );
          })}
        </Flex>
      )}
    </div>
  );
};

export default SourceAnalyticsHistogramCard;
