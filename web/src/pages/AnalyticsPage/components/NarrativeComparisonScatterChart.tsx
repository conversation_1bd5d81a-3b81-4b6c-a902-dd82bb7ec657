import { useEffect, useMemo, useState } from 'react';
import { Scatter<PERSON><PERSON> } from '@mantine/charts';
import { Flex, Loader } from '@mantine/core';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import { analyticsService } from '@/api/analytics/analyticsService';
import { ChartTooltip } from '@/components/ChartToolTip';
import Filters from '@/components/Filters/Filters';
import NarrativeBreakdownModal from '@/components/NarrativeBreakdown/NarrativeBreakdownModal';
import HelpText from '@/components/Texts/HelpText/HelpText';
import { Text } from '@/components/Texts/Text/Text';
import { colorPalettes } from '@/consts';
import { useAppContext } from '@/context/AppContext';
import { sourceDictionary } from '@/dictionary/sourceDictionary';
import { getDateRangeParams } from '@/helpers/dateUtils';
import { AnalyticsRequestProps, TopicScatterPlotProps } from '@/types/analyticsTypes';
import { TeamTypeProps } from '@/types/teamType';
import { FilterForm } from '@/types/timelineTypes';
import { TopicProps } from '@/types/topicType';

interface NarrativeComparisonScatterChartProps {
  topicOptions: TopicProps[];
  groupOptions: TeamTypeProps[];
  narrativeAspectOptions: { value: string; label: string }[];
}

const NarrativeComparisonScatterChart = ({
  topicOptions,
  groupOptions,
  narrativeAspectOptions,
}: NarrativeComparisonScatterChartProps) => {
  const [loading, setLoading] = useState(false);
  const [scatterChartData, setScatterChartData] = useState<TopicScatterPlotProps[]>([]);
  const [selectedNarrativeId, setSelectedNarrativeId] = useState<number | null>(null);
  const [modalOpened, setModalOpened] = useState(false);

  const { userInfo } = useAppContext();

  const filterForm = useForm<FilterForm>({
    initialValues: {
      dateRange: null,
      multiSelectTopicIds: [],
      multiSelectNarrativeAspects: [],
      sentiments: [],
      sources: null,
      customDateRange: '',
      groups: [],
    },
  });

  useEffect(() => {
    if (narrativeAspectOptions.length > 0 && userInfo && groupOptions) {
      const groups = groupOptions.filter(
        (g: { id: number | undefined }) => g?.id === userInfo?.groups?.[0]?.id
      );
      const defaultNarrativeAspects = narrativeAspectOptions
        ?.slice(0, 3)
        ?.map(({ value }) => value);

      filterForm.setInitialValues({
        ...filterForm.getInitialValues(),
        multiSelectNarrativeAspects: [],
      });

      filterForm.setValues({
        multiSelectNarrativeAspects: defaultNarrativeAspects,
        groups,
      });
    }
  }, [narrativeAspectOptions, userInfo, groupOptions]);

  const fetchScatterChartData = async () => {
    try {
      setLoading(true);
      const { sentiments, sources, multiSelectTopicIds, multiSelectNarrativeAspects, groups } =
        filterForm.values;

      if (multiSelectNarrativeAspects?.length === 0) return;

      const { dateFrom, dateTo } = getDateRangeParams(filterForm.values);

      const params = {
        dateFrom,
        dateTo,
        topicIds: multiSelectTopicIds,
        sentiments: sentiments?.map((s) => s.id),
        sources: sources ? [sourceDictionary[sources as keyof typeof sourceDictionary]] : undefined,
        aspects: multiSelectNarrativeAspects,
        groupIds: groups?.map((g) => g.id),
      } as AnalyticsRequestProps;

      const response = await analyticsService.getNarrativeScatterPlot(params);
      setScatterChartData(response as TopicScatterPlotProps[]);
    } catch (error) {
      notifications.show({
        title: 'Error fetching timeline',
        message: 'There was an error fetching timeline',
        autoClose: 3000,
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchScatterChartData();
  }, [filterForm.values]);

  const series = useMemo(() => {
    const map: Record<string, { x: number; y: number; narrativeId: number }[]> = {};
    for (const { narrativeAspect, count, sentiment, narrativeId } of scatterChartData) {
      if (!map[narrativeAspect]) {
        map[narrativeAspect] = [];
      }
      map[narrativeAspect].push({ x: count, y: sentiment, narrativeId });
    }
    return Object.entries(map).map(([aspect, data], index) => ({
      name: aspect
        .replace(/_/g, ' ')
        .toLowerCase()
        .replace(/^./g, (char) => char.toUpperCase()),
      data,
      color: colorPalettes[index],
    }));
  }, [scatterChartData]);

  const handlePointClick = (event: any) => {
    const narrativeId = event?.payload?.narrativeId;
    if (narrativeId) {
      setSelectedNarrativeId(narrativeId);
      setModalOpened(true);
    }
  };

  const missingNarrativeFilter = filterForm.values.multiSelectNarrativeAspects?.length === 0;

  return (
    <Flex direction="column" gap={18}>
      <NarrativeBreakdownModal
        opened={modalOpened}
        onClose={() => {
          setModalOpened(false);
          setSelectedNarrativeId(null);
        }}
        narrativeId={selectedNarrativeId!}
        filterForm={filterForm}
      />
      <div className="mt-8">
        <Text size="2lg" bold>
          Narrative & Sentiment Comparison
        </Text>
        <HelpText
          text="Compare sentiment across different topics and narratives to understand public perception. Use the filters to compare different sources or filter across a custom date range."
          modalPage="analytics"
        />
      </div>
      <Filters
        filterForm={filterForm}
        topicOptions={topicOptions}
        groupOptions={groupOptions}
        narrativeAspectOptions={narrativeAspectOptions}
      />
      {loading ? (
        <Flex w="100%" mih={300} align="center" justify="center">
          <Loader color="violet" />
        </Flex>
      ) : missingNarrativeFilter ? (
        <Flex w="100%" mih={350} align="center" justify="center">
          <Text size="lg">Select at least one narrative</Text>
        </Flex>
      ) : (
        <ScatterChart
          h={350}
          data={series}
          dataKey={{ x: 'x', y: 'y' }}
          xAxisLabel="Number of mentions"
          yAxisLabel="Sentiment"
          tooltipProps={{
            content: ({ payload }) => <ChartTooltip payload={payload} filterForm={filterForm} />,
          }}
          scatterProps={{
            onClick: handlePointClick,
            cursor: 'pointer',
          }}
          withLegend
        />
      )}
    </Flex>
  );
};

export default NarrativeComparisonScatterChart;
