import { Flex } from '@mantine/core';
import { Text } from '@/components/Texts/Text/Text';
import { getCompactNumber } from '@/helpers/numbers';
import { TimelineProps } from '@/types/timelineTypes';
import { COLOR_MAP } from '../../../../TimelinePage/constants';

interface OverviewCardsProps {
  timeline: TimelineProps;
}

const OverviewCards = ({ timeline }: OverviewCardsProps) => {
  const sourceType = timeline.type;

  return (
    <Flex
      className="bg-[#F1F3F5] px-[20px] py-[16px] rounded-[4px] border-0 border-t-[4px] border h-full max-h-48"
      direction="column"
      gap={16}
      justify="space-between"
      style={{
        borderTopColor: COLOR_MAP[sourceType].border,
      }}
    >
      <Text size="lg" className="first-letter:uppercase line-clamp-3">
        {timeline.summary}
      </Text>
      <Flex direction="column" gap={8}>
        <Text className="text-ellipsis line-clamp-1">
          <a
            href={timeline.externalUrl}
            target="_blank"
            rel="noreferrer"
            className="text-blue-500 hover:underline"
          >
            {timeline.title}
          </a>
        </Text>
        <Flex direction="row" justify="space-between" align="center" w="100%">
          <Flex direction="row" gap={16} align="center">
            <Flex direction="row" gap={4}>
              <img
                src="/icons/timelineIcons/likeIcon.svg"
                alt="like icon"
                className="w-[20px] h-[20px]"
              />
              <Text size="lg" bold>
                {getCompactNumber((timeline as any)?.likeCount ?? timeline.voteScore ?? 0)}
              </Text>
            </Flex>
            <Flex direction="row" gap={4}>
              <img
                src="/icons/timelineIcons/commentIcon.svg"
                alt="comment icon"
                className="w-[20px] h-[20px]"
              />
              <Text size="lg" bold>
                {getCompactNumber(timeline.commentCount ?? 0)}
              </Text>
            </Flex>
          </Flex>
          {sourceType === 'YOUTUBE_VIDEO' ? (
            <img
              src="/icons/sourceIcons/youtubeIcon.svg"
              alt="youtube icon"
              className="w-[32px] h-[32px]"
            />
          ) : (
            <img
              src="/icons/sourceIcons/redditIcon.svg"
              alt="reddit icon"
              className="w-[32px] h-[32px]"
            />
          )}
        </Flex>
      </Flex>
    </Flex>
  );
};

export default OverviewCards;
