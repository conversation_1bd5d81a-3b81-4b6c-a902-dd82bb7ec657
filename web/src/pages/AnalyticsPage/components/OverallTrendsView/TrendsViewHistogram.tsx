import { Box, Flex } from '@mantine/core';
import { Text } from '@/components/Texts/Text/Text';
import { getScoreColors } from '@/helpers/getScoreColors';
import { TrendsHistogramAnalyticsItemProps } from '@/types/analyticsTypes';

interface TrendsViewHistogram {
  trendHistogramItem: TrendsHistogramAnalyticsItemProps;
}

const TrendsViewHistogram = ({ trendHistogramItem }: TrendsViewHistogram) => {
  const { count, sentiment, sentimentDistribution = [] } = trendHistogramItem;

  const total = count || 1;
  return (
    <Flex
      direction="row"
      px={9}
      py={6}
      className="bg-[#EBE9E5] rounded-lg  max-h-[66px] group-hover:shadow transition-shadow"
      gap={14}
      justify="space-between"
      h="100%"
    >
      <Flex direction="column" justify="space-between">
        <Flex>
          <Text
            bold
            size="md"
            className={`${getScoreColors(Math.round(sentiment ?? -1))} flex min-w-[24px] min-h-[24px] rounded-full items-center justify-center text-white`}
            component="data"
          >
            {Math.round(sentiment)}
          </Text>
        </Flex>
        <Flex direction="row" gap={6}>
          <img src="/icons/miscIcons/commentIcon.svg" alt="commentIcon" />
          <Text size="md" bold className="whitespace-nowrap" component="data">
            {count}
          </Text>
        </Flex>
      </Flex>

      {sentimentDistribution.length === 0 ? (
        <Flex align="center">
          <Text size="md" bold className="text-center">
            Distribution not <br /> available
          </Text>
        </Flex>
      ) : (
        <Flex direction="row" align="end" gap={4} style={{ height: 55 }} mt="auto">
          {sentimentDistribution.map((val, i) => {
            const percentage = (val / total) * 100;
            return (
              <Box
                key={i}
                className="w-[12px] rounded-md"
                style={{
                  height: `${percentage}%`,
                  backgroundColor: `${i === 0 ? '#F03E3E' : i === 1 ? '#F1A79E' : i === 2 ? '#CBCBCB' : i === 3 ? '#95CEAB' : '#12B886'}`,
                }}
              />
            );
          })}
        </Flex>
      )}
    </Flex>
  );
};

export default TrendsViewHistogram;
