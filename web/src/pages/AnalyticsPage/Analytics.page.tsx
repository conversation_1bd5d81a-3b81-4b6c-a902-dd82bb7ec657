import { Accordion, AccordionControl, Flex } from '@mantine/core';
import { authGuardHOC } from '@/components/authGuardHOC';
import { Layout } from '@/components/Layout/Layout';
import { Heading } from '@/components/Texts/Heading/Heading';
import HelpText from '@/components/Texts/HelpText/HelpText';
import useNarratives from '@/hooks/useNarratives';
import NarrativeComparisonAreaChart from './components/NarrativeComparisonAreaChart/NarrativeComparisonAreaChart';
import NarrativeComparisonScatterChart from './components/NarrativeComparisonScatterChart';
import OverallTrendsView from './components/OverallTrendsView/OverallTrendsView';
import SourceAnalytics from './components/SourceAnalytics/SourceAnalytics';
import { useAnalytics } from './useAnalytics';

const AnalyticsPage = () => {
  const { topicOptions, groupOptions } = useAnalytics();
  const { narrativeAspects } = useNarratives();

  return (
    <Layout>
      <Flex direction="column" w="100%" maw="1280px" h="100%" className="py-[24px] md:py-[32px]">
        <Heading tagLevel="2" className="px-[12px] md:px-[40px]">
          Analytics
        </Heading>
        <HelpText
          text="The Analytics page provides comprehensive insights and trends from your data. You can explore narrative trends over time, identify key themes, analyze source distribution, and compare sentiment and narratives across different topics. These tools help you understand patterns and make data-driven decisions."
          modalPage="analytics"
          className="px-[12px] md:px-[40px] mt-2"
        />
        <Accordion
          chevron={
            <img
              src="/icons/timelineIcons/timelineAccordionIcon.svg"
              alt="timeline accordion icon"
              className="transition-transform duration-30"
            />
          }
          chevronPosition="left"
          w="100%"
          className="md:px-[24px]"
          chevronSize={28}
          multiple
          defaultValue={['overview', 'trends', 'source-analytics']}
        >
          <Accordion.Item value="overview" style={{ borderBottom: '3px solid #DEE2E6' }}>
            <AccordionControl>
              <Heading tagLevel="3" className="py-[20px]">
                Overview
              </Heading>
            </AccordionControl>
            <Accordion.Panel>
              <OverallTrendsView />
            </Accordion.Panel>
          </Accordion.Item>
          <Accordion.Item value="trends" style={{ borderBottom: '3px solid #DEE2E6' }}>
            <AccordionControl>
              <Heading tagLevel="3" className="py-[20px]">
                Trends
              </Heading>
            </AccordionControl>
            <Accordion.Panel>
              <NarrativeComparisonAreaChart
                topicOptions={topicOptions}
                groupOptions={groupOptions}
              />
              <NarrativeComparisonScatterChart
                topicOptions={topicOptions}
                groupOptions={groupOptions}
                narrativeAspectOptions={narrativeAspects}
              />
            </Accordion.Panel>
          </Accordion.Item>
          <Accordion.Item value="source-analytics" style={{ borderBottom: 'none' }}>
            <AccordionControl>
              <Heading tagLevel="3" className="py-[20px]">
                Source Analytics
              </Heading>
            </AccordionControl>
            <Accordion.Panel>
              <SourceAnalytics />
            </Accordion.Panel>
          </Accordion.Item>
        </Accordion>
      </Flex>
    </Layout>
  );
};

export default authGuardHOC(AnalyticsPage);
