import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Loader } from '@mantine/core';
import { useForm } from '@mantine/form';
import Button from '@/components/Button/Button';
import { Input } from '@/components/Input/Input';
import { Text } from '@/components/Texts/Text/Text';

interface VerifyProps {
  setStep: (step: number) => void;
  handleUserInformation: (key: string, value: string) => void;
  username: string;
}

const Verify = ({ setStep, handleUserInformation, username }: VerifyProps) => {
  const [loading, setLoading] = useState(false);
  const [showVerifyError, setShowVerifyError] = useState(false);
  const form = useForm({
    mode: 'uncontrolled',
    initialValues: {
      code: '',
    },
    validate: {
      code: (value) =>
        /^[A-Za-z0-9]{4}-[A-Za-z0-9]{4}$/.test(value) ? null : 'Invalid code format',
    },
  });

  const onSubmit = async ({ code }: { code: string }) => {
    setLoading(true);
    try {
      const response = await fetch(`/api/forgot-password?username=${username}&code=${code}`);
      if (response.status === 200) {
        setStep(2);
        const data = await response.json();
        handleUserInformation('code', code);
        handleUserInformation('name', `${data.firstName} ${data.lastName}`);
      } else {
        setShowVerifyError(true);
      }
    } catch (error) {
      setShowVerifyError(true);
    } finally {
      setLoading(false);
    }
  };

  return (
    <form noValidate onSubmit={form.onSubmit(onSubmit)} className="h-full flex items-center">
      {loading ? (
        <Flex justify="center" align="center" className="h-full w-full">
          <Loader size="lg" color="violet" />
        </Flex>
      ) : (
        <Flex direction="column" w="100%">
          <Text size="xl" bold className="mb-[24px] text-center">
            Verification Code
          </Text>

          <Text size="lg" className="mb-[24px]">
            Enter the verification code sent to {username}
          </Text>
          {showVerifyError && (
            <Alert variant="light" color="red" className="mb-[24px]">
              Invalid verification code, please try again.
            </Alert>
          )}
          <Flex direction="column" gap={16} className="mb-[24px]">
            <Input
              type="text"
              label="Verification Code"
              placeholder="XXXX-XXXX"
              required
              {...form.getInputProps('code')}
            />
          </Flex>
          <Flex justify="space-between" align="center">
            <Button type="button" size="auto" onClick={() => setStep(0)} variant="secondary">
              Back
            </Button>
            <Button type="submit" size="auto">
              Verify
            </Button>
          </Flex>
        </Flex>
      )}
    </form>
  );
};

export default Verify;
