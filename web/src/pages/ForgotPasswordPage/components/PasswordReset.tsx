import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Alert, Flex, Input, Loader, PasswordInput } from '@mantine/core';
import { useForm } from '@mantine/form';
import { authService } from '@/api/auth/authService';
import Button from '@/components/Button/Button';
import { Icon } from '@/components/icon/Icon';
import { Text } from '@/components/Texts/Text/Text';

interface PasswordSetUpProps {
  setStep: (step: number) => void;
  userInformation: {
    username: string;
    name: string;
    code: string;
  };
}

const PasswordReset = ({ setStep, userInformation }: PasswordSetUpProps) => {
  const [loading, setLoading] = useState(false);
  const [showPasswordResetError, setShowPasswordResetError] = useState({
    show: false,
    message: '',
  });
  const { username, name, code } = userInformation;

  const navigate = useNavigate();

  const form = useForm<{ password: string; confirmPassword: string }>({
    initialValues: {
      password: '',
      confirmPassword: '',
    },
    validate: {
      password: (value, values) =>
        value.length === 0
          ? 'Password is required'
          : value !== values.confirmPassword
            ? 'Passwords do not match'
            : null,
      confirmPassword: (value, values) =>
        value.length === 0
          ? 'Confirm Password is required'
          : value !== values.password
            ? 'Passwords do not match'
            : null,
    },
  });

  const onSubmit = async ({ password }: { password: string }) => {
    setLoading(true);
    try {
      const response = await authService.resetPassword(username, code, password);
      if (response.status === 201) {
        navigate('/login');
      } else if (response.status === 400) {
        setShowPasswordResetError({
          show: true,
          message: 'Password is not strong enough. Please try again.',
        });
      }
    } catch (error) {
      setShowPasswordResetError({
        show: true,
        message: 'An error occurred while resetting the password. Please try again.',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <form noValidate onSubmit={form.onSubmit(onSubmit)} className="h-full flex items-center">
      {loading ? (
        <Flex justify="center" align="center" className="h-full w-full">
          <Loader size="lg" color="violet" />
        </Flex>
      ) : (
        <Flex direction="column" w="100%">
          <Text size="xl" bold className="mb-[24px] text-center">
            Change Your Password
          </Text>
          {showPasswordResetError.show && (
            <Alert variant="light" color="red" className="mb-[24px]">
              {showPasswordResetError.message}
            </Alert>
          )}
          <Flex direction="column" gap={16} className="mb-[24px]">
            <Input.Wrapper label="Name">
              <Input
                type="name"
                leftSection={<Icon iconCategory="input" iconType="email" />}
                disabled
                defaultValue={name}
              />
            </Input.Wrapper>
            <Input.Wrapper label="Email">
              <Input
                type="email"
                leftSection={<Icon iconCategory="input" iconType="email" />}
                disabled
                defaultValue={username}
              />
            </Input.Wrapper>

            <PasswordInput
              withAsterisk
              label="New Password"
              placeholder="Password"
              leftSection={<Icon iconCategory="input" iconType="lock" />}
              {...form.getInputProps('password')}
            />
            <PasswordInput
              withAsterisk
              label="Confirm Password"
              placeholder="Password"
              leftSection={<Icon iconCategory="input" iconType="lock" />}
              {...form.getInputProps('confirmPassword')}
            />
          </Flex>
          <Flex justify="space-between" align="center">
            <Button type="button" size="auto" onClick={() => setStep(1)}>
              Back
            </Button>
            <Button type="submit" size="auto">
              Reset
            </Button>
          </Flex>
        </Flex>
      )}
    </form>
  );
};

export default PasswordReset;
