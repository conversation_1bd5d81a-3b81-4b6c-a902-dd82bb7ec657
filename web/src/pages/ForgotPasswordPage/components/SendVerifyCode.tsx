import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Loader } from '@mantine/core';
import { useForm } from '@mantine/form';
import Button from '@/components/Button/Button';
import { Icon } from '@/components/icon/Icon';
import { Input } from '@/components/Input/Input';
import { Text } from '@/components/Texts/Text/Text';

interface SendVerifyCodeProps {
  setStep: (step: number) => void;
  handleUserInformation: (key: string, value: string) => void;
}

const SendVerifyCode = ({ setStep, handleUserInformation }: SendVerifyCodeProps) => {
  const [showError, setShowError] = useState(false);
  const [loading, setLoading] = useState(false);

  const form = useForm({
    mode: 'uncontrolled',
    initialValues: {
      username: '',
    },
    validate: {
      username: (value) => (/^\S+@\S+$/.test(value) ? null : 'Invalid email'),
    },
  });

  const onSubmit = async ({ username }: { username: string }) => {
    setLoading(true);
    try {
      const body = JSON.stringify({ username });
      const response = await fetch('/api/forgot-password', {
        method: 'POST',
        body,
        headers: { 'Content-Type': 'application/json' },
      });

      if (response.status === 201) {
        setStep(1);
        handleUserInformation('username', username);
      }
    } catch (error) {
      setShowError(true);
    } finally {
      setLoading(false);
    }
  };

  return (
    <form noValidate onSubmit={form.onSubmit(onSubmit)} className="h-full flex items-center">
      {loading ? (
        <Flex justify="center" align="center" className="h-full w-full">
          <Loader size="lg" color="violet" />
        </Flex>
      ) : (
        <Flex direction="column" w="100%">
          <Text size="xl" bold className="mb-[24px] text-center">
            Forgot Password
          </Text>
          {showError && (
            <Alert variant="light" color="red" className="mb-[24px]">
              An error occurred while sending the verification code to this email. Please try again.
            </Alert>
          )}
          <Flex direction="column" gap={16} className="mb-[24px]">
            <Input
              type="email"
              label="Username or Email"
              placeholder="Your Username or Email"
              leftSection={<Icon iconCategory="input" iconType="email" />}
              required
              {...form.getInputProps('username')}
            />
          </Flex>
          <Button type="submit" size="auto">
            Next
          </Button>
        </Flex>
      )}
    </form>
  );
};

export default SendVerifyCode;
