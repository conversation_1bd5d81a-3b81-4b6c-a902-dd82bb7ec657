import { useEffect, useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Flex, useMantineTheme } from '@mantine/core';
import UnauthenticatedLayout from '@/components/Layout/UnauthenticatedLayout';
import { Heading } from '@/components/Texts/Heading/Heading';
import { Text } from '@/components/Texts/Text/Text';
import { useAppContext } from '@/context/AppContext';
import PasswordReset from './components/PasswordReset';
import SendVerifyCode from './components/SendVerifyCode';
import Verify from './components/Verify';

const enum ForgotPasswordStep {
  SendCode = 0,
  Verify = 1,
  Reset = 2,
}

export function ForgotPasswordPage() {
  const [step, setStep] = useState<ForgotPasswordStep>(ForgotPasswordStep.SendCode);

  const [userInformation, setUserInformation] = useState({
    username: '',
    name: '',
    code: '',
  });

  const handleUserInformation = (key: string, value: string) => {
    setUserInformation((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  const { userInfo, initialized } = useAppContext();
  const truthKeepTheme = useMantineTheme().other.truthKeep;
  const navigate = useNavigate();

  useEffect(() => {
    if (initialized && userInfo) {
      navigate('/timeline');
    }
  }, [userInfo, initialized]);

  const steps: React.ReactNode[] = [
    <SendVerifyCode setStep={setStep} handleUserInformation={handleUserInformation} />,
    <Verify
      setStep={setStep}
      handleUserInformation={handleUserInformation}
      username={userInformation.username}
    />,
    <PasswordReset setStep={setStep} userInformation={userInformation} />,
  ];

  return (
    <UnauthenticatedLayout>
      <Flex
        direction="column"
        w="100%"
        h="100%"
        gap={12}
        className="p-[40px]"
        style={{ backgroundColor: truthKeepTheme.backgroundColor }}
      >
        <Flex h="72px" align="center" gap={12} display={{ base: 'none', sm: 'flex' }}>
          <Link to="/" style={{ display: 'flex', alignItems: 'center' }}>
            <img src="/icons/tkLogoSmall.svg" alt="logo" />
          </Link>
          <Link to="/" style={{ textDecoration: 'none' }}>
            <Heading tagLevel="3" className="font-normal">
              Truthkeep
            </Heading>
          </Link>
        </Flex>
        <Flex w="100%" h="100%" align="center" justify="center">
          <Flex direction="column" w={289} h="100%" justify="space-between">
            {steps[step] ?? steps[0]}
            <Text size="sm" className="text-center">
              Don't have an account? Speak to the Marketing Team for access to Truthkeep.
            </Text>
          </Flex>
        </Flex>
      </Flex>
    </UnauthenticatedLayout>
  );
}
