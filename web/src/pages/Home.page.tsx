import { Text } from '@mantine/core';
import { Layout } from '@/components/Layout/Layout';
import { useAppContext } from '@/context/AppContext';
import { authGuardHOC } from '../components/authGuardHOC';

function HomePage() {
  const { userInfo } = useAppContext();

  return (
    <Layout>
      <Text size="lg" mt="lg" ta="center">
        Welcome, {userInfo?.email}
      </Text>
    </Layout>
  );
}

export default authGuardHOC(HomePage);
