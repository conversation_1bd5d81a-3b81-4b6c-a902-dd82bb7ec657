import { Button, CheckIcon, Flex, Space } from '@mantine/core';
import { authService } from '@/api/auth/authService';
import { Layout } from '@/components/Layout/Layout';
import { Heading } from '@/components/Texts/Heading/Heading';
import { useAppContext } from '@/context/AppContext';

export default function ChangeTenantPage() {
  const { userInfo } = useAppContext();

  const handleTenantChange = async (tenantId: string) => {
    await authService.changeTenant(tenantId);
    window.location.reload();
  };

  return (
    <Layout>
      <Flex
        direction="column"
        align="start"
        w="100%"
        maw="1280px"
        h="100%"
        className="py-[24px] px-[12px] md:py-[32px] md:px-[40px]"
      >
        <Heading tagLevel="2">Select Tenant</Heading>
        <Space h="md" />
        <Flex direction="column" gap="md" component="ul">
          {userInfo?.tenants.map((tenant) => (
            <li key={tenant.id}>
              <Button type="button" variant="outline" onClick={() => handleTenantChange(tenant.id)}>
                {tenant.name}
                {tenant.id === userInfo?.sessionTenantId && (
                  <CheckIcon className="ml-2" width={16} height={16} />
                )}
              </Button>
            </li>
          ))}
        </Flex>
      </Flex>
    </Layout>
  );
}
