import { Link, useParams } from 'react-router-dom';
import { Flex, LoadingOverlay, Text } from '@mantine/core';
import { authGuardHOC } from '@/components/authGuardHOC';
import { Layout } from '@/components/Layout/Layout';
import DisplayTimelines from '../TimelinePage/components/DisplayTimelines';
import { useTimeline } from '../TimelinePage/useTimeline';

function TimelineDetailPage() {
  const { id } = useParams();
  const { toggleBookmark, timelineResponseObject, loading } = useTimeline({
    analysisId: id,
  });
  const isNotFoundError = !loading && !timelineResponseObject?.items?.length;

  if (loading) {
    return <LoadingOverlay visible loaderProps={{ color: 'violet' }} />;
  }

  return (
    <Layout>
      <div className="flex flex-col max-w-[1280px] px-[12px] md:px-[40px]">
        {isNotFoundError ? (
          <Flex direction="column" justify="center" align="center" className="my-40">
            <Text>404: Post not found</Text>
            <br />
            <Text>
              <Link to="/" replace className="underline text-blue-500">
                Return to home
              </Link>
            </Text>
          </Flex>
        ) : (
          <div className="px-[16px] pb-[32px]">
            <DisplayTimelines
              timelineResponseObject={timelineResponseObject}
              openItems={id ? [id.toString()] : []}
              paginationObject={timelineResponseObject}
              handlePageNavigation={() => {}}
              toggleBookmark={toggleBookmark}
            />
          </div>
        )}
      </div>
    </Layout>
  );
}

export default authGuardHOC(TimelineDetailPage);
