import { Alert, Flex, Input, Loader, PasswordInput, useMantineTheme } from '@mantine/core';
import Button from '@/components/Button/Button';
import { Icon } from '@/components/icon/Icon';
import UnauthenticatedLayout from '@/components/Layout/UnauthenticatedLayout';
import { Heading } from '@/components/Texts/Heading/Heading';
import { Text } from '@/components/Texts/Text/Text';
import { useOnboarding } from './useOnboarding';

const OnboardingPage = () => {
  const truthKeepTheme = useMantineTheme().other.truthKeep;
  const {
    loading,
    onboardingInformation,
    onboardingError,
    form,

    onSubmit,
  } = useOnboarding();

  return (
    <UnauthenticatedLayout>
      <Flex
        direction="column"
        w="100%"
        h="100%"
        gap={12}
        className="p-[40px]"
        style={{ backgroundColor: truthKeepTheme.backgroundColor }}
      >
        <Flex h="72px" align="center" gap={12} display={{ base: 'none', sm: 'flex' }}>
          <img src="/icons/tkLogoSmall.svg" alt="logo" />
          <Heading tagLevel="3" className="font-normal">
            Truthkeep
          </Heading>
        </Flex>
        <form
          noValidate
          onSubmit={form.onSubmit(onSubmit)}
          className="h-full flex justify-center items-center"
        >
          <Flex direction="column" w={289} h="100%" justify="space-between">
            {loading ? (
              <Flex justify="center" align="center" className="h-full w-full">
                <Loader size="lg" color="violet" />
              </Flex>
            ) : (
              <Flex direction="column" w="100%" h="100%" justify="center">
                <Text size="xl" bold className="mb-[24px] text-center">
                  Account Setup
                </Text>
                {onboardingError.show && (
                  <Alert variant="light" color="red" className="mb-[24px]">
                    {onboardingError.message}
                  </Alert>
                )}
                <Flex direction="column" gap={16} className="mb-[24px]">
                  <Input.Wrapper label="Name">
                    <Input
                      type="name"
                      leftSection={<Icon iconCategory="input" iconType="email" />}
                      disabled
                      defaultValue={`${onboardingInformation.firstName} ${onboardingInformation.lastName}`}
                    />
                  </Input.Wrapper>
                  <Input.Wrapper label="Email">
                    <Input
                      type="email"
                      leftSection={<Icon iconCategory="input" iconType="email" />}
                      disabled
                      defaultValue={onboardingInformation.email}
                    />
                  </Input.Wrapper>

                  <PasswordInput
                    withAsterisk
                    label="New Password"
                    placeholder="Password"
                    leftSection={<Icon iconCategory="input" iconType="lock" />}
                    {...form.getInputProps('password')}
                  />
                  <PasswordInput
                    withAsterisk
                    label="Confirm Password"
                    placeholder="Password"
                    leftSection={<Icon iconCategory="input" iconType="lock" />}
                    {...form.getInputProps('confirmPassword')}
                  />
                </Flex>
                <Flex justify="center">
                  <Button type="submit" size="auto">
                    Join Truthkeep
                  </Button>
                </Flex>
              </Flex>
            )}
            <Text size="sm" className="text-center">
              Don’t have an account? Speak to the Marketing Team for access to Truthkeep.
            </Text>
          </Flex>
        </form>
      </Flex>
    </UnauthenticatedLayout>
  );
};

export default OnboardingPage;
