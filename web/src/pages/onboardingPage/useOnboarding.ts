import { useEffect, useMemo, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useForm } from '@mantine/form';
import { userService } from '@/api/user/userService';

interface OnboardingInformationProps {
  email: string;
  firstName: string;
  lastName: string;
  signupCode: string;
}

export const useOnboarding = () => {
  const [loading, setLoading] = useState(false);
  const [onboardingInformation, setOnboardingInformation] = useState<OnboardingInformationProps>(
    {} as OnboardingInformationProps
  );
  const [onboardingError, setOnboardingError] = useState({
    show: false,
    message: '',
  });

  const navigate = useNavigate();
  const location = useLocation();

  const query = useMemo(() => new URLSearchParams(location.search), [location.search]);

  const form = useForm<{ password: string; confirmPassword: string }>({
    initialValues: {
      password: '',
      confirmPassword: '',
    },
    validate: {
      password: (value, values) =>
        value.length === 0
          ? 'Password is required'
          : value !== values.confirmPassword
            ? 'Passwords do not match'
            : null,
      confirmPassword: (value, values) =>
        value.length === 0
          ? 'Confirm Password is required'
          : value !== values.password
            ? 'Passwords do not match'
            : null,
    },
  });

  const initialize = async () => {
    setLoading(true);
    try {
      const response = await userService.fetchUserOnboardingInformation(
        query.get('userId') as string,
        query.get('code') as string
      );
      if (response.status === 200) {
        const data = (await response.json()) as OnboardingInformationProps;
        setOnboardingInformation({
          email: data.email,
          firstName: data.firstName,
          lastName: data.lastName,
          signupCode: data.signupCode,
        });
      } else {
        navigate('/login');
      }
    } catch (error) {
      navigate('/login');
    } finally {
      setLoading(false);
    }
  };

  const onSubmit = async () => {
    setLoading(true);
    try {
      const response = await userService.completeUserOnboarding(
        query.get('userId') as string,
        query.get('code') as string,
        form.values.password
      );
      if (response.status === 204) {
        navigate('/login');
      } else if (response.status === 400) {
        setOnboardingError({
          show: true,
          message:
            'Password must be at least 8 characters and include a lowercase letter, an uppercase letter, a number, and a symbol.',
        });
      }
    } catch (error) {
      setOnboardingError({
        show: true,
        message: 'An error occurred while setting up the passwords. Please try again.',
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (query.get('userId') || query.get('code')) {
      initialize();
    }
  }, [query]);

  return {
    loading,
    onboardingInformation,
    onboardingError,
    form,

    onSubmit,
  };
};
