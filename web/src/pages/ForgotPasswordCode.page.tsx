import { useNavigate, useSearchParams } from 'react-router-dom';
import { Button, Flex, Paper, Text, TextInput, Title } from '@mantine/core';
import { useForm } from '@mantine/form';
import { useAppContext } from '@/context/AppContext';

export function ForgotPasswordCodePage() {
  const [searchParams] = useSearchParams();
  const username = searchParams.get('username');
  const { initialize } = useAppContext();

  const form = useForm({
    mode: 'uncontrolled',
    initialValues: {
      code: '',
    },
  });

  const navigate = useNavigate();
  const onSubmit = async ({ code }: { code: string }) => {
    const response = await fetch(`/api/forgot-password?username=${username}&code=${code}`);

    if (response.status === 201) {
      initialize();
      navigate('/timeline');
    }
  };

  return (
    <Flex align="center" justify="center" h="100%">
      <Paper withBorder shadow="md" p="md" radius="md" w="360">
        <Title mb="md">Password Reset</Title>
        <Text mb="lg">Enter the verification code sent to {username}</Text>
        <form onSubmit={form.onSubmit(onSubmit)}>
          <TextInput
            mb="sm"
            required
            withAsterisk
            label="Verification Code"
            placeholder="XXXX-XXXX"
            key={form.key('code')}
            maxLength={9}
            {...form.getInputProps('code')}
          />
          <Button type="submit" mx="auto" mt="lg" display="block">
            Verify
          </Button>
        </form>
      </Paper>
    </Flex>
  );
}
