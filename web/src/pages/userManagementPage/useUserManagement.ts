import { useEffect, useRef, useState } from 'react';
import { notifications } from '@mantine/notifications';
import { userService } from '@/api/user/userService';
import { ModalTypeId, PaginationObjectProps } from '@/types/globalTypes';
import { UserProps } from '@/types/userType';

export const useUserManagement = () => {
  const [loading, setLoading] = useState(true);

  const [users, setUsers] = useState<UserProps[]>([]);
  const initiallyFetchedUsers = useRef<UserProps[]>([]);

  const [selectedUsers, setSelectedUsers] = useState<UserProps[]>([]);
  const [selectedUserToEdit, setSelectedUserToEdit] = useState<UserProps | null>(null);

  const [showAdminsOnly, setShowAdminsOnly] = useState<boolean>(false);

  const [openedModalId, setOpenedModalId] = useState<ModalTypeId>('');

  const [paginationObject, setPaginationObject] = useState<PaginationObjectProps>({
    page: 1,
    size: 100,
  } as PaginationObjectProps);

  const initialize = async (adminOnly?: boolean) => {
    try {
      setLoading(true);
      const response = await userService.fetchUsers({
        page: 1,
        roles: adminOnly ? 'admin' : undefined,
      });
      setUsers(response.data.items as UserProps[]);

      setPaginationObject({
        page: response.data.page,
        size: response.data.size,
        total: response.data.total,
        totalPages: response.data.totalPages,
      });
      initiallyFetchedUsers.current = response.data.items as UserProps[];
    } catch (error) {
      notifications.show({
        title: 'Initialization Error',
        message: 'There was an error while initializing the page',
        autoClose: 3000,
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const response = await userService.fetchUsers({
        page: paginationObject.page,
        roles: showAdminsOnly ? 'admin' : undefined,
      });
      setUsers(response.data.items as UserProps[]);
      initiallyFetchedUsers.current = response.data.items as UserProps[];
    } catch (error) {
      notifications.show({
        title: 'Error fetching data',
        message: 'There was an error fetching users',
        autoClose: 3000,
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  const deleteSelectedUsers = async (selectedUsers: UserProps[]) => {
    try {
      await userService.deleteUsers(selectedUsers.map((user) => user.id));
      setSelectedUsers([]);
    } catch (error) {
      notifications.show({
        title: 'Error deleting users',
        message: 'There was an error while deleting users',
        autoClose: 3000,
        color: 'red',
      });
    }
  };

  const handlePageNavigation = (page: number) => {
    setPaginationObject({
      ...paginationObject,
      page,
    });
  };

  async function toggleUserAdmin(user: UserProps) {
    setUsers((prev: UserProps[]) =>
      prev.map((u: UserProps) =>
        u.id === user.id
          ? {
              ...u,
              isAdmin: !u.isAdmin,
            }
          : u
      )
    );

    try {
      await userService.editUser(user.id, {
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        isAdmin: !user.isAdmin,
        groups: user.groups.map((group) => group.id),
      });
      notifications.show({
        title: `Admin status successfully updated for ${user.firstName} ${user.lastName}`,
        message: '',
        color: 'teal',
        autoClose: 3000,
      });
    } catch (err) {
      setUsers((prev: UserProps[]) =>
        prev.map((u: UserProps) =>
          u.id === user.id
            ? {
                ...u,
                isAdmin: !u.isAdmin,
              }
            : u
        )
      );
      notifications.show({
        title: 'Failed to update the admin status',
        message: '',
        color: 'red',
        autoClose: 3000,
      });
    }
  }

  useEffect(() => {
    initialize();
  }, []);

  useEffect(() => {
    fetchUsers();
  }, [paginationObject.page]);

  return {
    users,
    selectedUsers,
    showAdminsOnly,
    paginationObject,
    openedModalId,
    selectedUserToEdit,
    loading,

    initialize,
    deleteSelectedUsers,
    setShowAdminsOnly,
    setSelectedUsers,
    handlePageNavigation,
    setOpenedModalId,
    setSelectedUserToEdit,
    toggleUserAdmin,
  };
};
