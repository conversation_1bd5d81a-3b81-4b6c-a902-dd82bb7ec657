import '@mantine/core/styles.css';
import './global.css';
import '@mantine/notifications/styles.css';
import '@mantine/charts/styles.css';

import { Notifications } from '@mantine/notifications';
import Chatbot from './components/Chatbot/Chatbot';
import { AppProvider } from './context/AppContext';
import { AppThemeProvider } from './context/AppThemeContext';
import { ChatbotProvider } from './context/ChatbotContext';
import { HelpModalProvider } from './context/HelpModalContext';
import { Router } from './Router';

export default function App() {
  return (
    <AppProvider>
      <ChatbotProvider>
        <AppThemeProvider>
          <HelpModalProvider>
            <Notifications />
            <Router />
            <Chatbot />
          </HelpModalProvider>
        </AppThemeProvider>
      </ChatbotProvider>
    </AppProvider>
  );
}
