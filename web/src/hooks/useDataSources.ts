import { useEffect, useState } from 'react';
import { datasourceService } from '@/api/sources/datasourceService';
import { SubredditSource, YoutubeChannelSource } from '@/types/datasourceTypes';

export function useSubredditSources() {
  const [isLoading, setIsLoading] = useState(true);
  const [subredditSources, setSubredditSources] = useState<SubredditSource[]>([]);

  useEffect(() => {
    setSubredditSources([]);
    setIsLoading(true);
    datasourceService
      .fetchRedditSources()
      .then((response) => {
        setSubredditSources(response);
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, []);

  return { subredditSources, isLoading };
}

export function useYoutubeSources() {
  const [isLoading, setIsLoading] = useState(true);
  const [youtubeSources, setYoutubeSources] = useState<YoutubeChannelSource[]>([]);

  useEffect(() => {
    setYoutubeSources([]);
    setIsLoading(true);
    datasourceService
      .fetchYoutubeSources()
      .then((response) => {
        setYoutubeSources(response);
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, []);

  return { youtubeSources, isLoading };
}
