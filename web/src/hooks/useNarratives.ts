import { useEffect, useState } from 'react';
import { narrativeService } from '@/api/narratives/narrativeService';

export default function useNarratives() {
  const [narrativeAspects, setNarrativeAspects] = useState<{ value: string; label: string }[]>([]);

  useEffect(() => {
    narrativeService.fetchNarrativeAspects().then((response) => {
      setNarrativeAspects(response.items);
    });
  }, []);

  return { narrativeAspects };
}
