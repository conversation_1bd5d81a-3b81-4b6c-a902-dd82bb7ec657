import { SubredditSource, YoutubeChannelSource } from '@/types/datasourceTypes';
import apiClient from '../apiClient';

export const datasourceService = {
  fetchRedditSources: async (): Promise<SubredditSource[]> => {
    const response = await apiClient.get('/sources/reddit?size=100');
    return response.data.items;
  },

  fetchYoutubeSources: async (): Promise<YoutubeChannelSource[]> => {
    const response = await apiClient.get('/sources/youtube?size=100');
    return response.data.items;
  },
};
