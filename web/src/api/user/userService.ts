import { UserPayload } from '@/types/userType';
import apiClient from '../apiClient';

export const userService = {
  fetchUserProfile: async () => {
    return await apiClient.get('/users/me');
  },

  fetchUsers: async (params: { page?: number; roles?: string; size?: number } = {}) => {
    const pageSize = params.size ?? 100;
    return await apiClient.get('/users', { params: { ...params, size: pageSize } });
  },

  createUser: async (userData: UserPayload) => {
    return await apiClient.post('/users', userData);
  },

  editUser: async (userId: string, userData: UserPayload) => {
    return await apiClient.put(`/users/${userId}`, userData);
  },

  deleteUsers: async (ids: string[]) => {
    return await apiClient.delete('/users', {
      data: { userIds: ids },
    });
  },

  fetchUserOnboardingInformation: async (id: string, code: string) => {
    const res = await fetch(`api/users/${id}/onboarding/${code}`, {
      method: 'GET',
      headers: { 'Content-Type': 'application/json' },
    });
    return res;
  },

  completeUserOnboarding: async (id: string, code: string, password: string) => {
    const res = await fetch(`api/users/${id}/onboarding/${code}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        password,
      }),
    });
    return res;
  },

  updatePassword: async (currentPassword: string, newPassword: string) => {
    return await apiClient.put('/users/me/password', {
      currentPassword,
      newPassword,
    });
  },
};
