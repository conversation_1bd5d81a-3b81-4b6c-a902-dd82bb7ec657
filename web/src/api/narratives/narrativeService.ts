import qs from 'qs';
import { AnalyticsRequestProps } from '@/types/analyticsTypes';
import {
  NarrativeAspectProps,
  NarrativeBreakdownProps,
  NarrativeProps,
} from '@/types/narrativeTypes';
import apiClient from '../apiClient';

export const narrativeService = {
  fetchNarrative: async (id: string): Promise<NarrativeProps> =>
    apiClient.get(`/narratives/${id}`).then((response) => response.data),

  fetchNarrativeAspects: async (): Promise<NarrativeAspectProps> =>
    apiClient.get('/narratives/aspects?size=100').then((response) => response.data),

  fetchNarrativeBreakdown: async (
    id: number,
    params?: AnalyticsRequestProps
  ): Promise<NarrativeBreakdownProps> =>
    apiClient
      .get(`/narratives/${id}/breakdown`, {
        params,
        paramsSerializer: (obj) => qs.stringify(obj, { arrayFormat: 'repeat' }),
      })
      .then((response) => response.data),
};
