import { PaginatedResponse } from '@/types/paginatedTypes';
import { Sources } from '@/types/timelineTypes';
import apiClient from '../apiClient';

export interface SearchRequest {
  query: string;
}

export interface SearchRequestQuery {
  page?: number;
  take?: number;
  sharedWithGroupIds?: number[];
  bookmarkedOnly?: boolean;
}

export interface SearchResult {
  id: string;
  source?: Sources;
  text: string;
  url: string;
  score: number;
  searchType: 'semantic' | 'keyword';
}

export const searchService = {
  search: async (
    query: string,
    options: SearchRequestQuery = {}
  ): Promise<PaginatedResponse<SearchResult>> => {
    const params = {
      page: options.page ?? 1,
      sharedWithGroupIds: options.sharedWithGroupIds?.length
        ? options.sharedWithGroupIds
        : undefined,
      bookmarkedOnly: options.bookmarkedOnly ?? undefined,
    };

    const response = await apiClient.post<PaginatedResponse<SearchResult>>(
      '/search',
      { query } as SearchRequest,
      { params }
    );

    return response.data;
  },
};
