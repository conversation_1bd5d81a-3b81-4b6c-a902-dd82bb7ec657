import axios, { AxiosResponse } from 'axios';
import { setupCache } from 'axios-cache-interceptor';

const axois = axios.create({
  baseURL: '/api',
  withCredentials: true,
});

const apiClient = setupCache(axois, {
  // Disables caching, but inflight requests are still deduplicated by axios-cache-interceptor
  ttl: 0,
});

let refreshPromise: Promise<AxiosResponse<any, any>> | null = null;

const refreshAuthToken = async () => {
  return apiClient.post('/refresh');
};

apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    if (originalRequest.url?.includes('/refresh')) {
      return Promise.reject(error);
    }

    if (error.response?.status !== 401) {
      return Promise.reject(error);
    }
    if (!refreshPromise) {
      refreshPromise = refreshAuthToken().finally(() => {
        refreshPromise = null;
      });
    }

    try {
      await refreshPromise;
      return apiClient(originalRequest);
    } catch (refreshError) {
      window.location.href = '/login';
    }
  }
);

export default apiClient;
