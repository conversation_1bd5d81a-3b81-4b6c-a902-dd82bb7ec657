import apiClient from '../apiClient';

export const authService = {
  login: async (username: string, password: string) => {
    await apiClient.post('/login', { username, password });
  },

  logout: async () => {
    await apiClient.post('/logout').catch((error) => {
      // Ignore 401 error for already logged out user trying to log out again
      if (error.response?.status !== 401) {
        throw error;
      }
    });
  },

  refreshToken: async () => {
    await apiClient.post('/refresh');
  },

  resetPassword: async (email: string, code: string, password: string) => {
    const res = await fetch('/api/set-password', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email, code, password }),
    });
    return res;
  },

  changeTenant: async (tenantId: string) => {
    await apiClient.put('/session-tenant', { tenantId });
  },
};
