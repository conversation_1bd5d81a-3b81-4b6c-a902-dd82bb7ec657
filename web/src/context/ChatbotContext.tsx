import React, { createContext, Dispatch, SetStateAction, useContext, useState } from 'react';
import { TimelineProps } from '@/types/timelineTypes';

interface ChatbotContextType {
  timelinesToBeSent: TimelineProps[];
  chatOpen: boolean;
  chatIsMaximized?: boolean;
  setChatOpen?: Dispatch<SetStateAction<boolean>>;
  setTimelinesToBeSent: Dispatch<SetStateAction<TimelineProps[]>>;
  setChatIsMaximized?: Dispatch<SetStateAction<boolean>>;
  expandChat?: () => void;
}

const ChatbotContext = createContext<ChatbotContextType>({
  timelinesToBeSent: [],
  chatOpen: false,
  chatIsMaximized: false,
  setChatOpen: () => {},
  setTimelinesToBeSent: () => {},
  setChatIsMaximized: () => {},
  expandChat: () => {},
});

export const useChatbotContext = () => useContext(ChatbotContext);

export const ChatbotProvider = ({ children }: { children: React.ReactNode }) => {
  const [chatOpen, setChatOpen] = useState(false);
  const [chatIsMaximized, setChatIsMaximized] = useState(false);

  const [timelinesToBeSent, setTimelinesToBeSent] = useState<TimelineProps[]>(
    [] as TimelineProps[]
  );

  const expandChat = () => {
    setChatOpen(true);
    setChatIsMaximized(true);
  };

  return (
    <ChatbotContext.Provider
      value={{
        timelinesToBeSent,
        chatOpen,
        chatIsMaximized,
        setChatOpen,
        setTimelinesToBeSent,
        setChatIsMaximized,
        expandChat,
      }}
    >
      {children}
    </ChatbotContext.Provider>
  );
};
