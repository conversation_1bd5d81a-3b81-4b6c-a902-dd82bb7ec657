import React, { createContext, useContext } from 'react';
import { MantineProvider, MantineThemeOverride } from '@mantine/core';
import { useLocalStorage } from '@mantine/hooks';
import { defaultTheme } from '../themes/defaultTheme/defaultTheme';

type ThemeName = 'default';

interface AppThemeContextType {
  themeName: ThemeName;
  setThemeName: (name: ThemeName) => void;
}

const AppThemeContext = createContext<AppThemeContextType>({
  themeName: 'default',
  setThemeName: () => {},
});

export const useThemeContext = () => useContext(AppThemeContext);

export const AppThemeProvider = ({ children }: { children: React.ReactNode }) => {
  const [themeName, setThemeName] = useLocalStorage<'default'>({
    key: 'custom-theme',
    defaultValue: 'default',
  });

  // we can add more themes.
  const activeTheme: MantineThemeOverride = themeName === 'default' ? defaultTheme : defaultTheme;

  return (
    <AppThemeContext.Provider value={{ themeName, setThemeName }}>
      <MantineProvider theme={activeTheme}>{children}</MantineProvider>
    </AppThemeContext.Provider>
  );
};
