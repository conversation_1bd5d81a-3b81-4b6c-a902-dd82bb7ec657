import { createContext, ReactNode, useContext, useState } from 'react';

interface HelpModalContextType {
  isOpen: boolean;
  activeTab: string;
  openHelpModal: (tab?: string) => void;
  closeHelpModal: () => void;
}

const HelpModalContext = createContext<HelpModalContextType | undefined>(undefined);

export const useHelpModal = () => {
  const context = useContext(HelpModalContext);
  if (!context) {
    throw new Error('useHelpModal must be used within a HelpModalProvider');
  }
  return context;
};

interface HelpModalProviderProps {
  children: ReactNode;
}

export const HelpModalProvider = ({ children }: HelpModalProviderProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('getting-started');

  const openHelpModal = (tab: string = 'getting-started') => {
    setActiveTab(tab);
    setIsOpen(true);
  };

  const closeHelpModal = () => {
    setIsOpen(false);
  };

  return (
    <HelpModalContext.Provider
      value={{
        isOpen,
        activeTab,
        openHelpModal,
        closeHelpModal,
      }}
    >
      {children}
    </HelpModalContext.Provider>
  );
};
