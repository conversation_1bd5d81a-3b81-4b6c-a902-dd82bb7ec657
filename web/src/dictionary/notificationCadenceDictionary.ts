import { type NotificationCadence } from '@/types/topicType';

export const labelToCadence: Record<string, NotificationCadence> = {
  Weekly: 'WEEKLY',
  Daily: 'DAILY',
  Monthly: 'MONTHLY',
  Off: 'OFF',
};

export const cadenceToLabel: Record<NotificationCadence, string> = {
  WEEKLY: 'Weekly',
  DAILY: 'Daily',
  MONTHLY: 'Monthly',
  OFF: 'Off',
};

export const NOTIFICATION_PREFERENCE_OPTIONS: { label: string; value: NotificationCadence }[] = [
  { label: 'Off', value: 'OFF' },
  { label: 'Daily', value: 'DAILY' },
  { label: 'Weekly', value: 'WEEKLY' },
  { label: 'Monthly', value: 'MONTHLY' },
];
