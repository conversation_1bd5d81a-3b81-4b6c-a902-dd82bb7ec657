import { Sentiment, Sources } from './timelineTypes';

export interface TrendsHistogramAnalyticsItemProps {
  id: number;
  name?: string;
  summary?: string;
  count: number;
  sentiment: number;
  sentimentDistribution: number[];
}

export interface SubredditSourceAnalyticsProps {
  count: number;
  name: string;
  sentiment: number;
  sentimentDistribution: number[];
}

export interface ScatterPlotToolTipProps {
  id: number;
  summary: string;
  narrativeAspect: string;
  topicId: number;
  topicName: string;
  count: number;
  sentiment: number;
  sentimentDistribution: number[];
}

export interface TopicScatterPlotProps {
  narrativeId: number;
  summary: string;
  narrativeAspect:
    | 'UNKNOWN'
    | 'LEARNING_CURVE'
    | 'EASE_OF_DEBUGGING'
    | 'EASE_OF_USE'
    | 'EASE_OF_INTEGRATION'
    | 'DOCUMENTATION_QUALITY'
    | 'PRICING'
    | 'PERFORMANCE'
    | 'CUSTOMER_SUPPORT'
    | 'BUSINESS_PRACTICES'
    | 'FEATURE_REQUEST'
    | 'BUG';
  topicId: number;
  topicName: string;
  sentiment: number;
  count: number;
}

export interface AnalyticsRequestProps {
  dateFrom?: string;
  dateTo?: string;
  sentiments?: Sentiment[];
  topicIds?: number[];
  sources?: Sources[];
  narrativeIds?: number[];
  aspects?: string[];
}

export interface NarrativeAspectTrendsComparisonProps {
  topics: string[];
  data: Array<{ date: string } & Record<string, number>>;
}
