import { Sources } from './timelineTypes';

export interface ChatMessage {
  type: Sources | 'text';
  content: string;
}

export interface ChatResponseMessage extends ChatMessage {
  id: string;
}

export interface LocalChatMessages extends ChatMessage {
  source: 'user' | 'botResponse';
}

export interface ChatRequestDto {
  previousResponseId?: string;
  messages: ChatMessage[];
}

export interface ChatResponseDto {
  messages: ChatResponseMessage[];
}
