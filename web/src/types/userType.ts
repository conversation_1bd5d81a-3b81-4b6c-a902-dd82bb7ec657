import { TeamTypeProps } from './teamType';

interface BaseUserProps {
  email: string;
  firstName: string;
  lastName: string;
  isAdmin: boolean;
}

export interface UserFormProps extends BaseUserProps {
  groups: TeamTypeProps[];
}
export interface UserPayload extends BaseUserProps {
  groups: number[];
}

export interface UserProps extends BaseUserProps {
  id: string;
  groups: TeamTypeProps[];
}
