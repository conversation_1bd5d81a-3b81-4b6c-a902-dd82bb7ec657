import { PillThemeProps } from '@/themes/types/componentProps/PillThemeProps';

export const defaultPillTheme = {
  secondary: {
    root: {
      backgroundColor: 'white',
      border: '1px solid #868E96',
      padding: '8px 16px',
      height: 'auto',
    },
  },
  groupPill: {
    root: {
      backgroundColor: 'white',
      border: '1px solid #7950F2',
      padding: '4px 0px 4px 8px',
      height: 'auto',
    },
  },
  topicPill: {
    root: {
      backgroundColor: '#222E50',
      color: 'white',
      height: '26px',
    },
    label: {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
    },
  },
  tagPill: {
    root: {
      backgroundColor: '#0ca678',
      color: 'white',
      height: '26px',
    },
    label: {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
    },
  },
  sentimentPill: {
    positive: {
      backgroundColor: '#12B886',
      color: 'white',
    },
    negative: {
      backgroundColor: '#FA5252',
      color: 'white',
    },
    unknown: {
      backgroundColor: '#868E96',
      color: 'white',
    },
  },
} as PillThemeProps;
