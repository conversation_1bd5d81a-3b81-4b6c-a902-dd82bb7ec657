import { ButtonThemeProps } from '@/themes/types/componentProps/buttonThemeProps';

export const defaultButtonTheme = {
  primary: {
    '--button-bg': '#7048E8',
    '--button-color': '#fff',
    '--button-hover': '#6741D9',
  },
  secondary: {
    '--button-bg': 'transparent',
    '--button-color': '#495057',
    '--button-bd': '1px solid #868E96',
    '--button-hover-color': '#212529',
    '--button-hover': 'transparent',
  },
  danger: {
    '--button-bg': '#FA5252',
    '--button-color': '#fff',
    '--button-hover': '#F03E3E',
  },
  secondaryDanger: {
    '--button-bg': '#fff',
    '--button-color': '#FA5252',
    '--button-bd': '1px solid #FA5252',
    '--button-hover-color': '#FF8787',
    '--button-color-hover': '#FF8787',
    '--button-hover-bd': '#FF8787',
    '--button-hover': '#fff',
  },
  boarderless: {
    backgroundColor: 'transparent',
    color: '#7950F2',
    border: 'none',
  },
} as ButtonThemeProps;
