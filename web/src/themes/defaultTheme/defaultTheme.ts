import { MantineThemeOverride } from '@mantine/core';
import { ThemeProps } from '../types/themeProps';
import { defaultButtonTheme } from './defaultComponentTheme/defaultButtonTheme';
import { defaultPillTheme } from './defaultComponentTheme/defaultPillTheme';
import { defaultSwitchTheme } from './defaultComponentTheme/defaultSwitchTheme';
import { defaultTableTheme } from './defaultComponentTheme/defaultTableTheme';
import { defaultTextTheme } from './defaultComponentTheme/defaultTextTheme';
import { defaultTruthKeepTheme } from './defaultComponentTheme/defaultTruthKeepTheme';

export const defaultTheme: MantineThemeOverride = {
  fontFamily: 'DM Sans, sans-serif',
  components: {
    InputWrapper: {
      styles: {
        label: {
          fontWeight: 700,
          fontSize: '14px',
          lineHeight: '16px',
        },
      },
    },
    Pagination: {
      styles: {
        root: {
          '--pagination-active-bg': '#7048E8',
          '--pagination-active-color': 'white',
        } as React.CSSProperties,
      },
    },
    MantinePillsInput: {
      styles: {
        label: {
          fontWeight: 700,
          fontSize: '14px',
          lineHeight: '16px',
        },
      },
    },
    Switch: {
      styles: {
        root: {
          cursor: 'pointer',
        },
        track: {
          cursor: 'pointer',
        },
        thumb: {
          cursor: 'pointer',
        },
      },
    },
    Checkbox: {
      styles: {
        root: { cursor: 'pointer' },
        input: { cursor: 'pointer' },
        label: { cursor: 'pointer' },
      },
    },
  },
  other: {
    truthKeep: defaultTruthKeepTheme,
    button: defaultButtonTheme,
    switch: defaultSwitchTheme,
    text: defaultTextTheme,
    table: defaultTableTheme,
    pill: defaultPillTheme,
  },
} as ThemeProps;
