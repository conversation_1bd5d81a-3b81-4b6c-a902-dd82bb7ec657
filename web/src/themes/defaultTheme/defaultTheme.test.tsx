import { defaultTheme } from '@/themes/defaultTheme/defaultTheme';
import { ThemeProps } from '@/themes/types/themeProps';

const themes: { name: string; theme: ThemeProps }[] = [
  { name: 'defaultTheme', theme: defaultTheme as ThemeProps },
];

describe('Truthkeep application color themes for all themes are structurally valid', () => {
  themes.forEach(({ name, theme }) => {
    describe(name, () => {
      const truthKeepTheme = theme.other.truthKeep;

      it('has all required application color themes', () => {
        expect(truthKeepTheme.backgroundColor).toBeDefined();
        expect(typeof truthKeepTheme.backgroundColor).toBe('string');

        expect(truthKeepTheme.secondaryBackgroundColor).toBeDefined();
        expect(typeof truthKeepTheme.secondaryBackgroundColor).toBe('string');

        expect(truthKeepTheme.textColor).toBeDefined();
        expect(typeof truthKeepTheme.textColor).toBe('string');
      });
    });
  });
});

describe('Button themes for all themes are structurally valid', () => {
  const expectedKeys: Record<string, string[]> = {
    primary: ['--button-bg', '--button-color', '--button-hover'],
    secondary: [
      '--button-bg',
      '--button-color',
      '--button-bd',
      '--button-hover-color',
      '--button-hover',
    ],
    danger: ['--button-bg', '--button-color', '--button-hover'],
    secondaryDanger: [
      '--button-bg',
      '--button-color',
      '--button-bd',
      '--button-hover-color',
      '--button-color-hover',
      '--button-hover-bd',
      '--button-hover',
    ],
    boarderless: ['backgroundColor', 'color', 'border'],
  };

  themes.forEach(({ name, theme }) => {
    describe(`${name} theme`, () => {
      const buttonTheme = theme.other.button;

      it('defines all variants', () => {
        Object.keys(expectedKeys).forEach((variant) => {
          expect(buttonTheme).toHaveProperty(variant);
        });
      });

      Object.entries(expectedKeys).forEach(([variant, keys]) => {
        it(`"${variant}" has expected keys`, () => {
          const styles = (buttonTheme as any)[variant]!;

          keys.forEach((key) => {
            expect(styles[key]).toBeDefined();
            expect(typeof styles[key]).toBe('string');
          });
        });
      });
    });
  });
});

describe('Switch themes for all themes are structurally valid', () => {
  themes.forEach(({ name, theme }) => {
    describe(name, () => {
      const switchTheme = theme.other.switch;

      it('has primary backgroundColor and color', () => {
        expect(switchTheme.backgroundColor).toBeDefined();
        expect(typeof switchTheme.backgroundColor).toBe('string');
      });
    });
  });
});

describe('text theme for all themes are structurally valid', () => {
  themes.forEach(({ name, theme }) => {
    describe(name, () => {
      const textTheme = theme.other.text;

      it('has all required text color themes', () => {
        expect(textTheme.primary).toBeDefined();
        expect(typeof textTheme.primary).toBe('string');

        expect(textTheme.secondary).toBeDefined();
        expect(typeof textTheme.secondary).toBe('string');
      });
    });
  });
});

describe('table theme for all themes are structurally valid', () => {
  themes.forEach(({ name, theme }) => {
    describe(name, () => {
      const tableTheme = theme.other.table;

      it('has all required table color themes', () => {
        expect(tableTheme.selectedRowBackgroundColor).toBeDefined();
        expect(typeof tableTheme.selectedRowBackgroundColor).toBe('string');
      });
    });
  });
});

describe('pill theme for all themes are structurally valid', () => {
  themes.forEach(({ name, theme }) => {
    describe(name, () => {
      const pillTheme = theme.other.pill;

      it('has all required secondary type pill themes', () => {
        expect(pillTheme.secondary.root.backgroundColor).toBeDefined();
        expect(typeof pillTheme.secondary.root.backgroundColor).toBe('string');

        expect(pillTheme.secondary.root.border).toBeDefined();
        expect(typeof pillTheme.secondary.root.border).toBe('string');

        expect(pillTheme.secondary.root.padding).toBeDefined();
        expect(typeof pillTheme.secondary.root.padding).toBe('string');

        expect(pillTheme.secondary.root.height).toBeDefined();
        expect(typeof pillTheme.secondary.root.height).toBe('string');
      });

      it('has all required team type pill themes', () => {
        expect(pillTheme.secondary.root.backgroundColor).toBeDefined();
        expect(typeof pillTheme.secondary.root.backgroundColor).toBe('string');

        expect(pillTheme.secondary.root.border).toBeDefined();
        expect(typeof pillTheme.secondary.root.border).toBe('string');

        expect(pillTheme.secondary.root.padding).toBeDefined();
        expect(typeof pillTheme.secondary.root.padding).toBe('string');

        expect(pillTheme.secondary.root.height).toBeDefined();
        expect(typeof pillTheme.secondary.root.height).toBe('string');
      });
    });
  });
});
