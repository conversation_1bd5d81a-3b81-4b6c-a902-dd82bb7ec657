import { ButtonThemeProps } from './componentProps/buttonThemeProps';
import { PillThemeProps } from './componentProps/PillThemeProps';
import { SwitchThemeProps } from './componentProps/switchThemeProps';
import { TableThemeProps } from './componentProps/tableThemeProps';
import { TextThemeProps } from './componentProps/textThemeProps';
import { TurthKeepThemeProps } from './componentProps/truthKeepThemeProps';

export interface ThemeProps {
  other: {
    truthKeep: TurthKeepThemeProps;
    button: ButtonThemeProps;
    switch: SwitchThemeProps;
    text: TextThemeProps;
    table: TableThemeProps;
    pill: PillThemeProps;
  };
}
