export interface ButtonThemeProps {
  primary: {
    '--button-bg': string;
    '--button-color': string;
    '--button-hover': string;
  };
  secondary: {
    '--button-bg': string;
    '--button-color': string;
    '--button-bd': string;
    '--button-hover-color': string;
    '--button-hover': string;
  };
  danger: {
    '--button-bg': string;
    '--button-color': string;
    '--button-hover': string;
  };
  secondaryDanger: {
    '--button-bg': string;
    '--button-color': string;
    '--button-bd': string;
    '--button-hover-color': string;
    '--button-color-hover': string;
    '--button-hover-bd': string;
    '--button-hover': string;
  };
  boarderless: {
    backgroundColor: string;
    color: string;
    border: string;
  };
}
