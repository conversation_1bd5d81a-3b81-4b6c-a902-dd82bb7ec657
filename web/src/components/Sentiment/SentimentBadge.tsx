import { getScoreColors } from '@/helpers/getScoreColors';
import { Text } from '../Texts/Text/Text';

type SentimentBadgeProps = {
  className?: string;
  sentiment: number;
  size: 'xs' | 'sm' | 'md' | 'lg' | '2lg' | 'xl';
};

export default function SentimentBadge({ sentiment, size, className }: SentimentBadgeProps) {
  return (
    <div
      className={`${getScoreColors(sentiment)} aspect-square rounded-full items-center justify-center text-white cursor-pointer whitespace-nowrap inline-flex ${className}`}
    >
      <Text size={size} bold>
        {sentiment}
      </Text>
    </div>
  );
}
