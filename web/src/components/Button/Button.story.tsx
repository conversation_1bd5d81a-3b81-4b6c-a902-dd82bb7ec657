import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import Button from './Button';

const meta: Meta<typeof Button> = {
  title: 'Components/Button',
  component: Button,
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: { type: 'select' },
      options: ['primary', 'secondary', 'danger', 'secondaryDanger'],
    },
    size: {
      control: { type: 'select' },
      options: ['full', 'auto'],
      description: 'Controls the width of the button',
    },
    onClick: { action: 'clicked' },
    className: {
      control: 'text',
      description: 'Optional custom Tailwind classname for styling',
    },
  },
};

export default meta;
type Story = StoryObj<typeof Button>;

export const Primary: Story = {
  args: {
    children: 'Primary Button',
    variant: 'primary',
    size: 'full',
  },
};

export const Secondary: Story = {
  args: {
    children: 'Secondary Button',
    variant: 'secondary',
    size: 'full',
  },
};

export const Danger: Story = {
  args: {
    children: 'Danger Button',
    variant: 'danger',
    size: 'full',
  },
};

export const SecondaryDanger: Story = {
  args: {
    children: 'Secondary Danger',
    variant: 'secondaryDanger',
    size: 'full',
  },
};

export const AutoWidth: Story = {
  args: {
    children: 'Auto Width',
    variant: 'primary',
    size: 'auto',
  },
};

export const CustomClass: Story = {
  args: {
    children: 'Custom Styled',
    className: 'bg-green-500 text-white px-4 py-2 rounded',
    size: 'auto',
  },
};
