import { Flex } from '@mantine/core';
import { TeamTypeProps } from '@/types/teamType';
import { Text } from '../Texts/Text/Text';
import Pill from './Pill';

type GroupPillProps = {
  group: TeamTypeProps;
  withRemoveButton?: boolean;
  remove?: (id: string | number) => void;
};

const GroupPill = ({ group, withRemoveButton, remove }: GroupPillProps) => {
  return (
    <Pill
      withRemoveButton={withRemoveButton}
      variant="groupPill"
      onRemove={() => {
        if (remove) {
          remove(group.id);
        }
      }}
    >
      <Flex direction="row" align="center" h="100%">
        <img src="/icons/pills/pillUserIcon.svg" alt="User Icon" className="mr-[10.5px]" />
        <Text size="sm" className="mr-[10.5px]">
          {group.name}
        </Text>
      </Flex>
    </Pill>
  );
};

export default GroupPill;
