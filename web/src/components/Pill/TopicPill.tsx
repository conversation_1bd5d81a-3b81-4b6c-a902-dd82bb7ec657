import { Pill, PillProps, useMantineTheme } from '@mantine/core';
import SentimentBadge from '../Sentiment/SentimentBadge';
import { Text } from '../Texts/Text/Text';

type TopicPillProps = PillProps & {
  topic: {
    name: string;
    sentiment: number;
  };
};

const TopicPill = ({ topic, ...props }: TopicPillProps) => {
  const theme = useMantineTheme();

  const name = topic?.name ?? '';
  const sentiment = (topic?.sentiment ?? -1) === -1 ? 50 : topic?.sentiment;

  return (
    <Pill
      hidden={!name}
      styles={{
        ...theme.other.pill.topicPill,
        root: {
          ...theme.other.pill.topicPill.root,
          paddingLeft: '0',
        },
      }}
      title={name}
      {...props}
    >
      <SentimentBadge sentiment={sentiment} size="xs" className="mx-1 h-5" />
      <Text component="span" size="md" className="max-w-64 truncate">
        {name}
      </Text>
    </Pill>
  );
};

export default TopicPill;
