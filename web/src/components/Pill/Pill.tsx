import { Pill as MantinePill, useMantineTheme } from '@mantine/core';

type CustomVariant = 'groupPill' | 'secondary';

type PillsProps = {
  withRemoveButton?: boolean;
  children: React.ReactNode;
  variant?: CustomVariant;
  className?: string;
  onRemove?: () => void;
};

const Pill = ({ withRemoveButton = true, children, className, variant, onRemove }: PillsProps) => {
  const pillTheme = useMantineTheme().other.pill;
  const isCustom = !!className;

  return (
    <MantinePill
      unstyled={isCustom}
      className={className}
      withRemoveButton={withRemoveButton}
      onRemove={onRemove}
      styles={variant ? pillTheme[variant] : {}}
    >
      {children}
    </MantinePill>
  );
};

export default Pill;
