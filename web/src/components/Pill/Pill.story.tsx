import type { Meta, StoryObj } from '@storybook/react';
import Pill from './Pill';

const meta: Meta<typeof Pill> = {
  title: 'Components/Pill',
  component: Pill,
  tags: ['autodocs'],
  argTypes: {
    withRemoveButton: {
      control: 'boolean',
      description: 'Toggle the display of the remove button on the pill.',
    },
    variant: {
      control: { type: 'select' },
      options: ['groupPill', 'secondary'],
      description: 'Custom variant styles to apply to the pill.',
    },
    className: {
      control: 'text',
      description: 'Optional custom Tailwind CSS class names for styling.',
    },
    onRemove: {
      action: 'removed',
      description: 'Callback triggered when the remove button is clicked.',
    },
  },
};

export default meta;
type Story = StoryObj<typeof Pill>;

export const Default: Story = {
  args: {
    children: 'Default Pill',
    variant: 'groupPill',
    withRemoveButton: true,
  },
};

export const Secondary: Story = {
  args: {
    children: 'Secondary Pill',
    variant: 'secondary',
    withRemoveButton: true,
  },
};

export const NoRemoveButton: Story = {
  args: {
    children: 'No Remove Button',
    variant: 'groupPill',
    withRemoveButton: false,
  },
};

export const CustomStyled: Story = {
  args: {
    children: 'Custom Styled Pill',
    className: 'bg-blue-500 text-white px-4 py-2 rounded',
    withRemoveButton: false,
  },
};
