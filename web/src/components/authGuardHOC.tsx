import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { LoadingOverlay } from '@mantine/core';
import { useAppContext } from '@/context/AppContext';

export function authGuardHOC<P extends object>(
  Component: React.ComponentType<P>,
  adminProtected = false
): React.FC<P> {
  return (props: P) => {
    const { initialized, userInfo } = useAppContext();
    const location = useLocation();

    if (!initialized) {
      return <LoadingOverlay visible loaderProps={{ color: 'violet' }} />;
    }

    if (!userInfo) {
      return <Navigate to="/login" replace state={{ from: location.pathname }} />;
    }

    if (adminProtected && !userInfo.isAdmin) {
      return <Navigate to="/timeline" replace />;
    }

    return <Component {...props} />;
  };
}
