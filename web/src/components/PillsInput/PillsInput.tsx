import { ReactNode, useEffect, useMemo, useState } from 'react';
import { <PERSON><PERSON>, Pill as Man<PERSON>Pill, PillsInput as MantinePillsInput } from '@mantine/core';
import Button from '../Button/Button';
import { Input } from '../Input/Input';

export interface HasIdName {
  id: string | number;
  name: string;
}

type RenderPillFunction<T extends HasIdName> = (
  data: T,
  withRemoveButton?: boolean,
  remove?: (id: string | number) => void
) => ReactNode;

interface PillsInputProps<T extends HasIdName> {
  label: string;
  renderPill: RenderPillFunction<T>;
  withRemoveButton?: boolean;
  options?: T[];
  selectedData?: T[];
  onChange: (data: T) => void;
  remove?: (id: string | number) => void;
}

const PillsInput = <T extends HasIdName>({
  label,
  renderPill,
  withRemoveButton = true,
  options = [],
  selectedData = [],
  onChange,
  remove,
}: PillsInputProps<T>) => {
  const [currentOptions, setCurrentOptions] = useState<T[]>(options);
  useEffect(() => {
    setCurrentOptions(options);
  }, [options]);

  const [openDropdown, setOpenDropdown] = useState(false);

  const optionNames = useMemo(() => currentOptions.map((option) => option.name), [currentOptions]);

  const handleRemove = (id: string | number) => {
    if (remove) {
      remove(id);
    }
    const removedOption = options.find((option) => option.id === id);
    if (removedOption) {
      setCurrentOptions((prev) => {
        if (prev.find((option) => option.id === id)) {
          return prev;
        }
        return [...prev, removedOption];
      });
    }
  };

  return (
    <Flex direction="column" gap={12}>
      <MantinePillsInput label={label}>
        <MantinePill.Group>
          {selectedData.map((data) => renderPill(data, withRemoveButton, handleRemove))}
          <Button className="cursor-pointer" onClick={() => setOpenDropdown((prev) => !prev)}>
            <img
              src="/icons/miscIcons/addIcon.svg"
              alt="Add Icon"
              className="w-[24px] h-[24px] cursor-pointer"
            />
          </Button>
        </MantinePill.Group>
      </MantinePillsInput>
      {openDropdown && (
        <Input
          type="dropdown"
          data={optionNames}
          onChange={(value) => {
            if (typeof value === 'string') {
              const selectedOption = currentOptions.find((opt) => opt.name === value);
              if (selectedOption) {
                onChange(selectedOption);
                setCurrentOptions((prev) => prev.filter((opt) => opt.id !== selectedOption.id));
              }
            }
          }}
        />
      )}
    </Flex>
  );
};

export default PillsInput;
