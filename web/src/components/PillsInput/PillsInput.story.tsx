import { useState } from 'react';
import type { Meta, StoryObj } from '@storybook/react';
import PillsInput, { HasIdName } from './PillsInput';

const sampleOptions: HasIdName[] = [
  { id: '1', name: 'Team A' },
  { id: '2', name: 'Team B' },
  { id: '3', name: 'Team C' },
];

const meta: Meta<typeof PillsInput> = {
  title: 'Components/PillsInput',
  component: PillsInput,
  tags: ['autodocs'],
  argTypes: {
    label: {
      control: 'text',
      description: 'The label for the Pill Input component.',
    },
    withRemoveButton: {
      control: 'boolean',
      description: 'Toggle whether the remove button is shown on each rendered pill.',
    },
  },
};

export default meta;

const Template = (args: any) => {
  const [selected, setSelected] = useState<HasIdName[]>([]);
  return (
    <PillsInput
      {...args}
      options={sampleOptions}
      selectedData={selected}
      onChange={(data: HasIdName) => {
        setSelected([...selected, data]);
      }}
      remove={(id: string | number) => setSelected((prev) => prev.filter((item) => item.id !== id))}
      renderPill={(data, withRemove, remove) => (
        <div
          key={data.id}
          style={{
            display: 'inline-flex',
            alignItems: 'center',
            border: '1px solid #ccc',
            borderRadius: '4px',
            padding: '4px 8px',
            marginRight: '4px',
            backgroundColor: '#f7f7f7',
          }}
        >
          {data.name}
          {withRemove && (
            <button
              onClick={() => remove && remove(data.id)}
              style={{
                marginLeft: '8px',
                background: 'none',
                border: 'none',
                cursor: 'pointer',
              }}
              type="button"
            >
              x
            </button>
          )}
        </div>
      )}
    />
  );
};

export const Default: StoryObj<typeof PillsInput> = {
  render: Template,
  args: {
    label: 'Select a Team',
    withRemoveButton: true,
  },
};
