import React from 'react';
import { Flex } from '@mantine/core';
import Button from '@/components/Button/Button';
import GenericModal from '@/components/Modal/GenericModal';
import Pill from '@/components/Pill/Pill';
import { Text } from '@/components/Texts/Text/Text';
import { UserProps } from '@/types/userType';

const AdminConfirmationModal = ({
  user,
  opened,
  toggleUserAdmin,
  onClose,
}: {
  user: UserProps;
  opened: boolean;
  toggleUserAdmin: (user: UserProps) => void;
  onClose: () => void;
}) => {
  return (
    <GenericModal title="Admin Status Update" opened={opened}>
      <Flex direction="column" justify="center" align="center" p={20} gap={24}>
        <Text size="xl" className="text-center">
          Are you sure you want to change this user's admin status?
        </Text>
        <Pill variant="secondary" withRemoveButton={false}>
          <Text size="lg" bold>
            {user?.firstName} {user?.lastName}
          </Text>
        </Pill>
        <div className="flex flex-row gap-[8px] w-full">
          <Button
            onClick={() => {
              onClose();
            }}
            variant="secondary"
          >
            Cancel
          </Button>
          <Button
            onClick={() => {
              toggleUserAdmin(user);
              onClose();
            }}
            variant="primary"
          >
            Change
          </Button>
        </div>
      </Flex>
    </GenericModal>
  );
};

export default AdminConfirmationModal;
