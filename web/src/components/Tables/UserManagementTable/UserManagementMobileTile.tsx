import { Checkbox, Flex, useMantineTheme } from '@mantine/core';
import Button from '@/components/Button/Button';
import GroupPill from '@/components/Pill/GroupPill';
import Switch from '@/components/Switch/Switch';
import { Text } from '@/components/Texts/Text/Text';
import { UserProps } from '@/types/userType';

type UserManagementMobileTileProps = {
  users: UserProps[];
  selectedUsers: UserProps[];
  setSelectedUsers: (selectedRows: UserProps[]) => void;
  setSelectedUserToEdit: (user: UserProps | null) => void;
  toggleUserAdmin: (user: UserProps) => void;
};

const UserManagementMobileTile = ({
  users,
  selectedUsers,
  setSelectedUsers,
  setSelectedUserToEdit,
  toggleUserAdmin,
}: UserManagementMobileTileProps) => {
  const tableTheme = useMantineTheme().other.table;

  return (
    <div className="block md:hidden">
      {users.map((user) => {
        return (
          <Flex
            direction="column"
            p={15}
            gap={28}
            key={user.id}
            className="border-b border-b-[#E0E0E0]"
            bg={
              selectedUsers.some((selectedUser) => selectedUser.id === user.id)
                ? `${tableTheme.selectedRowBackgroundColor}`
                : undefined
            }
          >
            <Flex direction="row" justify="space-between" align="start" w="100%" gap={8}>
              <Flex direction="row" gap={16} align="start">
                <Checkbox
                  aria-label="Select row"
                  checked={selectedUsers.some((selectedUser) => selectedUser.id === user.id)}
                  onChange={(event) =>
                    setSelectedUsers(
                      event.currentTarget.checked
                        ? [...selectedUsers, user]
                        : selectedUsers.filter((selectedUser) => selectedUser.id !== user.id)
                    )
                  }
                  size="xs"
                  color="violet"
                />
                <Text size="md" bold>
                  {user.firstName} {user.lastName}
                </Text>
              </Flex>
              <Button
                className="flex items-center cursor-pointer min-w-[19px] min-h-[19px]"
                onClick={() => setSelectedUserToEdit(user)}
              >
                <img src="/icons/miscIcons/editIcon.svg" alt="edit Icon" />
              </Button>
            </Flex>
            <Text size="md">{user.email}</Text>
            <Flex direction="row" gap={8} wrap="wrap" align="center">
              {user.groups.length > 0 ? (
                <>
                  {user.groups.map((group) => {
                    return <GroupPill key={group.id} group={group} />;
                  })}
                </>
              ) : (
                <Text size="md" className="text-[#868E96] italic">
                  Click the edit icon to add teams
                </Text>
              )}
              <Button
                className="w-[24px] h-[24px] cursor-pointer"
                onClick={() => {
                  setSelectedUserToEdit(user);
                }}
              >
                <img src="/icons/miscIcons/addIcon.svg" alt="Add Icon" />
              </Button>
            </Flex>
            <Flex direction="row" gap={8} align="center">
              <Text size="md">Admin</Text>
              <Switch
                onChange={() => {
                  toggleUserAdmin(user);
                }}
                checked={user.isAdmin}
              />
            </Flex>
          </Flex>
        );
      })}
    </div>
  );
};

export default UserManagementMobileTile;
