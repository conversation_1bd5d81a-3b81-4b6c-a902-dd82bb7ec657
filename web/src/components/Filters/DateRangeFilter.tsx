import { useEffect, useMemo, useState } from 'react';
import { Flex, Select } from '@mantine/core';
import { DatePicker } from '@mantine/dates';
import { UseFormReturnType } from '@mantine/form';
import { customDateRangeConverter } from '@/helpers/dateUtils';
import Button from '../Button/Button';
import GenericModal from '../Modal/GenericModal';
import classes from './DateRange.module.css';

export function DateRangeFilter({
  filterForm,
}: {
  filterForm: UseFormReturnType<Record<string, any>>;
}) {
  const [modalOpen, setModalOpen] = useState(false);
  const [tempRange, setTempRange] = useState<[string | null, string | null]>([null, null]);

  const formattedRange = useMemo(() => {
    return filterForm?.values?.dateRange === 'custom'
      ? tempRange?.[0] && tempRange?.[1]
        ? `${tempRange[0]} to ${tempRange[1]}`
        : 'Pick a range…'
      : 'Pick a range...';
  }, [tempRange, filterForm?.values?.dateRange]);

  const applyRange = () => {
    const [from, to] = tempRange;
    if (from && to) {
      filterForm.setValues({
        ...filterForm.values,
        dateRange: 'custom',
        customDateRange: `${from} & ${to}`,
      });
      setModalOpen(false);
    }
  };

  useEffect(() => {
    if (filterForm?.values?.dateRange === 'custom' && filterForm?.values?.customDateRange) {
      const { dateFrom, dateTo } = customDateRangeConverter(filterForm?.values?.customDateRange);
      setTempRange([dateFrom || null, dateTo || null]);
    }
  }, [filterForm?.values?.dateRange]);

  return (
    <>
      <Select
        label="Date Range"
        placeholder="Select a date range"
        data={[
          { value: 'Last week', label: 'Last week' },
          { value: 'Last month', label: 'Last month' },
          { value: 'Last 6 months', label: 'Last 6 months' },
          { value: 'Last year', label: 'Last year' },
          { value: 'All time', label: 'All time' },
          { value: 'custom', label: formattedRange },
        ]}
        {...filterForm.getInputProps('dateRange')}
        onChange={(val) => {
          if (val === 'custom') {
            setModalOpen(true);
          } else {
            filterForm.setFieldValue('dateRange', val);
            filterForm.setFieldValue('customDateRange', '');
            setTempRange([null, null]);
          }
        }}
        styles={{
          input: {
            backgroundColor: '#F1F3F5',
            borderColor: '#F1F3F5',
          },
        }}
      />

      <GenericModal
        opened={modalOpen}
        onClose={() => setModalOpen(false)}
        title="Pick a date range"
        size="lg"
      >
        <Flex w="100%" py={40} align="center" justify="center">
          <DatePicker
            type="range"
            value={tempRange}
            onChange={(r) => setTempRange(r as [string | null, string | null])}
            allowSingleDateInRange
            maxDate={new Date()}
            c="violet"
            size="md"
            classNames={{ day: classes.day }}
          />
        </Flex>

        <Flex gap={8}>
          <Button
            variant="secondary"
            onClick={() => {
              setModalOpen(false);
            }}
          >
            Cancel
          </Button>
          <Button
            variant="primary"
            type="submit"
            disabled={!(Boolean(tempRange?.[0]) && Boolean(tempRange?.[1]))}
            onClick={() => {
              applyRange();
            }}
          >
            Save
          </Button>
        </Flex>
      </GenericModal>
    </>
  );
}
