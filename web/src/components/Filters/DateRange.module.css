.day[data-in-range] {
  background: var(--mantine-color-violet-1) !important;
  color: var(--mantine-color-text) !important;
}

.day[data-first-in-range],
.day[data-last-in-range],
.day[data-selected] {
  background: var(--mantine-color-violet-6) !important;
  color: var(--mantine-color-white) !important;
}

.day[data-first-in-range] {
  border-top-left-radius: 999px;
  border-bottom-left-radius: 999px;
}

.day[data-last-in-range] {
  border-top-right-radius: 999px;
  border-bottom-right-radius: 999px;
}