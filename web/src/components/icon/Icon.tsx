import { iconDictionary } from './iconDictionary';

type IconMapper = typeof iconDictionary;
type IconCategory = keyof IconMapper;
type IconType<C extends IconCategory> = keyof IconMapper[C];
type SvgProps = {
  width: string;
  height: string;
  viewBox: string;
  path: string;
};

type IconProps<C extends IconCategory = IconCategory, T extends IconType<C> = IconType<C>> = {
  iconCategory: C;
  iconType: T;
  className?: string;
};

export const Icon = <C extends IconCategory, T extends IconType<C>>({
  iconCategory,
  iconType,
  className = '',
}: IconProps<C, T>) => {
  const currentSvgProps = iconDictionary[iconCategory][iconType] as SvgProps;

  if (!currentSvgProps) {
    return null;
  }

  return (
    <svg
      width={currentSvgProps.width}
      height={currentSvgProps.height}
      viewBox={currentSvgProps.viewBox}
      className={`fill-current ${className}`}
    >
      <path fillRule="evenodd" clipRule="evenodd" d={currentSvgProps.path} />
    </svg>
  );
};
