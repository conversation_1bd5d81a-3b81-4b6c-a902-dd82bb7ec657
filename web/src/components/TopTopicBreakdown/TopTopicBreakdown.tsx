import { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import type { PieSectorDataItem } from 'recharts/types/polar/Pie';
import { PieChart } from '@mantine/charts';
import { Flex } from '@mantine/core';
import { UseFormReturnType } from '@mantine/form';
import { Text } from '@/components/Texts/Text/Text';
import TrendsViewHistogram from '@/pages/AnalyticsPage/components/OverallTrendsView/TrendsViewHistogram';
import { TopicScatterPlotProps, TrendsHistogramAnalyticsItemProps } from '@/types/analyticsTypes';
import { FilterForm } from '@/types/timelineTypes';
import Filters from '../Filters/Filters';

interface TopTopicBreakdownProps {
  topicAnalytics: TrendsHistogramAnalyticsItemProps;
  narratives: TopicScatterPlotProps[];
  filterForm: UseFormReturnType<FilterForm>;
}

const COLORS = ['violet.06', 'green.06', 'blue.06', 'yellow.06', 'red.06'];

export default function TopTopicBreakdown({
  topicAnalytics,
  narratives,
  filterForm,
}: TopTopicBreakdownProps) {
  const [activeNarrative, setActiveNarrative] = useState<string | null>(null);

  const pieChartData = narratives
    .sort((a, b) => b.count - a.count)
    .map((narrative, i) => ({
      name: narrative.summary,
      value: narrative.count,
      color: COLORS[i % COLORS.length],
    }))
    .slice(0, 5);

  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);

  const handlePieChartClick = (entry: PieSectorDataItem) => {
    const clickedNarrative = narratives.find((n) => n.summary === entry.name);
    if (clickedNarrative) {
      const url = `/timeline/narrative/${clickedNarrative.narrativeId}`;
      window.open(url, '_blank');
    }
  };

  const handleMouseEnter = (_: any, index: number) => {
    setHoveredIndex(index);
    setActiveNarrative(pieChartData[index]?.name ?? null);
  };

  const handleMouseLeave = () => {
    setHoveredIndex(null);
  };

  return (
    <Flex direction="column" gap={16}>
      <div className="flex flex-wrap items-center gap-4 ">
        <Flex
          direction="row"
          align="center"
          className="py-[16px] px-[20px] border border-[#DEE2E6] rounded-lg"
        >
          <Text>
            <dt>{topicAnalytics.name}</dt>
          </Text>
        </Flex>
        <TrendsViewHistogram trendHistogramItem={topicAnalytics} />
        <Filters filterForm={filterForm} showResetButton={false} />
      </div>
      <Text bold>Top Narratives</Text>
      <div className="flex flex-col md:flex-row items-center md:items-start justify-between gap-4 ">
        <div className="relative">
          <PieChart
            data={pieChartData}
            size={300}
            withTooltip
            withLabels
            withLabelsLine={false}
            tooltipDataSource="segment"
            pieProps={{
              onClick: handlePieChartClick,
              cx: '50%',
              cy: '50%',
              outerRadius: 100,
              cursor: 'pointer',
              onMouseEnter: handleMouseEnter,
              onMouseLeave: handleMouseLeave,
              activeIndex: hoveredIndex ?? undefined,
              activeShape: {
                outerRadius: 105,
              } as any,
            }}
          />
        </div>
        <Flex component="ul" direction="column" gap={8}>
          {narratives.slice(0, 5).map((narrative) => (
            <li key={narrative.narrativeId}>
              <Link to={`/timeline/narrative/${narrative.narrativeId}`} target="_blank">
                <Flex
                  direction="row"
                  justify="space-between"
                  align="center"
                  className={`py-[16px] px-[20px] border border-[#DEE2E6] rounded-lg cursor-pointer hover:shadow-md transition-shadow ${
                    activeNarrative === narrative.summary ? 'shadow bg-gray-100' : ''
                  }`}
                  gap={16}
                >
                  <Text>
                    <dt>{narrative.summary}</dt>
                  </Text>
                  <Text bold>
                    <dd>{narrative.count}</dd>
                  </Text>
                </Flex>
              </Link>
            </li>
          ))}
        </Flex>
      </div>
    </Flex>
  );
}
