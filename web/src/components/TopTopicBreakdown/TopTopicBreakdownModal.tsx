import { useEffect } from 'react';
import { useForm, UseFormReturnType } from '@mantine/form';
import { FilterForm } from '@/types/timelineTypes';
import GenericModal, { GenericModalProps } from '../Modal/GenericModal';
import TopTopicBreakdown from './TopTopicBreakdown';
import { useTopicAnalytics, useTopicNarratives } from './useTopicAnalytics';

interface TopTopicBreakdownModalProps extends Omit<GenericModalProps, 'title' | 'children'> {
  topicId: number;
  initialFilterForm: UseFormReturnType<Record<string, any>>;
}

export default function TopTopicBreakdownModal({
  topicId,
  initialFilterForm,
  ...modalProps
}: TopTopicBreakdownModalProps) {
  const filterForm = useForm<FilterForm>({
    initialValues: {
      dateRange: 'All time',
      customDateRange: '',
    },
  });

  const { topicAnalytics, loading: topicAnalyticsLoading } = useTopicAnalytics(
    topicId,
    filterForm.values
  );
  const { narratives, loading: narrativesLoading } = useTopicNarratives(topicId, filterForm.values);

  const loading = topicAnalyticsLoading || narrativesLoading;

  useEffect(() => {
    if (!modalProps.opened) return;

    const seed = {
      dateRange: initialFilterForm.values.dateRange,
      customDateRange: initialFilterForm.values.customDateRange,
    };

    filterForm.setValues(seed);
    filterForm.setInitialValues(seed);
    filterForm.resetDirty(seed);
  }, [
    modalProps.opened,
    initialFilterForm.values.dateRange,
    initialFilterForm.values.customDateRange,
  ]);

  return (
    <GenericModal {...modalProps} loading={loading} title="Top Topic Breakdown" size="lg">
      {topicAnalytics && narratives && (
        <TopTopicBreakdown
          topicAnalytics={topicAnalytics}
          narratives={narratives}
          filterForm={filterForm}
        />
      )}
    </GenericModal>
  );
}
