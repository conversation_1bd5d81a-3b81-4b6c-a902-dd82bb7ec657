import { useEffect, useState } from 'react';
import { analyticsService } from '@/api/analytics/analyticsService';
import { getDateRangeParams } from '@/helpers/dateUtils';
import { TopicScatterPlotProps, TrendsHistogramAnalyticsItemProps } from '@/types/analyticsTypes';
import { FilterForm } from '@/types/timelineTypes';

export function useTopicAnalytics(topicId: number, filters?: FilterForm) {
  const [loading, setLoading] = useState(true);
  const [topicAnalytics, setTopicAnalytics] = useState<TrendsHistogramAnalyticsItemProps>();
  useEffect(() => {
    setTopicAnalytics(undefined);
    if (!topicId) return;

    setLoading(true);
    const { dateFrom, dateTo } = getDateRangeParams(filters);
    analyticsService
      .getTopicAnalytics(topicId, {
        dateFrom,
        dateTo,
      })
      .then((data) => {
        setTopicAnalytics(data);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [topicId, filters]);

  return { topicAnalytics, loading };
}

export function useTopicNarratives(topicId: number, filters?: FilterForm) {
  const [loading, setLoading] = useState(true);
  const [narratives, setNarratives] = useState<TopicScatterPlotProps[]>();

  useEffect(() => {
    setNarratives(undefined);
    if (!topicId) return;

    setLoading(true);
    const { dateFrom, dateTo } = getDateRangeParams(filters);

    analyticsService
      .getNarrativeScatterPlot({
        topicIds: [topicId],
        dateFrom,
        dateTo,
      })
      .then((data) => {
        setNarratives(data);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [topicId, filters]);

  return { narratives, loading };
}
