import { useEffect, useRef, useState } from 'react';
import useSessionStorageState from 'use-session-storage-state';
import { chatbotService } from '@/api/chatbot/chatbotService';
import { useChatbotContext } from '@/context/ChatbotContext';
import { ChatMessage, ChatRequestDto, LocalChatMessages } from '@/types/chatbotTypes';
import { TimelineProps } from '@/types/timelineTypes';

export const useChatbot = () => {
  const [addPostsModalOpen, setAddPostsModalOpen] = useState(false);

  const { timelinesToBeSent, chatOpen, chatIsMaximized, setTimelinesToBeSent } =
    useChatbotContext();

  const [localMessages, setLocalMessages] = useState<LocalChatMessages[]>([]);

  const [input, setInput] = useState('');
  const [botIsResponding, setBotIsResponding] = useState(false);
  const [progressiveStatus, setProgressiveStatus] = useState<string | null>(null);
  const scrollRef = useRef<HTMLDivElement>(null);
  const statusTimersRef = useRef<NodeJS.Timeout[]>([]);

  const [chatMessages, setChatMessages] = useSessionStorageState('chatMessages', {
    defaultValue: localMessages,
  });
  const [storageSentTimelineReference, setStorageSentTimelineReference] = useSessionStorageState(
    'storageSentTimelineReference',
    {
      defaultValue: [] as TimelineProps[],
    }
  );
  const [previousMessageId, setPreviousMessageId] = useSessionStorageState('previousMessageId', {
    defaultValue: '' as string,
  });

  const startProgressiveStatus = () => {
    // Clear any existing timers
    statusTimersRef.current.forEach((timer) => clearTimeout(timer));
    statusTimersRef.current = [];

    // Set progressive status messages
    const timer1 = setTimeout(() => {
      setProgressiveStatus('thinking');
    }, 2000);
    statusTimersRef.current.push(timer1);

    const timer2 = setTimeout(() => {
      setProgressiveStatus('searching content');
    }, 6000);
    statusTimersRef.current.push(timer2);

    const timer3 = setTimeout(() => {
      setProgressiveStatus('summarizing response');
    }, 9000);
    statusTimersRef.current.push(timer3);
  };

  const clearProgressiveStatus = () => {
    statusTimersRef.current.forEach((timer) => clearTimeout(timer));
    statusTimersRef.current = [];
    setProgressiveStatus(null);
  };

  const sendMessage = async () => {
    if (input.trim()) {
      let messageToBeSent: ChatMessage[] = [{ type: 'text', content: input }];
      const history = [
        ...localMessages,
        ...messageToBeSent.map((msg) => ({ source: 'user', ...msg })),
      ];

      if (timelinesToBeSent.length > 0) {
        const mergedSentTimelines = [...storageSentTimelineReference, ...timelinesToBeSent];
        const sentTimelineReference = Array.from(
          new Map(mergedSentTimelines.map((item) => [item.id, item])).values()
        );

        setStorageSentTimelineReference(sentTimelineReference);
        const timelineMessages = [] as ChatMessage[];
        timelinesToBeSent.forEach((timeline) => {
          const timelineConvertedMessage = {
            type: timeline.type,
            content: timeline.sourceId,
          };
          timelineMessages.push(timelineConvertedMessage);
          history.push({
            source: 'user',
            ...timelineConvertedMessage,
          });
        });
        messageToBeSent = [...messageToBeSent, ...timelineMessages];
      }

      setChatMessages(history as LocalChatMessages[]);
      setInput('');
      setTimelinesToBeSent([]);
      setBotIsResponding(true);
      startProgressiveStatus();

      try {
        const payload: ChatRequestDto = {
          messages: messageToBeSent,
          previousResponseId: previousMessageId ? previousMessageId : undefined,
        };
        const response = await chatbotService.chat(payload);
        setPreviousMessageId(response.messages[response.messages.length - 1].id);
        setChatMessages(
          (prev) =>
            [...prev, { ...response.messages[0], source: 'botResponse' }] as LocalChatMessages[]
        );
      } catch (err) {
        setChatMessages(
          (prev) =>
            [
              ...prev,
              {
                source: 'botResponse',
                type: 'text',
                content: 'An unexpected error occurred.',
              },
            ] as LocalChatMessages[]
        );
      } finally {
        setBotIsResponding(false);
        clearProgressiveStatus();
      }
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && !botIsResponding) {
      sendMessage();
    }
  };

  const handleRefresh = () => {
    setLocalMessages([]);
  };

  useEffect(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollTo({
        top: scrollRef.current.scrollHeight,
      });
    }
  }, [localMessages, chatIsMaximized]);

  useEffect(() => {
    if (chatOpen && chatIsMaximized && localMessages.length === 0) {
      setChatMessages([
        {
          source: 'botResponse',
          type: 'text',
          content: 'Hello! How can I assist you today?',
        },
      ]);
    }
  }, [chatOpen, chatIsMaximized]);

  useEffect(() => {
    setLocalMessages(chatMessages);
  }, [chatMessages]);

  // Cleanup timers on unmount
  useEffect(() => {
    return () => {
      statusTimersRef.current.forEach((timer) => clearTimeout(timer));
    };
  }, []);

  return {
    botIsResponding,
    localMessages,
    scrollRef,
    input,
    addPostsModalOpen,
    storageSentTimelineReference,
    progressiveStatus,

    setAddPostsModalOpen,
    setInput,
    handleRefresh,
    handleKeyDown,
    sendMessage,
  };
};
