import { Flex } from '@mantine/core';

interface ProgressiveStatusBubbleProps {
  status: string;
}

const ProgressiveStatusBubble = ({ status }: ProgressiveStatusBubbleProps) => {
  return (
    <Flex justify="start" className="ml-[5px]">
      <div className="bg-[#F1F3F5] px-[25px] py-[10px] rounded-[16px]">
        <div className="text-sm text-gray-600 italic">{status}...</div>
      </div>
    </Flex>
  );
};

export default ProgressiveStatusBubble;
