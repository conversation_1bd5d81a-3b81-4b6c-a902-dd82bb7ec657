import { Flex } from '@mantine/core';
import { Icon } from '@/components/icon/Icon';
import Markdown from '@/components/Markdown/Markdown';
import { Text } from '@/components/Texts/Text/Text';
import { LocalChatMessages } from '@/types/chatbotTypes';
import { TimelineProps } from '@/types/timelineTypes';

interface ChatBubbleProps {
  message: LocalChatMessages;
  sentTimelinesReference: TimelineProps[];
}

const ChatBubble = ({ message, sentTimelinesReference }: ChatBubbleProps) => {
  const { source, content, type } = message;
  return (
    <Flex justify={source === 'user' ? 'end' : 'start'}>
      <div
        className={`
            relative inline-block px-[15px] py-[7px] rounded-[16px] max-w-[75%]
            ${source === 'user' ? `text-left mr-[5px]` : 'text-left ml-[5px]'}
            ${source === 'user' ? (type !== 'text' ? 'bg-[#222E50]' : 'bg-[#7048E8]') : 'bg-[#F1F3F5]'}
        `}
      >
        {type === 'text' ? (
          <Text
            className={`
            ${source === 'user' ? 'text-white' : 'text-[#212529]'}
            `}
            component="div"
          >
            <Markdown>{content}</Markdown>
          </Text>
        ) : (
          <Flex direction="row" gap={12} align="center" className="max-w-[130px]">
            {type === 'YOUTUBE_VIDEO' || type === 'YOUTUBE_COMMENT' ? (
              <img
                className="w-[20px] h-[20px]"
                src="/icons/sourceIcons/youtubeIcon.svg"
                alt="youtube icon"
              />
            ) : (
              <img
                className="w-[20px] h-[20px]"
                src="/icons/sourceIcons/redditIcon.svg"
                alt="reddit icon"
              />
            )}
            <Text
              size="md"
              className={`
                truncate
            ${source === 'user' ? 'text-white' : 'text-[#212529]'}
            `}
            >
              {sentTimelinesReference.find((timeline) => timeline.sourceId === message.content)
                ?.title || ''}
            </Text>
          </Flex>
        )}

        <Icon
          iconCategory="timeline"
          iconType={source === 'user' ? 'user' : 'bot'}
          className={`
            absolute 
            ${source === 'user' ? 'right-[-5px] bottom-[0px]' : 'left-[-4px] bottom-[0px]'}
            ${source === 'user' ? (type !== 'text' ? 'hidden' : 'text-[#7048E8]') : 'text-[#F1F3F5]'}
          `}
        />
      </div>
    </Flex>
  );
};

export default ChatBubble;
