import { Flex, Input } from '@mantine/core';
import { Text } from '@/components/Texts/Text/Text';
import Button from '../Button/Button';
import ChatIconWithoutArrow from './icons/chatIconWithoutArrow.svg?react';
import ChatbotOpenIcon from './icons/tkChatbotOpenIcon.svg?react';

import './chatbot.css';

import { useAppContext } from '@/context/AppContext';
import { useChatbotContext } from '@/context/ChatbotContext';
import AddPostModal from '../Modal/addPostModal/AddPostModal';
import BotLoadingBubble from './components/BotLoadingBubble';
import ChatBubble from './components/ChatBubble';
import ProgressiveStatusBubble from './components/ProgressiveStatusBubble';
import { useChatbot } from './useChatbot';

const Chatbot = () => {
  const {
    botIsResponding,
    localMessages,
    scrollRef,
    input,
    addPostsModalOpen,
    storageSentTimelineReference,
    progressiveStatus,

    setAddPostsModalOpen,
    setInput,

    handleRefresh,
    handleKeyDown,
    sendMessage,
  } = useChatbot();

  const {
    timelinesToBeSent,
    chatOpen,
    chatIsMaximized,
    setChatOpen,
    setTimelinesToBeSent,
    setChatIsMaximized,
  } = useChatbotContext();

  const { initialized, userInfo } = useAppContext();

  if (!initialized || !userInfo) {
    return null;
  }

  if (!chatOpen) {
    return (
      <Button
        className="fixed bottom-[40px] right-[40px] z-10 cursor-pointer shadox-lg"
        onClick={() => {
          setChatOpen?.(true);
          setChatIsMaximized?.(true);
        }}
      >
        <ChatbotOpenIcon className="hover:drop-shadow-lg transition-all" />
      </Button>
    );
  }
  return (
    <>
      <AddPostModal onClose={() => setAddPostsModalOpen(false)} opened={addPostsModalOpen} />
      <div
        className={`
        fixed bottom-0 right-0 md:bottom-[40px] md:right-[40px] z-10 bg-white rounded-lg
        drop-shadow-lg flex flex-col
        ${chatIsMaximized ? 'w-full h-[calc(100vh-60px)] md:w-[400px] md:h-[65%]' : 'w-full md:w-[400px] h-[60px]'}
        transition-all duration-300
      `}
      >
        <Flex
          direction="row"
          justify="space-between"
          align="center"
          w="100%"
          p={12}
          className={`${chatIsMaximized ? 'border-b border-[#DEE2E6]' : ''}`}
        >
          <Flex direction="row" gap={12} align="center">
            <ChatIconWithoutArrow />
            <Text size="lg" bold>
              Truthkeep AI Chatbot Beta
            </Text>
          </Flex>
          <Flex direction="row" gap={4}>
            <Button
              className="w-[24px] h-[24px] p-0 cursor-pointer"
              onClick={() => setChatIsMaximized?.((m) => !m)}
            >
              {chatIsMaximized ? (
                <img src="/icons/chatbot/windowMinimizeIcon.svg" alt="minimize" />
              ) : (
                <img src="/icons/chatbot/windowMaximizeIcon.svg" alt="maximize" />
              )}
            </Button>
            <Button
              className="w-[24px] h-[24px] p-0 cursor-pointer"
              onClick={handleRefresh}
              disabled={localMessages.length === 0}
            >
              <img src="/icons/chatbot/refreshIcon.svg" alt="refresh" />
            </Button>
            <Button
              className="w-[24px] h-[24px] p-0 cursor-pointer"
              onClick={() => {
                setChatOpen?.(false);
                setChatIsMaximized?.(false);
              }}
            >
              <img src="/icons/chatbot/closeIcon.svg" alt="close" />
            </Button>
          </Flex>
        </Flex>

        {chatIsMaximized && (
          <>
            <Flex
              ref={scrollRef}
              direction="column"
              gap={8}
              className="overflow-y-auto flex-1 mt-2"
              p={12}
            >
              {localMessages.map((message, index) => (
                <ChatBubble
                  key={index}
                  message={message}
                  sentTimelinesReference={storageSentTimelineReference}
                />
              ))}
              {progressiveStatus && <ProgressiveStatusBubble status={progressiveStatus} />}
              {botIsResponding && !progressiveStatus && <BotLoadingBubble />}
            </Flex>

            <Flex direction="column" w="100%" mt={4}>
              {timelinesToBeSent?.length > 0 && (
                <Flex direction="row" gap={8} className="w-full overflow-x-auto h-[40px]" p={12}>
                  {timelinesToBeSent.map((timeline) => {
                    const { type, title } = timeline;
                    return (
                      <Flex
                        direction="row"
                        gap={12}
                        align="center"
                        className="flex-shrink-0 max-w-[150px] bg-[#222E50] rounded-lg"
                        p={14}
                        key={timeline.id}
                      >
                        {type === 'YOUTUBE_VIDEO' || type === 'YOUTUBE_COMMENT' ? (
                          <img
                            className="w-[20px] h-[20px]"
                            src="/icons/sourceIcons/youtubeIcon.svg"
                            alt="youtube icon"
                          />
                        ) : (
                          <img
                            className="w-[20px] h-[20px]"
                            src="/icons/sourceIcons/redditIcon.svg"
                            alt="reddit icon"
                          />
                        )}
                        <Text size="md" className="truncate text-white">
                          {title}
                        </Text>
                        <Button
                          onClick={() => {
                            setTimelinesToBeSent((prev) =>
                              prev.filter((t) => t.id !== timeline?.id)
                            );
                          }}
                          className="cursor-pointer"
                        >
                          <img
                            src="/icons/pills/pillDeleteIcon.svg"
                            alt="Delete Loaded Timeline Icon"
                            className="min-w-[14px] min-h-[16px] ml-[6px]"
                          />
                        </Button>
                      </Flex>
                    );
                  })}
                </Flex>
              )}
              <Flex direction="row" gap={8} align="center" p={12}>
                <Button
                  className="min-w-[40px] min-h-[40px] p-0 cursor-pointer"
                  onClick={() => {
                    setAddPostsModalOpen(true);
                  }}
                >
                  <img
                    src="/icons/miscIcons/addIcon.svg"
                    alt="add post"
                    className="border border-[#DEE2E6] rounded-sm w-full h-full"
                  />
                </Button>
                <Input
                  className="flex-1"
                  placeholder="Enter message"
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  onKeyDown={handleKeyDown}
                  rightSectionPointerEvents="all"
                  rightSection={
                    <Button
                      className="w-[32px] h-[32px] p-0 cursor-pointer"
                      onClick={sendMessage}
                      disabled={botIsResponding}
                    >
                      <img src="/icons/chatbot/sendMessageIcon.svg" alt="send" />
                    </Button>
                  }
                />
              </Flex>
            </Flex>
          </>
        )}
      </div>
    </>
  );
};

export default Chatbot;
