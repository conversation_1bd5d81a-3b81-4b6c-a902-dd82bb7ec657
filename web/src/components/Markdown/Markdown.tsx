import ReactMarkdown, { Components } from 'react-markdown';

export default function Markdown({ children }: { children: string }) {
  return <ReactMarkdown components={components}>{children}</ReactMarkdown>;
}

const components: Components = {
  ol: ({ children, ...props }) => (
    <ul className="list-disc px-4 py-2" {...props}>
      {children}
    </ul>
  ),
  ul: ({ children, ...props }) => (
    <ul className="list-disc pl-4" {...props}>
      {children}
    </ul>
  ),
  li: ({ children, ...props }) => (
    <li className="list-disc" {...props}>
      {children}
    </li>
  ),
  a: ({ children, ...props }) => (
    <a className="text-blue-500 underline" target="_blank" rel="noreferrer" {...props}>
      {children}
    </a>
  ),
};
