import { useEffect, useState } from 'react';
import { UseFormReturnType } from '@mantine/form';
import { analyticsService } from '@/api/analytics/analyticsService';
import { narrativeService } from '@/api/narratives/narrativeService';
import { timelineService } from '@/api/timeline/timelineService';
import { getDateRangeParams } from '@/helpers/dateUtils';
import { TrendsHistogramAnalyticsItemProps } from '@/types/analyticsTypes';
import { NarrativeBreakdownProps } from '@/types/narrativeTypes';
import { PaginatedResponse } from '@/types/paginatedTypes';
import { FilterForm, TimelineProps } from '@/types/timelineTypes';
import GenericModal, { GenericModalProps } from '../Modal/GenericModal';
import NarrativeBreakdown from './NarrativeBreakdown';

interface NarrativeBreakdownModalProps extends Omit<GenericModalProps, 'title' | 'children'> {
  narrativeId: number | null;
  filterForm?: UseFormReturnType<FilterForm>;
}

export default function NarrativeBreakdownModal({
  narrativeId,
  filterForm,
  ...modalProps
}: NarrativeBreakdownModalProps) {
  const [narrativeStatsLoading, setNarrativeStatsLoading] = useState(false);
  const [narrativeStats, setNarrativeStats] = useState<TrendsHistogramAnalyticsItemProps | null>();

  useEffect(() => {
    setNarrativeStats(null);
    if (!narrativeId) return;

    setNarrativeStatsLoading(true);
    const { dateFrom, dateTo } = getDateRangeParams(filterForm?.values);

    analyticsService
      .getNarrativeComparisonPopover(narrativeId, {
        dateFrom,
        dateTo,
      })
      .then(setNarrativeStats)
      .finally(() => setNarrativeStatsLoading(false));
  }, [narrativeId, filterForm?.values]);

  const [postsLoading, setPostsLoading] = useState(false);
  const [posts, setPosts] = useState<PaginatedResponse<TimelineProps>>();

  useEffect(() => {
    setPosts(undefined);
    if (!narrativeId) return;

    setPostsLoading(true);
    const { dateFrom, dateTo } = getDateRangeParams(filterForm?.values);
    timelineService
      .fetchTimeline({
        narrativeIds: [narrativeId],
        dateFrom,
        dateTo,
        size: 10,
      })
      .then((response) => setPosts(response))
      .finally(() => setPostsLoading(false));
  }, [narrativeId, filterForm?.values]);

  const [narrativeBreakdownLoading, setNarrativeBreakdownLoading] = useState(false);
  const [narrativeBreakdown, setNarrativeBreakdown] = useState<NarrativeBreakdownProps | null>();

  useEffect(() => {
    setNarrativeBreakdown(null);
    if (!narrativeId) return;

    setNarrativeBreakdownLoading(true);
    const { dateFrom, dateTo } = getDateRangeParams(filterForm?.values);
    narrativeService
      .fetchNarrativeBreakdown(narrativeId, {
        dateFrom,
        dateTo,
      })
      .then(setNarrativeBreakdown)
      .finally(() => setNarrativeBreakdownLoading(false));
  }, [narrativeId, filterForm?.values]);

  const loading = narrativeStatsLoading || postsLoading;

  return (
    <GenericModal
      {...modalProps}
      loading={loading || !narrativeId}
      title="Narrative Breakdown"
      size="xl"
      disableScroll
    >
      {!loading && narrativeStats && posts && (
        <NarrativeBreakdown
          narrativeStats={narrativeStats}
          posts={posts}
          narrativeBreakdownLoading={narrativeBreakdownLoading}
          narrativeBreakdown={narrativeBreakdown}
        />
      )}
    </GenericModal>
  );
}
