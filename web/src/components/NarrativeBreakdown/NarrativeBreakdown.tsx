import { Link } from 'react-router-dom';
import { Flex, Loader } from '@mantine/core';
import { Text } from '@/components/Texts/Text/Text';
import TrendsViewHistogram from '@/pages/AnalyticsPage/components/OverallTrendsView/TrendsViewHistogram';
import { TrendsHistogramAnalyticsItemProps } from '@/types/analyticsTypes';
import { NarrativeBreakdownProps } from '@/types/narrativeTypes';
import { PaginatedResponse } from '@/types/paginatedTypes';
import { TimelineProps } from '@/types/timelineTypes';
import Markdown from '../Markdown/Markdown';
import SimpleTimelineCard from '../SimpleTimelineCard/SimpleTimelineCard';
import { Heading } from '../Texts/Heading/Heading';

interface NarrativeBreakdownComponentProps {
  narrativeStats: TrendsHistogramAnalyticsItemProps;
  posts: PaginatedResponse<TimelineProps>;
  narrativeBreakdownLoading: boolean;
  narrativeBreakdown?: NarrativeBreakdownProps | null;
}

export default function NarrativeBreakdown({
  narrativeStats,
  posts,
  narrativeBreakdownLoading,
  narrativeBreakdown,
}: NarrativeBreakdownComponentProps) {
  const morePostsCount = posts?.total - posts?.items.length;

  return (
    <Flex className="flex-col md:flex-row" gap={16}>
      <Flex
        direction="column"
        gap={16}
        w="100%"
        className="md:pb-[var(--mb-padding,_var(--mantine-spacing-md))] md:max-h-[calc(100dvh_-_var(--modal-y-offset)_*_2_-_3.75rem_*_var(--mantine-scale))] md:overflow-y-auto"
      >
        <Flex
          direction="row"
          w="100%"
          h="fit-content"
          align="center"
          justify="space-between"
          className="h-full py-[16px] px-[20px] border border-[#DEE2E6] rounded-lg"
          gap={16}
          key={narrativeStats.id}
        >
          <Text className="text-start">
            <dt>{narrativeStats.summary}</dt>
          </Text>
          <Text bold className="text-nowrap">
            <dd>{narrativeStats.count}</dd>
          </Text>
        </Flex>
        <Flex direction="column" gap={16} w="100%">
          <div hidden={!narrativeBreakdown?.longSummary && !narrativeBreakdownLoading}>
            <Heading tagLevel="5">Summary</Heading>
            {narrativeBreakdownLoading && <Loader type="dots" className="mx-auto" />}
            <Text className="text-start">{narrativeBreakdown?.longSummary}</Text>
          </div>
          <div hidden={!narrativeBreakdown?.tipsAndActions && !narrativeBreakdownLoading}>
            <Heading tagLevel="5">Tips & Actions</Heading>
            {narrativeBreakdownLoading && <Loader type="dots" className="mx-auto" />}
            <div className="text-sm">
              <Markdown>{narrativeBreakdown?.tipsAndActions || ''}</Markdown>
            </div>
          </div>
        </Flex>
      </Flex>
      <Flex
        direction="column"
        w="100%"
        gap={16}
        className="md:pb-[var(--mb-padding,_var(--mantine-spacing-md))] md:max-h-[calc(100dvh_-_var(--modal-y-offset)_*_2_-_3.75rem_*_var(--mantine-scale))] md:overflow-y-auto"
      >
        <div className="w-min">
          <TrendsViewHistogram trendHistogramItem={narrativeStats} />
        </div>
        <Flex direction="column" gap={8} hidden={posts?.items.length === 0}>
          <Heading tagLevel="5">Posts</Heading>
          <Flex direction="column" gap={16}>
            {posts?.items.map((post) => (
              <Link
                key={post.id}
                to={`/timeline/${post.id}`}
                target="_blank"
                className="no-underline"
              >
                <SimpleTimelineCard
                  timeline={post}
                  className="hover:shadow-md transition-shadow cursor-pointer"
                />
              </Link>
            ))}
            <Link
              to={`/timeline/narrative/${narrativeStats.id}`}
              className="text-blue-500 hover:underline"
              target="_blank"
              hidden={!(morePostsCount > 0)}
            >
              <Text>Show {morePostsCount} more</Text>
            </Link>
          </Flex>
        </Flex>
      </Flex>
    </Flex>
  );
}
