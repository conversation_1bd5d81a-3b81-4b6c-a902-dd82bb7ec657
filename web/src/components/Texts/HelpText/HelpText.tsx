import { ReactNode } from 'react';
import { Text } from '@/components/Texts/Text/Text';
import { useHelpModal } from '@/context/HelpModalContext';

interface HelpTextProps {
  text: ReactNode;
  modalPage: string;
  className?: string;
}

const HelpText = ({ text, modalPage, className = '' }: HelpTextProps) => {
  const { openHelpModal } = useHelpModal();

  const handleHelpClick = () => {
    openHelpModal(modalPage);
  };

  return (
    <Text size="md" className={`text-gray-600 ${className}`}>
      {text}
      <button
        type="button"
        onClick={handleHelpClick}
        className="cursor-pointer hover:opacity-70 transition-opacity ml-1 inline-flex items-center"
        aria-label="Open help"
      >
        <svg
          width="14"
          height="14"
          viewBox="0 0 16 16"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          className="text-gray-500"
          style={{ transform: 'translateY(2px)' }}
        >
          <circle cx="8" cy="8" r="7" stroke="currentColor" strokeWidth="1.5" />
          <path
            d="M6 6C6 4.89543 6.89543 4 8 4C9.10457 4 10 4.89543 10 6C10 7.10457 9.10457 8 8 8V10"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
          />
          <circle cx="8" cy="12" r="0.5" fill="currentColor" />
        </svg>
      </button>
    </Text>
  );
};

export default HelpText;
