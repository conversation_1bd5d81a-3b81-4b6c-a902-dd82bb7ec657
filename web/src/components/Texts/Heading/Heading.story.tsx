import { <PERSON>a, <PERSON>Obj } from '@storybook/react';
import { Heading } from './Heading';

const meta: Meta<typeof Heading> = {
  title: 'Components/Heading',
  component: Heading,
  tags: ['autodocs'],
  argTypes: {
    tagLevel: {
      control: { type: 'select' },
      options: ['1', '2', '3', '4', '5'],
    },
    className: {
      control: { type: 'text' },
    },
    children: {
      control: { type: 'text' },
      defaultValue: 'Sample Text',
    },
  },
};

export default meta;

type Story = StoryObj<typeof Heading>;

export const Default: Story = {
  args: {
    tagLevel: '1',
    children: 'Default Heading (H1)',
  },
};

export const AllLevels: Story = {
  render: () => (
    <div className="space-y-2">
      <Heading tagLevel="1">Heading Tag Level 1</Heading>
      <Heading tagLevel="2">Heading Tag Level 2</Heading>
      <Heading tagLevel="3">Heading Tag Level 3</Heading>
      <Heading tagLevel="4">Heading Tag Level 4</Heading>
      <Heading tagLevel="5">Heading Tag Level 5</Heading>
    </div>
  ),
};

export const CustomClass: Story = {
  args: {
    tagLevel: '2',
    className: 'text-blue-500 underline',
    children: 'Custom Styled Heading',
  },
};
