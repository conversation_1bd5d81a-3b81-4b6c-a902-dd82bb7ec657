import React, { JSX } from 'react';

type HeadingProps = {
  tagLevel: '1' | '2' | '3' | '4' | '5';
  children: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
  id?: string;
};

const defaultClassNameMap: Record<HeadingProps['tagLevel'], string> = {
  '1': 'font-bold text-[40px] leading-[48px] tracking-[0%]',
  '2': 'font-bold text-[32px] leading-[40px] tracking-[0%]',
  '3': 'font-bold text-[24px] leading-[32px] tracking-[0%]',
  '4': 'font-bold text-[18px] leading-[24px] tracking-[0%]',
  '5': 'font-bold text-[16px] leading-[20px] tracking-[0%]',
};

export function Heading({ tagLevel, children, className = '', style = {}, id }: HeadingProps) {
  const Tag = `h${tagLevel}` as keyof JSX.IntrinsicElements;
  const combinedClass = `${defaultClassNameMap[tagLevel]} ${className}`.trim();

  return (
    <Tag className={combinedClass} style={style} id={id}>
      {children}
    </Tag>
  );
}
