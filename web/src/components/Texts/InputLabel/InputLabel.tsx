import { ReactNode } from 'react';

type InputLabelProps = {
  children: ReactNode;
  htmlFor?: string;
  required?: boolean;
  className?: string;
};

export function InputLabel({ children, htmlFor, required, className }: InputLabelProps) {
  return (
    <label htmlFor={htmlFor} className={`text-[14px] leading-[16px] tracking-[0%] ${className}`}>
      {children} {required && <span className="text-red-500">*</span>}
    </label>
  );
}
