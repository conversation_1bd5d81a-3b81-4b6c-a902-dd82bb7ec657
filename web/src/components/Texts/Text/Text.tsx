import React from 'react';

type TextProps = {
  children: React.ReactNode;
  size?: 'xs' | 'sm' | 'md' | 'lg' | '2lg' | 'xl';
  bold?: boolean;
  className?: string;
  style?: React.CSSProperties;
  component?: React.ElementType;
  hidden?: boolean;
  id?: string;
};

const sizeClasses = {
  xs: 'text-[10px] leading-[12px] tracking-[0%]',
  sm: 'text-[12px] leading-[14px] tracking-[0%]s',
  md: 'text-[14px] leading-[16px] tracking-[0%]',
  lg: 'text-[16px] leading-[18px] tracking-[0%]',
  '2lg': 'text-[16px] leading-[18px] tracking-[0%]',
  xl: 'text-[20px] leading-[24px] tracking-[0%]',
};

export function Text({
  children,
  size = 'md',
  bold = false,
  className = '',
  style = {},
  component = 'p',
  hidden = false,
  id,
}: TextProps) {
  const combinedClass = `${bold ? 'font-bold' : ''} ${sizeClasses[size]} ${className}`.trim();

  return React.createElement(
    component,
    {
      className: combinedClass,
      style,
      id,
      hidden,
    },
    children
  );
}
