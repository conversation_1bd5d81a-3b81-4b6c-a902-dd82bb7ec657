import { Meta, StoryObj } from '@storybook/react';
import { Text } from './Text';

const meta: Meta<typeof Text> = {
  title: 'Components/Text',
  component: Text,
  tags: ['autodocs'],
  argTypes: {
    size: {
      control: { type: 'select' },
      options: ['sm', 'md', 'lg', 'xl'],
    },
    bold: {
      control: { type: 'boolean' },
    },
    className: {
      control: { type: 'text' },
    },
    children: {
      control: { type: 'text' },
      defaultValue: 'Sample Text',
    },
  },
};

export default meta;

type Story = StoryObj<typeof Text>;

export const Default: Story = {
  args: {
    size: 'md',
    bold: false,
    children: 'This is default text',
  },
};

export const Sizes: Story = {
  render: () => (
    <div className="space-y-2">
      <Text size="sm">Small text</Text>
      <Text size="md">Medium text</Text>
      <Text size="lg">Large text</Text>
      <Text size="xl">Extra Large text</Text>
    </div>
  ),
};

export const BoldText: Story = {
  args: {
    size: 'md',
    bold: true,
    children: 'Bold text',
  },
};

export const CustomClass: Story = {
  args: {
    size: 'lg',
    bold: false,
    className: 'text-red-500 underline',
    children: 'Custom styled text',
  },
};
