import { <PERSON>a, StoryObj } from '@storybook/react';
import { Input } from './Input';

const meta: Meta<typeof Input> = {
  title: 'Components/Input',
  component: Input,
  argTypes: {
    type: { table: { disable: true } },
    size: {
      control: 'select',
      options: ['sm', 'md', 'lg', 'xl'],
    },
    placeholder: {
      control: 'text',
    },
    required: {
      control: 'boolean',
    },
    disabled: {
      control: 'boolean',
    },
    radius: {
      control: { type: 'range', min: 0, max: 50, step: 1 },
    },
    data: {
      control: 'object',
    },
    onChange: { action: 'changed' },
  },
  args: {
    type: 'text',
    label: 'Label',
    placeholder: 'Enter text...',
    size: 'md',
    required: false,
    disabled: false,
    radius: 8,
  },
};

export default meta;
type Story = StoryObj<typeof Input>;

export const TextInput: Story = {
  args: {
    type: 'text',
    label: 'text label',
  },
};

export const NumberInput: Story = {
  args: {
    type: 'number',
    placeholder: 'Enter a number...',
    label: 'number input label',
  },
};

export const DropdownInput: Story = {
  args: {
    type: 'dropdown',
    data: ['Option 1', 'Option 2', 'Option 3'],
    placeholder: 'Pick an Option...',
    label: 'dropdown label',
  },
};
