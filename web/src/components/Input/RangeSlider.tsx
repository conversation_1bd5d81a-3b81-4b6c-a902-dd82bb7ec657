import { useEffect, useState } from 'react';
import {
  RangeSlider as MantineRangeSlider,
  RangeSliderProps,
  RangeSliderValue,
} from '@mantine/core';

export function LazyRangeSlider(props: RangeSliderProps) {
  const [tempValue, setTempValue] = useState<RangeSliderValue | undefined>(props.value);

  // Syncs state on form clear
  useEffect(() => {
    setTempValue(props.value);
  }, [props.value]);

  return (
    <MantineRangeSlider
      {...props}
      value={tempValue}
      onChange={(value) => setTempValue(value)}
      onChangeEnd={props.onChange}
    />
  );
}
