import { useEffect, useState } from 'react';
import { Flex, Select } from '@mantine/core';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import { groupService } from '@/api/group/groupService';
import { timelineService } from '@/api/timeline/timelineService';
import { TeamTypeProps } from '@/types/teamType';
import { TimelineProps } from '@/types/timelineTypes';
import Button from '../Button/Button';
import SimpleTimelineCard from '../SimpleTimelineCard/SimpleTimelineCard';
import GenericModal from './GenericModal';

type AssignTagsToTopicModalProps = {
  opened: boolean;
  onClose: () => void;
  onExitTransitionEnd?: () => void;
  timeline: TimelineProps;
};

const AssignGroupToTimelineModal = ({
  opened,
  onClose,
  onExitTransitionEnd,
  timeline,
}: AssignTagsToTopicModalProps) => {
  const [loading, setLoading] = useState(false);
  const [groupOptions, setGroupOptions] = useState<TeamTypeProps[]>([]);

  const form = useForm<{
    groupId: number | null;
  }>({
    initialValues: {
      groupId: null,
    },
    validate: {
      groupId: (value) => (value ? null : 'Team is required'),
    },
  });

  const initialize = async () => {
    try {
      const response = groupService.fetchGroups();
      setGroupOptions((await response).data.items as TeamTypeProps[]);
    } catch (e) {
      notifications.show({
        title: 'Error fetching data',
        message: 'There was an error fetching teams',
        autoClose: 3000,
        color: 'red',
      });
    }
  };

  useEffect(() => {
    if (opened) {
      initialize();
    }
  }, [opened]);

  const handleShareTimelineToAGroup = async (values: { groupId: number | null }) => {
    setLoading(true);
    try {
      if (values.groupId) {
        await timelineService.shareAnalysis(timeline.id, values.groupId);
        notifications.show({
          title: 'Timeline successfully shared to a team.',
          message: 'The timeline has been successfully shared to a team.',
          autoClose: 3000,
          color: 'teal',
        });
        onClose();
      }
    } catch {
      notifications.show({
        title: 'Failed to share a timeline to a team',
        color: 'red',
        autoClose: 3000,
        message: '',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <GenericModal
      title="Share a Timeline to a Team"
      opened={opened}
      onClose={() => {
        onClose();
      }}
      loading={loading}
      onExitTransitionEnd={async () => {
        onExitTransitionEnd?.();
        setGroupOptions([]);
        form.reset();
      }}
      size="lg"
    >
      <form onSubmit={form.onSubmit(handleShareTimelineToAGroup)} noValidate>
        <Flex direction="column" gap={24}>
          <Select
            label="Team"
            placeholder="Select a team"
            data={groupOptions.map((group) => ({
              label: group.name,
              value: String(group.id),
            }))}
            {...form.getInputProps('groupId')}
          />
          <SimpleTimelineCard timeline={timeline} />
          <Flex gap={8}>
            <Button
              variant="secondary"
              onClick={() => {
                onClose();
              }}
            >
              Cancel
            </Button>
            <Button variant="primary" type="submit">
              Share
            </Button>
          </Flex>
        </Flex>
      </form>
    </GenericModal>
  );
};

export default AssignGroupToTimelineModal;
