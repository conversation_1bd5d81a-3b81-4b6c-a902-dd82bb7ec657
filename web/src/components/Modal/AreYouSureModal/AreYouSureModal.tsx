import React, { useState } from 'react';
import { Text } from '@/components/Texts/Text/Text';
import Button from '../../Button/Button';
import GenericModal from '../GenericModal';

type AreYouSureModal = {
  title: string;
  onClose: () => void;
  onConfirm: () => Promise<void>;

  heading?: string;
  targetObject?: React.ReactNode;
  description?: string;
  confirmButtonText?: string;

  children?: React.ReactNode;
  opened?: boolean;
};

export const AreYouSureModal = ({
  title,
  onClose,
  onConfirm,

  heading,
  targetObject = null,
  description,
  confirmButtonText = 'Delete',
  children,
  opened = false,
}: AreYouSureModal) => {
  const [loading, setLoading] = useState(false);

  const handleLoadingOnConfirm = async () => {
    setLoading(true);
    await onConfirm();
    setLoading(false);
  };

  return (
    <GenericModal title={title} onClose={onClose} loading={loading} opened={opened}>
      {children ? (
        children
      ) : (
        <div className="w-full h-full flex flex-col items-center justify-center px-[20%] text-center gap-[24px]">
          {heading && <Text size="xl">{heading}</Text>}
          {targetObject}
          {description && <Text size="sm">{description}</Text>}
        </div>
      )}
      <div className="flex flex-row gap-[8px] w-full mt-[24px]">
        <Button onClick={onClose} variant="secondary">
          Cancel
        </Button>
        <Button onClick={handleLoadingOnConfirm} variant="danger">
          {confirmButtonText}
        </Button>
      </div>
    </GenericModal>
  );
};
