import { Flex, Input, Pagination, Tabs } from '@mantine/core';
import Button from '../../Button/Button';
import GenericModal from '../GenericModal';
import AddPostTimelineCard from './AddPostTimelineCard';
import { useAddPostModal } from './useAddPostModal';

type AddPostModalProps = {
  onClose: () => void;
  opened: boolean;
};

const AddPostModal = ({ onClose, opened }: AddPostModalProps) => {
  const {
    loading,
    timelineSearch,
    timelineResponseObject,
    selectedTimelineItems,
    paginationObject,
    fetchedGroups,
    filterForm,

    setTimelinesToBeSent,
    closeModal,
    setTimelineSearch,
    searchTimeline,
    setSelectedTimelineItems,
    handlePageNavigation,
  } = useAddPostModal({ opened, onClose });

  const postCount = selectedTimelineItems.length;
  const sendButtonText =
    postCount === 0 ? 'Send' : `Send ${postCount} Post${postCount > 1 ? 's' : ''}`;

  return (
    <GenericModal
      title="Add Posts"
      onClose={closeModal}
      loading={loading}
      opened={opened}
      size="75%"
    >
      <Tabs
        defaultValue="add-post-all"
        w="100%"
        h="100%"
        color="violet"
        styles={{
          list: {
            flexWrap: 'nowrap',
            overflowY: 'scroll',
          },
        }}
        onChange={(value) => {
          if (value === 'add-post-all') {
            filterForm.reset();
          } else if (value === 'add-post-bookmark') {
            filterForm.setValues({
              bookmarkedOnly: true,
              groups: [],
            });
          } else {
            filterForm.setValues({
              bookmarkedOnly: false,
              groups: [Number(value)],
            });
          }
        }}
      >
        <Tabs.List grow>
          <Tabs.Tab value="add-post-all">All</Tabs.Tab>
          <Tabs.Tab value="add-post-bookmark">Bookmarked</Tabs.Tab>
          {fetchedGroups.map((item) => {
            return (
              <Tabs.Tab key={item.id} value={String(item.id)}>
                {item.name}
              </Tabs.Tab>
            );
          })}
        </Tabs.List>

        <Flex direction="column" align="center">
          <form
            className="flex flex-row gap-[20px] items-center justify-center"
            onSubmit={searchTimeline}
          >
            <Input
              placeholder="Search"
              type="text"
              defaultValue={timelineSearch}
              onChange={(event) => setTimelineSearch(event.target.value)}
              my={17}
              w="100%"
            />
            <Button variant="primary" type="submit" w="120px">
              Search
            </Button>
          </form>
          <Tabs.Panel value="add-post-all" w="100%">
            <div className="max-h-[350px] overflow-y-auto">
              <Flex direction="column" gap={24}>
                {timelineResponseObject?.items?.map((timeline) => {
                  const isSelected = selectedTimelineItems.some((t) => t.id === timeline?.id);
                  return (
                    <AddPostTimelineCard
                      key={timeline.id}
                      timeline={timeline}
                      isSelected={isSelected}
                      setSelectedTimelineItems={setSelectedTimelineItems}
                    />
                  );
                })}
              </Flex>
            </div>
          </Tabs.Panel>
          <Tabs.Panel value="add-post-bookmark" w="100%">
            <div className="max-h-[350px] overflow-y-auto">
              <Flex direction="column" gap={24}>
                {timelineResponseObject?.items?.map((timeline) => {
                  const isSelected = selectedTimelineItems.some((t) => t.id === timeline?.id);
                  return (
                    <AddPostTimelineCard
                      key={timeline.id}
                      timeline={timeline}
                      isSelected={isSelected}
                      setSelectedTimelineItems={setSelectedTimelineItems}
                    />
                  );
                })}
              </Flex>
            </div>
          </Tabs.Panel>
          {fetchedGroups.map((item) => {
            return (
              <Tabs.Panel key={item.id} value={String(item.id)} w="100%">
                <div className="max-h-[350px] overflow-y-auto">
                  <Flex direction="column" gap={24}>
                    {timelineResponseObject?.items?.map((timeline) => {
                      const isSelected = selectedTimelineItems.some((t) => t.id === timeline?.id);
                      return (
                        <AddPostTimelineCard
                          key={timeline.id}
                          timeline={timeline}
                          isSelected={isSelected}
                          setSelectedTimelineItems={setSelectedTimelineItems}
                        />
                      );
                    })}
                  </Flex>
                </div>
              </Tabs.Panel>
            );
          })}
          <Flex direction="row" mt={16} w="100%" justify="end" align="center">
            <Button
              variant="primary"
              type="submit"
              size="auto"
              onClick={() => {
                setTimelinesToBeSent(selectedTimelineItems);
                closeModal();
              }}
              disabled={postCount === 0}
            >
              {sendButtonText}
            </Button>
          </Flex>
          <Flex direction="row" w="100%" justify="center" align="center">
            <Pagination
              total={paginationObject.totalPages}
              value={paginationObject.page}
              onChange={handlePageNavigation}
              siblings={0}
              boundaries={1}
            />
          </Flex>
        </Flex>
      </Tabs>
    </GenericModal>
  );
};

export default AddPostModal;
