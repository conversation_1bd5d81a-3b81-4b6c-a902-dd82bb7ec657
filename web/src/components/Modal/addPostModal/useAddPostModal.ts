import { useEffect, useState } from 'react';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import { groupService } from '@/api/group/groupService';
import { SearchRequestQuery, searchService } from '@/api/search/searchService';
import { timelineService } from '@/api/timeline/timelineService';
import { useChatbotContext } from '@/context/ChatbotContext';
import { PaginationObjectProps } from '@/types/globalTypes';
import { PaginatedResponse } from '@/types/paginatedTypes';
import { TeamTypeProps } from '@/types/teamType';
import { TimelineFilterParam, TimelineProps } from '@/types/timelineTypes';

type UseTimelineProps = {
  onClose: () => void;
  opened: boolean;
};

interface AddPostFilterProps {
  groups: number[] | null;
  bookmarkedOnly: boolean | null;
}

export const useAddPostModal = ({ opened, onClose }: UseTimelineProps) => {
  const { setTimelinesToBeSent, timelinesToBeSent } = useChatbotContext();
  const filterForm = useForm<AddPostFilterProps>({
    initialValues: {
      groups: [],
      bookmarkedOnly: false,
    },
  });

  const [loading, setLoading] = useState(false);
  const [timelineResponseObject, setTimelineResponseObject] = useState<
    PaginatedResponse<TimelineProps>
  >({} as PaginatedResponse<TimelineProps>);
  const [selectedTimelineItems, setSelectedTimelineItems] = useState<TimelineProps[]>(
    timelinesToBeSent as TimelineProps[]
  );

  const [paginationObject, setPaginationObject] = useState<PaginationObjectProps>({
    page: 1,
    size: 10,
    total: 0,
    totalPages: 0,
  } as PaginationObjectProps);

  const [timelineSearch, setTimelineSearch] = useState('');
  const [submittedSearch, setSubmittedSearch] = useState<string>('');

  const [fetchedGroups, setFetchedGroups] = useState<TeamTypeProps[]>([]);

  const closeModal = () => {
    setSelectedTimelineItems([]);
    setTimelineSearch('');
    onClose();
  };

  const initialize = async () => {
    setLoading(true);
    filterForm.reset();
    try {
      const groupsResponse = await groupService.fetchGroups();
      setFetchedGroups(groupsResponse.data.items);
    } catch {
      notifications.show({
        title: 'There was an error initializing the modal.',
        message: undefined,
        autoClose: 3000,
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  const handlePageNavigation = (page: number) => {
    setPaginationObject((prev) => ({
      ...prev,
      page,
    }));
  };

  const searchTimeline = (event?: React.FormEvent<HTMLFormElement>) => {
    event?.preventDefault();
    setSubmittedSearch(timelineSearch);
    setPaginationObject((p) => ({ ...p, page: 1 }));
  };

  const fetchTimelineItems = async () => {
    setLoading(true);

    const fetchParams = {
      page: paginationObject.page,
      size: paginationObject.size,
      sharedWithGroupIds: filterForm.values.groups,
      bookmarkedOnly: filterForm.values.bookmarkedOnly,
    } as TimelineFilterParam;

    const searchParams = {
      page: paginationObject.page,
      sharedWithGroupIds: filterForm.values?.groups?.[0] || undefined,
      bookmarkedOnly: filterForm.values?.bookmarkedOnly,
    } as SearchRequestQuery;

    try {
      const response = submittedSearch
        ? await searchService.search(submittedSearch, searchParams)
        : await timelineService.fetchTimeline(fetchParams);
      setTimelineResponseObject(response as PaginatedResponse<TimelineProps>);
      setPaginationObject((prev) => ({
        ...prev,
        total: response.total,
        totalPages: response.totalPages,
      }));
    } catch (error) {
      notifications.show({
        title: 'Error fetching timeline',
        message: 'There was an error fetching timeline',
        autoClose: 3000,
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    initialize();
  }, []);

  useEffect(() => {
    setSelectedTimelineItems(timelinesToBeSent);
  }, [opened]);

  useEffect(() => {
    setPaginationObject((prev) => ({
      ...prev,
      page: 1,
    }));
  }, [filterForm.values.groups, filterForm.values.bookmarkedOnly]);

  useEffect(() => {
    fetchTimelineItems();
  }, [
    paginationObject.page,
    paginationObject.size,
    submittedSearch,
    filterForm.values.groups,
    filterForm.values.bookmarkedOnly,
  ]);

  return {
    loading,
    timelineSearch,
    timelineResponseObject,
    selectedTimelineItems,
    paginationObject,
    fetchedGroups,
    filterForm,

    setTimelinesToBeSent,
    closeModal,
    setTimelineSearch,
    searchTimeline,
    setSelectedTimelineItems,
    handlePageNavigation,
  };
};
