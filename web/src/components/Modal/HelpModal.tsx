import { Accordion, Flex, Tabs } from '@mantine/core';
import { Heading } from '@/components/Texts/Heading/Heading';
import { Text } from '@/components/Texts/Text/Text';
import { useHelpModal } from '@/context/HelpModalContext';
import { helpContent } from '@/dictionary/helpContent';
import GenericModal from './GenericModal';

type HelpModalProps = {
  onClose: () => void;
  opened: boolean;
  activeTab?: string;
};

const HelpModal = ({ onClose, opened }: HelpModalProps) => {
  const { activeTab } = useHelpModal();

  return (
    <GenericModal title="Help & Documentation" onClose={onClose} opened={opened} size="90%">
      <Tabs defaultValue={activeTab} w="100%" h="100%" color="violet">
        <Tabs.List>
          <Tabs.Tab value="getting-started">Getting Started</Tabs.Tab>
          <Tabs.Tab value="timeline">Timeline</Tabs.Tab>
          <Tabs.Tab value="analytics">Analytics</Tabs.Tab>
          <Tabs.Tab value="topics">Topics</Tabs.Tab>
          <Tabs.Tab value="chat">AI Chat</Tabs.Tab>
          <Tabs.Tab value="faq">FAQ & Definitions</Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="getting-started" className="max-h-[70vh] overflow-y-auto">
          <Flex direction="column" gap={24} className="p-4">
            <div>
              <Heading tagLevel="3" className="mb-3">
                {helpContent.gettingStarted.title}
              </Heading>
              <Text size="md" className="mb-4">
                {helpContent.gettingStarted.description}
              </Text>
            </div>

            {Object.entries(helpContent.gettingStarted.sections).map(([key, section]) => (
              <div key={key}>
                <Heading tagLevel="4" className="mb-2">
                  {section.title}
                </Heading>
                <Text size="md" className="mb-4">
                  {section.description}
                </Text>
                {'image' in section && section.image && (
                  <Flex justify="center" className="mb-4">
                    <img
                      src={section.image}
                      alt={`${section.title} interface`}
                      className="max-w-[60%] h-auto rounded-lg shadow-md"
                    />
                  </Flex>
                )}
              </div>
            ))}
          </Flex>
        </Tabs.Panel>

        <Tabs.Panel value="timeline" className="max-h-[70vh] overflow-y-auto">
          <Flex direction="column" gap={24} className="p-4">
            <div>
              <Heading tagLevel="3" className="mb-3">
                {helpContent.timeline.title}
              </Heading>
              <Text size="md" className="mb-4">
                {helpContent.timeline.description}
              </Text>
              <Flex justify="center" className="mb-4">
                <img
                  src={helpContent.timeline.image}
                  alt="Timeline interface"
                  className="max-w-[60%] h-auto rounded-lg shadow-md"
                />
              </Flex>
            </div>

            {Object.entries(helpContent.timeline.sections).map(([key, section]) => (
              <div key={key}>
                <Heading tagLevel="4" className="mb-2">
                  {section.title}
                </Heading>
                <Text size="md" className="mb-4">
                  {section.description}
                </Text>
                {'image' in section && section.image && (
                  <Flex justify="center" className="mb-4">
                    <img
                      src={section.image}
                      alt={`${section.title}`}
                      className="max-w-[60%] h-auto rounded-lg shadow-md"
                    />
                  </Flex>
                )}
              </div>
            ))}
          </Flex>
        </Tabs.Panel>

        <Tabs.Panel value="analytics" className="max-h-[70vh] overflow-y-auto">
          <Flex direction="column" gap={24} className="p-4">
            <div>
              <Heading tagLevel="3" className="mb-3">
                {helpContent.analytics.title}
              </Heading>
              <Text size="md" className="mb-4">
                {helpContent.analytics.description}
              </Text>
              <Flex justify="center" className="mb-4">
                <img
                  src={helpContent.analytics.image}
                  alt="Analytics interface"
                  className="max-w-[60%] h-auto rounded-lg shadow-md"
                />
              </Flex>
            </div>

            {Object.entries(helpContent.analytics.sections).map(([key, section]) => (
              <div key={key}>
                <Heading tagLevel="4" className="mb-2">
                  {section.title}
                </Heading>
                <Text size="md" className="mb-4">
                  {section.description}
                </Text>
                {'image' in section && section.image && (
                  <Flex justify="center" className="mb-4">
                    <img
                      src={section.image}
                      alt={`${section.title}`}
                      className="max-w-[60%] h-auto rounded-lg shadow-md"
                    />
                  </Flex>
                )}
              </div>
            ))}
          </Flex>
        </Tabs.Panel>

        <Tabs.Panel value="topics" className="max-h-[70vh] overflow-y-auto">
          <Flex direction="column" gap={24} className="p-4">
            <div>
              <Heading tagLevel="3" className="mb-3">
                {helpContent.topics.title}
              </Heading>
              <Text size="md" className="mb-4">
                {helpContent.topics.description}
              </Text>
              <Flex justify="center" className="mb-4">
                <img
                  src={helpContent.topics.image}
                  alt="Topic & Team Management interface"
                  className="max-w-[60%] h-auto rounded-lg shadow-md"
                />
              </Flex>
            </div>

            {Object.entries(helpContent.topics.sections).map(([key, section]) => (
              <div key={key}>
                <Heading tagLevel="4" className="mb-2">
                  {section.title}
                </Heading>
                <Text size="md" className="mb-4">
                  {section.description}
                </Text>
                {'image' in section && section.image && (
                  <Flex justify="center" className="mb-4">
                    <img
                      src={section.image}
                      alt={`${section.title}`}
                      className="max-w-[60%] h-auto rounded-lg shadow-md"
                    />
                  </Flex>
                )}
              </div>
            ))}
          </Flex>
        </Tabs.Panel>

        <Tabs.Panel value="chat" className="max-h-[70vh] overflow-y-auto">
          <Flex direction="column" gap={24} className="p-4">
            <div>
              <Heading tagLevel="3" className="mb-3">
                {helpContent.chat.title}
              </Heading>
              <Text size="md" className="mb-4">
                {helpContent.chat.description}
              </Text>
              <Flex justify="center" className="mb-4">
                <img
                  src={helpContent.chat.image}
                  alt="AI Chatbot interface"
                  className="max-w-[60%] h-auto rounded-lg shadow-md"
                />
              </Flex>
            </div>

            {Object.entries(helpContent.chat.sections).map(([key, section]) => (
              <div key={key}>
                <Heading tagLevel="4" className="mb-2">
                  {section.title}
                </Heading>
                <Text size="md" className="mb-4">
                  {section.description}
                </Text>
                {'image' in section && section.image && (
                  <Flex justify="center" className="mb-4">
                    <img
                      src={section.image}
                      alt={`${section.title}`}
                      className="max-w-[60%] h-auto rounded-lg shadow-md"
                    />
                  </Flex>
                )}
              </div>
            ))}
          </Flex>
        </Tabs.Panel>

        <Tabs.Panel value="faq" className="max-h-[70vh] overflow-y-auto">
          <Flex direction="column" gap={24} className="p-4">
            <div>
              <Heading tagLevel="3" className="mb-3">
                {helpContent.definitions.title}
              </Heading>
              <Text size="md" className="mb-4">
                {helpContent.definitions.description}
              </Text>
            </div>

            {Object.entries(helpContent.definitions.terms).map(([key, term]) => (
              <div key={key}>
                <Heading tagLevel="4" className="mb-2">
                  {term.title}
                </Heading>
                <Text size="md" className="mb-4">
                  {term.description}
                </Text>
              </div>
            ))}

            <div>
              <Heading tagLevel="3" className="mb-3">
                {helpContent.faq.title}
              </Heading>
              <Accordion variant="contained" className="mt-4">
                {helpContent.faq.questions.map((faqItem) => (
                  <Accordion.Item key={faqItem.id} value={faqItem.id}>
                    <Accordion.Control>
                      <Text size="md" bold>
                        {faqItem.question}
                      </Text>
                    </Accordion.Control>
                    <Accordion.Panel>
                      <Text size="sm">{faqItem.answer}</Text>
                    </Accordion.Panel>
                  </Accordion.Item>
                ))}
              </Accordion>
            </div>
          </Flex>
        </Tabs.Panel>
      </Tabs>
    </GenericModal>
  );
};

export default HelpModal;
