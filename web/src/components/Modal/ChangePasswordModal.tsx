import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Alert, Flex, PasswordInput } from '@mantine/core';
import { useForm } from '@mantine/form';
import { authService } from '@/api/auth/authService';
import { userService } from '@/api/user/userService';
import { useAppContext } from '@/context/AppContext';
import Button from '../Button/Button';
import { Icon } from '../icon/Icon';
import GenericModal from './GenericModal';

type ChangePasswordModalProps = {
  opened: boolean;
  onClose: () => void;
};

const ChangePasswordModal = ({ onClose, opened }: ChangePasswordModalProps) => {
  const [loading, setLoading] = useState(false);
  const [showError, setShowError] = useState({
    show: false,
    message: '',
  });

  const navigate = useNavigate();
  const { initialize } = useAppContext();

  const form = useForm<{
    currentPassword: string;
    newPassword: string;
    confirmNewPassword: string;
  }>({
    initialValues: {
      currentPassword: '',
      newPassword: '',
      confirmNewPassword: '',
    },
    validate: {
      currentPassword: (value) => (value.length === 0 ? 'Current Password is required' : null),
      newPassword: (value, values) =>
        value.length === 0
          ? 'Password is required'
          : value !== values.confirmNewPassword
            ? 'Passwords do not match'
            : null,
      confirmNewPassword: (value, values) =>
        value.length === 0
          ? 'Confirm Password is required'
          : value !== values.newPassword
            ? 'Passwords do not match'
            : null,
    },
  });

  const resetPassword = async () => {
    setLoading(true);
    try {
      await userService.updatePassword(form.values.currentPassword, form.values.newPassword);
      onClose();
      await authService.logout();
      initialize();
      navigate('/login');
    } catch (error) {
      setShowError({
        show: true,
        message:
          'Password change failed. Please ensure your current password is correct and your new password is strong (min. 8 characters, including upper & lower case letters, a number, and a symbol).',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <GenericModal title="Change Password" onClose={onClose} loading={loading} opened={opened}>
      <form onSubmit={form.onSubmit(resetPassword)} noValidate>
        <Flex direction="column" gap={24}>
          <PasswordInput
            withAsterisk
            label="Current Password"
            placeholder="Current Password"
            leftSection={<Icon iconCategory="input" iconType="lock" />}
            {...form.getInputProps('currentPassword')}
          />
          <PasswordInput
            withAsterisk
            label="New Password"
            placeholder="New Password"
            leftSection={<Icon iconCategory="input" iconType="lock" />}
            {...form.getInputProps('newPassword')}
          />
          <PasswordInput
            withAsterisk
            label="Confirm New Password"
            placeholder="Confirm New Password"
            leftSection={<Icon iconCategory="input" iconType="lock" />}
            {...form.getInputProps('confirmNewPassword')}
          />
          {showError.show && (
            <Alert variant="light" color="red">
              {showError.message}
            </Alert>
          )}
          <Flex gap={8}>
            <Button variant="secondary" onClick={onClose}>
              Cancel
            </Button>
            <Button variant="primary" type="submit">
              Change
            </Button>
          </Flex>
        </Flex>
      </form>
    </GenericModal>
  );
};

export default ChangePasswordModal;
