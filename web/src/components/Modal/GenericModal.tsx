import React from 'react';
import { LoadingOverlay, Modal as MantineModal } from '@mantine/core';

export type GenericModalProps = {
  children: React.ReactNode;
  title: string;
  onClose?: () => void;
  onExitTransitionEnd?: () => void;
  opened?: boolean;
  loading?: boolean;
  size?: string;
  disableScroll?: boolean;
};

const GenericModal = ({
  children,
  title,
  onClose = () => {},
  onExitTransitionEnd = () => {},
  opened = true,
  loading = false,
  size = 'md',
  disableScroll = false,
}: GenericModalProps) => {
  return (
    <MantineModal
      opened={opened}
      onClose={onClose}
      title={title}
      styles={{
        title: {
          fontWeight: 700,
          fontSize: '16px',
          lineHeight: '18px',
        },
      }}
      classNames={{
        content: disableScroll ? '!overflow-auto md:!overflow-hidden' : '!overflow-auto',
      }}
      onExitTransitionEnd={onExitTransitionEnd}
      size={size}
    >
      <div style={{ position: 'relative', minHeight: '100px' }}>
        {children}
        <LoadingOverlay visible={loading} loaderProps={{ color: 'violet' }} />
      </div>
    </MantineModal>
  );
};

export default GenericModal;
