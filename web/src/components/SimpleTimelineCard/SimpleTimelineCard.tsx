import { ReactNode } from 'react';
import { Flex, Tooltip } from '@mantine/core';
import { Text } from '@/components/Texts/Text/Text';
import { getScoreColors } from '@/helpers/getScoreColors';
import { getCompactNumber } from '@/helpers/numbers';
import { TimelineProps } from '@/types/timelineTypes';
import { COLOR_MAP } from '../../pages/TimelinePage/constants';

interface SimpleTimelineCardProps {
  timeline: TimelineProps;
  children?: ReactNode;
  className?: string;
}

const SimpleTimelineCard = ({ timeline, children, className = '' }: SimpleTimelineCardProps) => {
  const { type } = timeline;

  return (
    <div
      className={`rounded-[4px] border-0 border-t-[4px] border py-[16px] px-[24px] ${className}`}
      style={{
        backgroundColor: COLOR_MAP[type].background,
        borderTopColor: COLOR_MAP[type].border,
      }}
    >
      <Flex direction="row" gap={20}>
        {children}
        <Flex direction="column" gap={16} className="w-full">
          <Text size="lg">{timeline.summary}</Text>
          <Flex direction="row" justify="space-between" align="end">
            <Flex direction="column" gap={16} className="w-full">
              <Flex direction="row" gap={8}>
                {type === 'YOUTUBE_VIDEO' || type === 'YOUTUBE_COMMENT' ? (
                  <img src="/icons/sourceIcons/youtubeIcon.svg" alt="youtube icon" />
                ) : (
                  <img src="/icons/sourceIcons/redditIcon.svg" alt="reddit icon" />
                )}
              </Flex>
              <Flex direction="row" justify="space-between" align="center" w="100%">
                <Flex direction="row" gap={16} align="center">
                  <Tooltip
                    disabled={!timeline.sentimentReasoning}
                    label={timeline.sentimentReasoning}
                    withArrow
                    multiline
                    styles={{
                      tooltip: {
                        maxWidth: '16rem',
                      },
                    }}
                  >
                    <Flex
                      className={`${getScoreColors(timeline.sentiment === -1 ? 50 : (timeline.sentiment ?? 50))} min-w-[50px] min-h-[50px] p-[12px] rounded-full items-center justify-center text-white`}
                    >
                      <Text size="xl" bold>
                        {timeline.sentiment === -1 ? 50 : (timeline.sentiment ?? 50)}
                      </Text>
                    </Flex>
                  </Tooltip>
                  <Flex direction="row" gap={4} align="center">
                    <img src="/icons/timelineIcons/likeIcon.svg" alt="like icon" />
                    <Text size="lg" bold>
                      {getCompactNumber(timeline?.voteScore ?? 0)}
                    </Text>
                  </Flex>
                  <Flex direction="row" gap={4} align="center">
                    <img src="/icons/timelineIcons/commentIcon.svg" alt="comment icon" />
                    <Text size="lg" bold>
                      {getCompactNumber(timeline.commentCount ?? timeline.replies?.count ?? 0)}
                    </Text>
                  </Flex>
                </Flex>
              </Flex>
            </Flex>
          </Flex>
        </Flex>
      </Flex>
    </div>
  );
};

export default SimpleTimelineCard;
