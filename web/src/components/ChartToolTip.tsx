import { useEffect, useState } from 'react';
import { getFilteredChartTooltipPayload } from '@mantine/charts';
import { Flex, Loader, Paper } from '@mantine/core';
import { UseFormReturnType } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import { analyticsService } from '@/api/analytics/analyticsService';
import { Text } from '@/components/Texts/Text/Text';
import { sourceDictionary } from '@/dictionary/sourceDictionary';
import { getDateRangeParams } from '@/helpers/dateUtils';
import TrendsViewHistogram from '@/pages/AnalyticsPage/components/OverallTrendsView/TrendsViewHistogram';
import { ScatterPlotToolTipProps } from '@/types/analyticsTypes';
import { FilterForm } from '@/types/timelineTypes';

interface ChartTooltipProps {
  payload?: Record<string, any>[];
  filterForm?: UseFormReturnType<FilterForm>;
}

export function ChartTooltip({ payload, filterForm }: ChartTooltipProps) {
  if (!payload || !Array.isArray(payload) || payload.length === 0) {
    return null;
  }

  const [loading, setLoading] = useState(false);
  const [toolTipData, setToolTipData] = useState<ScatterPlotToolTipProps>(
    {} as ScatterPlotToolTipProps
  );
  const filtered = getFilteredChartTooltipPayload(payload ?? []);
  const point = filtered[0].payload as {
    x: number;
    y: number;
    narrativeId: number;
  };

  if (!point) {
    return null;
  }

  const fetchToolTipHistogram = async () => {
    setLoading(true);
    try {
      const { sentiments, sources, multiSelectTopicIds, multiSelectNarrativeAspects } =
        filterForm?.values ?? {};

      const { dateFrom, dateTo } = getDateRangeParams(filterForm?.values);
      const params = {
        dateFrom,
        dateTo,
        topicIds: multiSelectTopicIds?.map((t) => Number(t)),
        sentiments: sentiments?.map((s) => s.id),
        sources: sources ? [sourceDictionary[sources as keyof typeof sourceDictionary]] : undefined,
        aspects: multiSelectNarrativeAspects,
      };

      const response = await analyticsService.getNarrativeComparisonPopover(
        point.narrativeId,
        params
      );
      setToolTipData(response as ScatterPlotToolTipProps);
    } catch (error) {
      notifications.show({
        title: 'Error fetching tooltip histogram data',
        message: 'There was an error fetching tooltip histogram data',
        autoClose: 3000,
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchToolTipHistogram();
  }, [point.narrativeId, filterForm?.values]);

  return (
    <Paper px="md" py="sm" withBorder shadow="md" radius="md" className="max-w-[200px]">
      {loading ? (
        <Loader color="violet" />
      ) : (
        <Flex direction="column" gap={13}>
          <Flex direction="column" gap={5}>
            <Flex>
              <Text size="lg">
                Topic: <span className="font-[700]">{toolTipData.topicName}</span>
              </Text>
            </Flex>
            <Flex>
              <Text size="lg">
                Narrative: <span className="font-[700]">{toolTipData.summary}</span>
              </Text>
            </Flex>
          </Flex>
          <TrendsViewHistogram trendHistogramItem={toolTipData} />
        </Flex>
      )}
    </Paper>
  );
}
