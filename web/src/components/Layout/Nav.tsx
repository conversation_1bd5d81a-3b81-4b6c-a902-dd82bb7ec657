import { Link, useNavigate } from 'react-router-dom';
import { authService } from '@/api/auth/authService';
import { Text } from '@/components/Texts/Text/Text';
import { useAppContext } from '@/context/AppContext';
import { useHelpModal } from '@/context/HelpModalContext';
import Button from '../Button/Button';
import { Icon } from '../icon/Icon';
import HelpModal from '../Modal/HelpModal';

type NavMenuOption = {
  label: string;
  iconType: 'timeline' | 'analytics' | 'topic-management' | 'user-management' | 'settings';
};

interface NavProps {
  mobileMenuOpen: boolean;
}

const navMenuOptions = [
  { label: 'Timeline', iconType: 'timeline' },
  { label: 'Analytics', iconType: 'analytics' },
  { label: 'Topic & Team Management', iconType: 'topic-management' },
  { label: 'User Management', iconType: 'user-management' },
  { label: 'Settings', iconType: 'settings' },
  { label: 'Change tenant', iconType: 'change-tenant' },
] as NavMenuOption[];

const Nav = ({ mobileMenuOpen }: NavProps) => {
  const navigate = useNavigate();
  const { initialize, userInfo } = useAppContext();
  const { isOpen: helpModalOpen, closeHelpModal, openHelpModal } = useHelpModal();
  const visibleNavMenuOptions = navMenuOptions.filter((option) => {
    if (option.label === 'Change tenant') {
      return (userInfo?.tenants?.length ?? 0) > 1;
    }
    return option.iconType !== 'user-management' || userInfo?.isAdmin;
  });

  return (
    <>
      <div
        className={`
          bg-white z-20
          fixed top-0
           h-screen
          pt-[76px] px-[12px] py-[12px]
          border border-[#868E96]
          w-[75%] md:left-0 md:w-[300px]
          duration-300 ease-in-out md:duration-0
          ${mobileMenuOpen ? 'right-0' : 'right-[-75%]'}`}
      >
        <div className="flex flex-col h-full">
          <div className="flex flex-col">
            {visibleNavMenuOptions.map((option, index) => (
              <Link
                key={index}
                to={`/${option.iconType}`}
                className="
                group cursor-pointer
                px-[14px] py-[16px]
                text-[14px] leading-[16px] font-normal
                bg-transparent
                rounded-sm hover:bg-[#7950F21A]
              "
              >
                <NavbarButtonContent menu={option} />
              </Link>
            ))}
          </div>

          <div className="flex flex-col mt-auto">
            <Button
              onClick={() => openHelpModal()}
              className="
              group cursor-pointer
              px-[14px] py-[16px]
              text-[14px] leading-[16px] font-normal
              bg-transparent
              rounded-sm hover:bg-[#7950F21A]
            "
            >
              <div className="flex items-center gap-[12px]">
                <Text size="md" className="text-[#868E96] group-hover:text-[#7950F2]">
                  Help
                </Text>
              </div>
            </Button>
            <div className="flex flex-col w-full border-t border-[#DEE2E6] pt-[12px]">
              <Button
                onClick={async () => {
                  await authService.logout();
                  initialize();
                  navigate('/login', { replace: true, state: { from: location.pathname } });
                }}
                className="
                group cursor-pointer
                px-[14px] py-[16px]
                text-[14px] leading-[16px] font-normal
                bg-transparent
                rounded-sm hover:bg-[#7950F21A]
              "
              >
                <NavbarButtonContent
                  menu={{
                    label: 'Logout',
                    iconType: 'settings',
                  }}
                />
              </Button>
            </div>
          </div>
        </div>
      </div>
      <HelpModal opened={helpModalOpen} onClose={closeHelpModal} />
    </>
  );
};

const NavbarButtonContent = ({ menu }: { menu: NavMenuOption }) => {
  return (
    <div className="flex items-center gap-[12px]">
      <Icon
        iconCategory="navbar"
        iconType={menu.iconType}
        className="w-4 h-4 group-hover:text-[#7950F2]"
      />
      <Text size="md" className="group-hover:text-[#7950F2]">
        {menu.label}
      </Text>
    </div>
  );
};

export default Nav;
