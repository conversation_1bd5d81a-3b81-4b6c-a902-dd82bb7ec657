import { ReactNode } from 'react';
import { Flex, useMantineTheme } from '@mantine/core';
import { Text } from '@/components/Texts/Text/Text';
import { Heading } from '../Texts/Heading/Heading';

const UnauthenticatedLayout = ({ children }: { children: ReactNode }) => {
  const truthKeepTheme = useMantineTheme().other.truthKeep;
  return (
    <Flex align="center" justify="between" h="100%" direction={{ base: 'column', sm: 'row' }}>
      <div
        className="w-full md:min-w-[400px] lg:min-w-[520px] md:h-full"
        style={{ backgroundColor: truthKeepTheme.secondaryBackgroundColor }}
      >
        <Flex
          direction="column"
          align="center"
          justify="end"
          gap={32}
          h={{ base: 'auto', sm: '50%' }}
          pb={{ base: 28, sm: 20 }}
          pt={{ base: 80, sm: 0 }}
        >
          <Heading tagLevel="2" style={{ color: truthKeepTheme.textColor }}>
            Welcome to Truthkeep
          </Heading>
          <Text className="text-center" style={{ color: truthKeepTheme.textColor }}>
            Stay up to date with the latest
            <br />
            hardware conversations happening
            <br />
            across the internet
          </Text>
        </Flex>
        <Flex align="end" justify="center" className="h-[74px] md:h-[50%]">
          <div className="overflow-hidden relative h-full w-full">
            <img
              src="/icons/tkLogoLogin.svg"
              alt="logo"
              className="absolute bottom-[-102px] md:bottom-0 left-1/2 -translate-x-1/2"
            />
          </div>
        </Flex>
      </div>
      {children}
    </Flex>
  );
};

export default UnauthenticatedLayout;
