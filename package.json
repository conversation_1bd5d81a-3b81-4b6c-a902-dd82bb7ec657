{"name": "truthkeep-microchip", "version": "1.0.0", "description": "⚠️ This repo is a work in progress ⚠️", "main": "index.js", "directories": {"doc": "docs", "test": "test"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "prepare": "husky"}, "lint-staged": {"api/**/*.ts": ["npm run --prefix api format", " npm run --prefix api lint"], "web/**": ["npm run --prefix web prettier", "npm run --prefix web eslint"], "web/**/*.css": "npm run --prefix web stylelint"}, "repository": {"type": "git", "url": "git+https://github.com/truthkeep/truthkeep-microchip.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/truthkeep/truthkeep-microchip/issues"}, "homepage": "https://github.com/truthkeep/truthkeep-microchip#readme", "devDependencies": {"husky": "^9.1.7", "lint-staged": "^15.4.3"}}