# Backing services are created using Github Actions service containers
# https://docs.github.com/en/actions/use-cases-and-examples/using-containerized-services/about-service-containers
# Using docker containers alongside service containers is poorly documented
# This discussion explains how the networking is set up: https://github.com/orgs/community/discussions/26094

name: E2E Tests

on:
  push:
    branches: [main, staging]
  pull_request:
    branches: [main, staging, development]

jobs:
  e2e-test:
    runs-on: ubuntu-latest

    services:
      db:
        image: pgvector/pgvector:pg17
        env:
          POSTGRES_USER: root
          POSTGRES_PASSWORD: strongPASS
      redis:
        image: redis:latest
      local-ses:
        image: kamranahmed/local-ses:latest

    steps:
      - uses: actions/checkout@v3
      - uses: docker/setup-buildx-action@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: "22"
          cache: "npm"

      - name: Prepare backend env
        run: cp api/.env.example api/.env

      - name: Prepare test env
        run: cp test/.env.example test/.env

      - name: Build backend Docker image
        uses: docker/build-push-action@v5
        with:
          context: api
          file: api/docker/Dockerfile
          tags: api:latest
          push: false
          load: true
          cache-from: type=gha,scope=api-image
          cache-to: type=gha,mode=min,scope=api-image

      - name: Run backend Docker container
        run: >
          docker run -d \
              --env-file ./api/.env \
              --env DATABASE_URL=********************************** \
              --env DATABASE_URL_APP_USER=************************************** \
              --env REDIS_HOST=redis \
              --env SES_ENDPOINT=http://local-ses:8282 \
              --name api \
              --network=${{ job.container.network }} \
              api:latest

      - name: Add seed data for testing
        run: docker exec api npm run prisma:seed:test

      - name: Build frontend Docker image
        uses: docker/build-push-action@v5
        with:
          context: web
          file: web/docker/Dockerfile
          tags: web:latest
          push: false
          load: true
          cache-from: type=gha,scope=web-image
          cache-to: type=gha,mode=min,scope=web-image

      - name: Run frontend Docker container
        run: >
          docker run -d \
            --env API_HOST=http://api:3000 \
            --name web \
            --network=${{ job.container.network }} \
            -p 4173:4173 \
            web:latest

      - name: Build playwright Docker image
        uses: docker/build-push-action@v5
        with:
          context: test
          file: test/docker/Dockerfile
          tags: e2e-test:latest
          push: false
          load: true
          cache-from: type=gha,scope=test-image
          cache-to: type=gha,mode=min,scope=test-image

      - name: Run playwright Docker container
        timeout-minutes: 5
        run: >
          docker run \
            --env BASE_URL=http://web:4173 \
            --env LOCAL_SES_HOST=http://local-ses:8282 \
            --name e2e-test-container \
            --network=${{ job.container.network }} \
            e2e-test:latest
      - name: Show backend logs
        if: always()
        run: |
          echo "::group::Backend logs"
          docker logs --timestamps api
          echo "::endgroup::"
