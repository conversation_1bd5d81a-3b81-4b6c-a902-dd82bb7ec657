name: Deploy truthkeep-microchip aws

on:
  push:
    branches:
      - development
      - staging
      - main

permissions:
  id-token: write 
  contents: read     

env:
  AWS_REGION: ${{ vars.AWS_REGION }}
  ROLE_ARN: ${{ vars.ROLE_ARN }}
  CONTAINER_NAME: ${{ vars.CONTAINER_NAME }}

jobs:
  set-vars:
    name: Set Stage-Specific Vars
    runs-on: ubuntu-latest
    timeout-minutes: 10
    outputs:
      bucket: ${{ steps.vars.outputs.bucket }}
      cloudfront: ${{ steps.vars.outputs.cloudfront }}
      ecs_cluster: ${{ steps.vars.outputs.cluster }}
      ecs_service: ${{ steps.vars.outputs.service }}
      ecr_repo: ${{ steps.vars.outputs.ecr_repo }}
      ecs_family: ${{ steps.vars.outputs.family }}
    steps:
      - name: Set outputs based on branch
        id: vars
        run: |
          if [ "${{ github.ref_name }}" = "development" ]; then
            echo "bucket=${{ vars.S3_BUCKET_NAME_DEVELOPMENT }}" >> $GITHUB_OUTPUT
            echo "cloudfront=${{ vars.CLOUDFRONT_DISTRIBUTION_ID_DEVELOPMENT }}" >> $GITHUB_OUTPUT
            echo "cluster=${{ vars.ECS_CLUSTER_NAME_DEVELOPMENT }}" >> $GITHUB_OUTPUT
            echo "service=${{ vars.ECS_SERVICE_NAME_DEVELOPMENT }}" >> $GITHUB_OUTPUT
            echo "family=${{ vars.ECS_TASK_DEF_FAMILY_DEVELOPMENT }}" >> $GITHUB_OUTPUT
            echo "ecr_repo=${{ vars.ECR_REPO_NAME_DEVELOPMENT }}" >> $GITHUB_OUTPUT
          elif [ "${{ github.ref_name }}" = "staging" ]; then
            echo "bucket=${{ vars.S3_BUCKET_NAME_STAGING }}" >> $GITHUB_OUTPUT
            echo "cloudfront=${{ vars.CLOUDFRONT_DISTRIBUTION_ID_STAGING }}" >> $GITHUB_OUTPUT
            echo "cluster=${{ vars.ECS_CLUSTER_NAME_STAGING }}" >> $GITHUB_OUTPUT
            echo "service=${{ vars.ECS_SERVICE_NAME_STAGING }}" >> $GITHUB_OUTPUT
            echo "family=${{ vars.ECS_TASK_DEF_FAMILY_STAGING }}" >> $GITHUB_OUTPUT
            echo "ecr_repo=${{ vars.ECR_REPO_NAME_STAGING }}" >> $GITHUB_OUTPUT
          else
            echo "bucket=${{ vars.S3_BUCKET_NAME_PRODUCTION }}" >> $GITHUB_OUTPUT
            echo "cloudfront=${{ vars.CLOUDFRONT_DISTRIBUTION_ID_PRODUCTION }}" >> $GITHUB_OUTPUT
            echo "cluster=${{ vars.ECS_CLUSTER_NAME_PRODUCTION }}" >> $GITHUB_OUTPUT
            echo "service=${{ vars.ECS_SERVICE_NAME_PRODUCTION }}" >> $GITHUB_OUTPUT
            echo "family=${{ vars.ECS_TASK_DEF_FAMILY_PRODUCTION }}" >> $GITHUB_OUTPUT
            echo "ecr_repo=${{ vars.ECR_REPO_NAME_PRODUCTION }}" >> $GITHUB_OUTPUT
          fi

  frontend:
    name: Deploy Frontend
    runs-on: ubuntu-latest
    timeout-minutes: 10
    needs: set-vars

    steps:
      - uses: actions/checkout@v3

      - name: Configure AWS
        uses: aws-actions/configure-aws-credentials@v3
        with:
          role-to-assume: ${{ env.ROLE_ARN }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Build Frontend
        working-directory: web
        run: |
          npm ci
          npm run build

      - name: Upload to S3
        run: |
          aws s3 sync web/dist s3://${{ needs.set-vars.outputs.bucket }} --delete

      - name: Invalidate CloudFront
        run: |
          aws cloudfront create-invalidation \
            --distribution-id ${{ needs.set-vars.outputs.cloudfront }} \
            --paths "/*"

  backend:
    name: Deploy Backend
    runs-on: ubuntu-latest
    timeout-minutes: 10
    needs: set-vars

    steps:
      - uses: actions/checkout@v3

      - name: Configure AWS
        uses: aws-actions/configure-aws-credentials@v3
        with:
          role-to-assume: ${{ env.ROLE_ARN }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Log in to ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Build & Push Backend Docker Image
        run: |
          # shorthand vars
          REG="${{ steps.login-ecr.outputs.registry }}"
          REPO="${{ needs.set-vars.outputs.ecr_repo }}"
          SHA="${{ github.sha }}"

          docker build \
            -t $REG/$REPO:latest \
            -t $REG/$REPO:$SHA \
            -f api/docker/Dockerfile api/
          docker push $REG/$REPO:$SHA

      - name: Register new task definition
        id: register-td
        run: |
          FAMILY="${{ needs.set-vars.outputs.ecs_family }}"
          NEW_IMAGE="${{ steps.login-ecr.outputs.registry }}/${{ needs.set-vars.outputs.ecr_repo }}:${{ github.sha }}"

          # pull current active task-def JSON
          aws ecs describe-task-definition --task-definition "$FAMILY" \
            --query 'taskDefinition' --output json > td.json

          # strip read-only fields & inject new image
          jq --arg IMG "$NEW_IMAGE" '
            del(.taskDefinitionArn, .revision, .status,
                .registeredAt, .registeredBy, .compatibilities,
                .requiresAttributes, .inferenceAccelerators) |
            .containerDefinitions[0].image = $IMG
          ' td.json > td-new.json

          NEW_ARN=$(aws ecs register-task-definition \
                      --cli-input-json file://td-new.json \
                      --query 'taskDefinition.taskDefinitionArn' \
                      --output text)

          echo "new_arn=$NEW_ARN" >> "$GITHUB_OUTPUT"
      - name: Update ECS Service to new revision
        run: |
          aws ecs update-service \
            --cluster  ${{ needs.set-vars.outputs.ecs_cluster }} \
            --service  ${{ needs.set-vars.outputs.ecs_service }} \
            --task-definition ${{ steps.register-td.outputs.new_arn }}
