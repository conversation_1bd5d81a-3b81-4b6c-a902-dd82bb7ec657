# Conference Demo Chatbot Plan

## Objectives
- Deliver a September 16 demo where attendees can ask quantitative sensor-market questions and receive grounded, chart-ready responses.
- Prevent hallucinations by answering strictly from Truthkeep analytics or curated demo data with full traceability.
- Keep the existing chatbot UI but enable an analytics mode that can be demoed live or via staged data while preserving a path to production.

## Current Gaps
- Chatbot only receives long summaries; no access to timeline metrics (counts, sentiment averages, trends).
- No deterministic tools for aggregations by brand/topic/source/time window, so the LLM fabricates numbers when data is missing.
- Brand/topic tagging is incomplete for sensor verticals; some queries (Bosch MEMS, quantum sensors) return empty sets silently.
- UI cannot display structured payloads (tables, charts) returned from the backend.

## Implementation Strategy

### Phase 0 – Data Audit (1–2 days)
1. **Inventory coverage** for Bosch, TDK, STMicro, Analog Devices, Invensense, keyword clusters (RISC-V, quantum sensors, edge AI).
2. **Re-run tagging pipeline** where needed to ensure brands and sensor types are mapped to existing `Topic`/`Tag` records.
3. **Document true gaps** so demo responses can fall back gracefully (“0 records matching filters”).

### Phase 1 – Analytics Endpoints (3–5 days)
1. **Counts API**: expose `GET /analytics/brand-mentions` (filters: brand/topic IDs, sentiment, date range, source).
2. **Sentiment summary API**: aggregate average sentiment per brand and channel.
3. **Trend API**: return weekly/monthly counts for keyword combinations (e.g., RISC-V + MEMS).
4. **Issue tracker API**: count timeline items tagged `issue` for a brand with source breakdown.
5. **Keyword velocity API**: compute WoW deltas for predefined sensor keywords.
6. **All endpoints** must return structured JSON with IDs, counts, and traceable timeline item references.

### Phase 2 – Chatbot Tooling (2–3 days)
1. Add an **`analytics_query` tool** in `ChatService` that validates natural-language parameters (brand, sentiment, date range) via Zod and dispatches to the new analytics APIs.
2. Enforce **deterministic responses**: when filters yield no records, return a standardized “no data” message plus the filters used.
3. Update `openAIResponseToOutputMessage` path to **attach structured payloads** (e.g., arrays for tables, time-series for charts) alongside narrative text.

### Phase 3 – Frontend Enhancements (3–4 days)
1. Extend the chatbot component to **render tables and sparkline charts** when payload metadata is present.
2. Provide a toggle for **“Demo Mode”** that swaps API base URLs to a curated dataset (static JSON or staging database) without code changes.
3. Add inline citation badges linking to the underlying timeline items for transparency.

### Phase 4 – Demo Data & Scripting (2 days)
1. Populate a **curated dataset** that covers all conference prompts with realistic numbers and narratives.
2. Create a **scripted prompt deck** verifying each target question end-to-end.
3. Record contingency messaging for zero-results scenarios.

### Stretch Goal – Deep Research Integration (post-demo)
1. Export monthly sensor reports into an OpenAI vector store or MCP-compatible API.
2. Offer a **“Run Deep Research”** button that triggers an asynchronous `o3-deep-research` call; once complete, surface the report as a downloadable artifact.
3. Keep this separate from the live chat flow to avoid latency and cost spikes during the demo.

## Operational Safeguards
- **Guardrails:** refuse to fabricate numbers; every reply must cite count totals and time windows.
- **Latency:** analytics endpoints should respond <500ms; cache frequent brand queries.
- **Monitoring:** log analytics tool usage, success/failure, and returned record counts to verify demo stability.
- **Fallback:** if live data access fails, automatically switch to demo mode and notify the presenter.

## Deliverables Checklist
- [ ] Coverage report for target brands/topics.
- [ ] REST API suite with automated tests.
- [ ] Updated chatbot service with analytics tool routing.
- [ ] Frontend rendering for structured analytics responses.
- [ ] Demo dataset + prompt script.
- [ ] Optional deep-research spike notes.

## Risks & Mitigations
- **Data sparsity:** curate supplemental demo entries and surface “no data” answers clearly.
- **Timeline**: prioritize Phase 0–2; Phase 3 minimal viable charts if time constrained.
- **Hallucinations:** enforce deterministic tool responses and audit outputs before the demo.

---
Prepared for leadership review and sprint planning.
