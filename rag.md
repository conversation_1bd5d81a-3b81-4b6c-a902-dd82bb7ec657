  Front end (web/src/components/Chatbot) wraps messages in session storage, lets users attach
      timeline items via AddPostModal, then posts ChatRequestDto to /chat.
   •  Backend (api/src/chat/chat.service.ts) relays user turns to the OpenAI Responses API with
      three tools (search, search_similar, search_analytics) that delegate to
      SearchService/TimelineService for RAG data.

   Current RAG Flow
   •  Timeline ingestion writes embeddings/search vectors into SearchableEntity, and
      TimelineService.getTimelineItemDetails hydrates narratives, topics, tags, and sentiment for
      search hits.
   •  Chat tool outputs reduce those rich objects to plain bullet strings (URL + long summary)
      before the model builds the final reply, so narrative/topic structure is never surfaced.

   Key Gaps
   •  Timeline attachments lose their type context because inputMessageToOpenAIInputItem ignores
      message.type, so OpenAI sees only opaque IDs (e.g. "gHC7uZBigmQ"), hurting search_similar
      discovery.
   •  processFunctionCall returns unstructured text, discarding fields like narratives, topics,
      relevance, and tipsAndActions, leaving the model without direct access to the very
      narrative/topic data you need.
   •  There is no tool to fetch a specific timeline/post by ID or to summarize a topic/narrative
      cluster, forcing the assistant to rely on generic search even when the user references exact
       items.
   •  search_similar only supports Reddit/Youtube sources; forum narratives (ALL_ABOUT_CIRCUITS,
      MICROCHIP_CLASSIC, AVR_FREAKS) and Salesforce threads can’t be pulled in, reducing coverage.

   •  searchAnalytics depends on a free-form OpenAI prompt to build filters but clamps the time
      window to 60 days regardless of the request, so topic-level counts are often wrong.

   Recommended Improvements
   •  Encode attachments before the OpenAI call (e.g. prepend "ATTACHMENT|REDDIT_POST|<id>") or
      convert them server-side into immediate context blocks built from
      TimelineService.getTimelineItemDetails so the model sees titles, narratives, topics, and
      sentiments.
   •  Return tool outputs as JSON (e.g. {"items":[{...timeline fields...}]}) and update the system
       prompt to expect structured data; this lets downstream prompts reference specific
      narratives/topics deterministically.
   •  Add dedicated tools: get_timeline_item (by source/id with narratives), list_topic_activity
      (topic → recent posts & counts), and list_narrative_examples (narrative aspect → supporting
      posts) to let the assistant answer narrative/topic questions without heuristic search.
   •  Extend search_similar to all DataSource values, allow higher take, and reuse
      SearchService.rerankContextIds so the assistant can surface multiple corroborating posts for
       narrative questions.
   •  Harden searchAnalytics by parsing user filters locally (natural-language date parser,
      explicit topic lookup) instead of another OpenAI round trip, and return aggregate metrics
      alongside the raw items for clearer quantitative answers.

      ----

         Jason Liu’s RAG playbook highlights measuring retrieval recall first and segmenting failure
   modes to avoid absence/intervention bias, exactly what our pipeline needs instead of more
   prompt tweaks.

   Recommendations

   Instrument /chat tool calls to log retrieved timeline IDs, run synthetic question→timeline
   evaluations per topic/narrative segment, and block generations when recall fails so
   hallucinations are caught pre-LLM.
   Enrich timeline ingestion with structured topic/narrative metadata, expand search_similar to
   every DataSource, and add re-ranking—mirroring Liu’s structured extraction plus smart tool
   routing guidance.
   Add chat UI feedback hooks (thumbs, “missing context” prompts) to capture real failures,
   feeding the fine-tuning datasets Liu recommends for continuously reducing hallucinations.

   ason Liu’s RAG playbook highlights measuring retrieval recall first and segmenting failure
   modes to avoid absence/intervention bias, exactly what our pipeline needs instead of more
   prompt tweaks.

   Recommendations
---

   Instrument /chat tool calls to log retrieved timeline IDs, run synthetic question→timeline
   evaluations per topic/narrative segment, and block generations when recall fails so
   hallucinations are caught pre-LLM.
   Enrich timeline ingestion with structured topic/narrative metadata, expand search_similar to
   every DataSource, and add re-ranking—mirroring Liu’s structured extraction plus smart tool
   routing guidance.
   Add chat UI feedback hooks (thumbs, “missing context” prompts) to capture real failures,
   feeding the fine-tuning datasets Liu recommends for continuously reducing hallucinations.

   System Architecture Overview

   Design the chatbot stack around a three-stage RAG loop: Retrieval Orchestrator (query classification,
    hybrid retrieval, reranking), Context Assembly Layer (timeline enrichment, narrative/topic JSON
   packaging), and Grounded Generation Engine (multi-pass LLM with guardrails and post-validators) wired
    to user feedback storage.

   Retrieval & Indexing

   Adopt sentence-level chunks with sliding-window + small-to-big grouping, upgrade embeddings to
   domain-tuned BGE/LLM-Embedder, add metadata (narrative/topic IDs, sentiment, relevance) and extend
   search_similar/HyDE rewriting across all DataSource values before reranking top-K via cross-encoder.

   Context Packaging & Tooling

   Before any LLM call, convert retrieved timeline items into structured JSON (titles, narratives,
   topics, sentiment evidence, URLs) and expose new tools: get_timeline_item, list_topic_activity,
   list_narrative_examples, get_metrics, all returning validated payloads with citation handles.

   Generation & Guardrails

   Run an initial “planner” completion that chooses tools and required evidence, then a grounded
   “responder” completion that must cite retrieved snippets; gate outputs through automated checks
   (missing citations, unsupported claims, sentiment mismatches) to trigger fallback regeneration or
   user-facing caveats.

   Evaluation & Feedback Loop

   Instrument retrieval recall/precision per topic/narrative segment using synthetic + real questions,
   log tool usage/results, capture thumbs up/down plus “missing context” tags in UI, and feed this data
   into periodic embedding fine-tuning and guardrail threshold tuning to steadily drive hallucinations
   toward zero.

   ---

   Use your existing NestJS services to add a retrieval orchestrator: keep Postgres + SearchableEntity,
   but upgrade embeddings to bge-large-en (via llm-embedder Docker or OpenAI text-embedding-3-large) and
    add HyDE query rewriting plus cross-encoder reranking (cross-encoder/ms-marco-MiniLM-L-6-v2 served
   through HuggingFace Inference Endpoint).
   Expose new API tools in the current chat module—get_timeline_item, list_topic_activity,
   list_narrative_examples—returning structured JSON assembled from TimelineService, then wrap the
   OpenAI Responses API in a two-pass flow (planner + responder) with Guardrails like guardrails-ai or
   LangChain OutputParser to enforce citation checks.
   Use Mantine UI to capture thumbs/corrections and persist them via Prisma for evaluation, and schedule
    an Airflow (or BullMQ) job that re-computes retrieval recall using synthetic prompts plus the user
   feedback set, driving periodic embedding fine-tuning with FlagEmbedding scripts.