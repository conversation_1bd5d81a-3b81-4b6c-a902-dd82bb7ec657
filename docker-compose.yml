# this is meant to mirror how github actions will spin up services
version: "3.8"

services:
  api:
    build:
      context: api
      dockerfile: docker/Dockerfile
    env_file:
      - api/.env
    environment:
      DATABASE_URL: **********************************
      REDIS_HOST: redis
      SES_ENDPOINT: http://local-ses:8282
    container_name: api
    ports:
      - "3000:3000"
    depends_on:
      - db
      - redis
      - local-ses
    stop_grace_period: 0s


  web:
    build:
      context: web
      dockerfile: docker/Dockerfile
    container_name: web
    environment:
      API_HOST: http://api:3000
    ports:
      - "4173:4173"
    depends_on:
      - api
    stop_grace_period: 0s

  db:
    image: pgvector/pgvector:pg17
    container_name: postgres
    ports:
      - "5432:5432"
    environment:
      POSTGRES_USER: root
      POSTGRES_PASSWORD: strongPASS

  redis:
      image: redis:latest
      container_name: redis
      ports:
          - "6379:6379"

  local-ses:
    image: ka<PERSON><PERSON><PERSON><PERSON>/local-ses:latest
    ports:
      - "8282:8282"
    container_name: local-ses
    stop_grace_period: 0s
