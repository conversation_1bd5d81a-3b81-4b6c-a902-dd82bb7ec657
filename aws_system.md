Render Mermaid
Truthkeep Microchip AWS Cloud Architecture
Multi-Environment Setup

AWS Cloud Infrastructure

GitHub Actions CI/CD

External APIs

Monitoring

Networking & Security

Data & Storage

Backend (Container Services)

Frontend (Static Hosting)

YouTube Data API

Reddit API

Salesforce Forums API

OpenAI API

GitHub Repository
- development
- staging
- main

GitHub Actions
Workflow

S3 Bucket
Static Website

CloudFront CDN
Global Distribution

Elastic Container Registry
Docker Images

ECS Cluster
Container Orchestration

ECS Service
Auto Scaling

ECS Tasks
Running Containers

RDS PostgreSQL
with pgvector
Vector Database

ElastiCache Redis
Queue & Cache

S3 Bucket
allaboutcircuits-scraper
Scraped Data

VPC
Virtual Private Cloud

Application Load Balancer
Traffic Distribution

IAM Roles
Access Control

AWS Secrets Manager
API Keys & Credentials

CloudWatch
Logs & Metrics

CloudWatch Alarms
Monitoring

Development Environment
- S3: dev-bucket
- ECS: dev-cluster
- RDS: dev-database

Staging Environment
- S3: staging-bucket
- ECS: staging-cluster
- RDS: staging-database

Production Environment
- S3: prod-bucket
- ECS: prod-cluster
- RDS: prod-database

☁️ How Truthkeep's AWS Cloud System Works
🏗️ Cloud Architecture Overview
The Truthkeep Microchip system uses a modern, scalable AWS cloud architecture with containerized microservices, automated CI/CD, and multi-environment deployments.

🚀 CI/CD Pipeline & Deployment
name: Deploy truthkeep-microchip aws
on:
  push:
    branches:
      - development
      - staging
      - main

permissions:
  id-token: write 
  contents: read

  Automated Deployment Process:

Code Push → Triggers deployment based on branch
Environment Selection → Different AWS resources per environment
Parallel Deployment → Frontend and backend deploy simultaneously


- name: Build Frontend
  working-directory: web
  run: |
    npm ci
    npm run build

- name: Upload to S3
  run: |
    aws s3 sync web/dist s3://${{ needs.set-vars.outputs.bucket }} --delete

- name: Invalidate CloudFront
  run: |
    aws cloudfront create-invalidation \
      --distribution-id ${{ needs.set-vars.outputs.cloudfront }} \
      --paths "/*"

      Frontend Architecture:

S3 Static Hosting: React app served from S3 buckets
CloudFront CDN: Global content delivery with caching
Automatic Invalidation: Cache clearing on deployments
Backend Deployment (Containerized)
- name: Build & Push Backend Docker Image
  run: |
    docker build \
      -t $REG/$REPO:latest \
      -t $REG/$REPO:$SHA \
      -f api/docker/Dockerfile api/
    docker push $REG/$REPO:$SHA

- name: Update ECS Service to new revision
  run: |
    aws ecs update-service \
      --cluster  ${{ needs.set-vars.outputs.ecs_cluster }} \
      --service  ${{ needs.set-vars.outputs.ecs_service }} \
      --task-definition ${{ steps.register-td.outputs.new_arn }}

Backend Architecture:

Docker Containerization: NestJS API in Docker containers
ECR (Elastic Container Registry): Docker image storage
ECS (Elastic Container Service): Container orchestration
Auto Scaling: Automatic scaling based on demand

Data Layer Architecture
Database Systems
DATABASE_URL: z.string(),
DATABASE_URL_APP_USER: z.string(),
REDIS_HOST: z.string(),
REDIS_PORT: z.coerce.number().min(1000),

Data Storage:

RDS PostgreSQL: Primary database with pgvector extension for AI embeddings
ElastiCache Redis: Queue system (BullMQ) and caching
S3 Data Storage: Scraped content and file storage

S3 Integration for Data

private async getObject(key: string): Promise<string> {
    const commandOutput = await this.s3.getObject({
        Bucket: "allaboutcircuits-scraper",
        Key: key,
    });
    return commandOutput.Body.transformToString();
}

3 Usage:

Scraped Data Storage: All About Circuits forum data
File Storage: Documents, images, and other assets
Data Archival: Long-term storage of processed content

Multi-Environment Setup
Environment-Specific Resources

if [ "${{ github.ref_name }}" = "development" ]; then
  echo "bucket=${{ vars.S3_BUCKET_NAME_DEVELOPMENT }}" >> $GITHUB_OUTPUT
  echo "cloudfront=${{ vars.CLOUDFRONT_DISTRIBUTION_ID_DEVELOPMENT }}" >> $GITHUB_OUTPUT
  echo "cluster=${{ vars.ECS_CLUSTER_NAME_DEVELOPMENT }}" >> $GITHUB_OUTPUT

  Three Environments:

Development → development branch
Staging → staging branch
Production → main branch
Each environment has separate:

S3 buckets for frontend
CloudFront distributions
ECS clusters and services
RDS databases
ElastiCache instances
🔧 Container Configuration
services:
  api:
    build:
      context: api
      dockerfile: docker/Dockerfile
    environment:
      DATABASE_URL: **********************************
      REDIS_HOST: redis

Container Architecture:

API Container: NestJS backend application
Web Container: React frontend (for local development)
Database Container: PostgreSQL with pgvector
Redis Container: Queue and cache system
Local SES: Email testing service

 Security & Configuration
Environment Variables
const zodEnv = z.object({
    NODE_ENV: z.enum(["development", "production", "test", "provision"]),
    APP_BASE_URL: z.string().url(),
    DATABASE_URL: z.string(),
    OPENAI_API_KEY: z.string().optional(),
    YOUTUBE_API_KEY: z.string().optional(),
    // ... other environment variables
});

Security Features:

IAM Roles: Fine-grained access control
AWS Secrets Manager: Secure API key storage
VPC: Network isolation
Environment Validation: Type-safe environment variables

Monitoring & Observability
CloudWatch Integration:

Application Logs: Centralized logging
Metrics: Performance monitoring
Alarms: Automated alerting
Dashboards: Real-time system health

Background Processing
Queue System

BullModule.forRoot({
    connection: {
        host: process.env.REDIS_HOST,
        port: process.env.REDIS_PORT,
        tls: process.env.ENVIRONMENT === "test" ? undefined : {},
    },
    prefix: "{BULLMQ}",
}),

Background Jobs:

Data Collection: YouTube, Reddit, forum scraping
AI Analysis: Sentiment analysis, embedding generation
Timeline Updates: Materialized view refreshes
Notifications: Email and alert processing

Global Distribution
CloudFront CDN:

Global Edge Locations: Fast content delivery worldwide
Caching Strategy: Optimized for static assets
SSL/TLS: Secure HTTPS connections
Custom Domain: Professional domain setup
💰 Cost Optimization
AWS Cost Management:

Auto Scaling: Scale resources based on demand
Spot Instances: Cost-effective compute for background jobs
S3 Lifecycle Policies: Automatic data archival
CloudFront Caching: Reduced origin requests
🚀 Scalability Features
Horizontal Scaling:

ECS Auto Scaling: Automatic container scaling
Load Balancing: Traffic distribution across instances
Database Read Replicas: Distributed database load
Redis Clustering: Distributed caching and queuing
🔧 Development Workflow
Local Development: Docker Compose for local testing
Feature Branch: Push to development environment
Testing: Staging environment for QA
Production: Main branch deploys to production
Rollback: Quick rollback capabilities via ECS
This AWS cloud architecture provides a robust, scalable, and maintainable platform for the Truthkeep Microchip application, with automated deployments, multi-environment support, and enterprise-grade security and monitoring.