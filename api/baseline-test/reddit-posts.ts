export const redditPosts = [
    // {
    //     "id" : "sn2o3f",
    //     "title" : "NUL operator in XC8 Assembler",
    //     "text" : "I am trying to write a macro with an optional argument. According to the XC8 Assembler's user guide, I should be able to use the NUL operator to check for the presence of the argument. The assembler, however, produces a syntax error when parsing that line. Any help would be appreciated.\n\nThe code looks something like:\n\n&gt;FOO MACRO arg  \nIF NUL arg  \nclrw  \nELSE  \nmovlw arg  \nENDIF  \nENDM",
    //     "commentCount" : 2
    // },
    // {
    //     "id" : "si0otn",
    //     "title" : "PIC programming - stuck with dsPIC30F4013",
    //     "text" : "Guys, I am trying to rewrite some dsPIC30F4013 based boards (part of an EV charger) for a few days now. I wonder if you could comment on these issues I bumped into. \n\nI have two PICKits 3.0 and 3.5, Chinese (red) clones.\n\n(1) I can read and write the Flash - however on the EEPROM only read works and interestingly write as well as long as bit is cleared (!). In other words I can change FFFF to FFFE (changing the last bit from 1 to 0) but not the other way around from FFFE to FFFF (changing a bit from 0 to 1) and so on. \n\n(2) I have set Code Protect on two of these boards by mistake and I could not disable it anymore. I have tried Erase both using the PICkit3 standalone app and MPLAB X IPE  (4.20). Documentation mentions \"Bulk Erase\" but I simply can't make it work.\n\nAny comments are appreciated.",
    //     "commentCount" : 4
    // },
    // {
    //     "id" : "rjodji",
    //     "title" : "FPGA",
    //     "text" : "Hi, I would like to code an AT40K40AL FPGA but I don't know which software that I need. Can someone tell me which software I need?",
    //     "commentCount" : 2
    // },
    {
        id: "15auh5e",
        title: "School Project 50%",
        text: "Is anyone proficient in coding for ICD 3 on MPLAB X IDE v6.05? If so i would like to seek help. My team and i have been trying for weeks to get some code working for us but to no avail.\n\nEdit: So for more context, We are using PIC16F18877 in C++. We were given a scenario in where we are required to simulate a dam opening and closing periodically. lights have to flash to indicate when it opens or closes, accompanied with a buzzer to for a auditory warning that the dam is either opening or closing. The use of an LCD and seven segment is used to display temperature (seven segment) and water level and % (LCD) so far our biggest obstacle is the ADC using a potentiometer to simulate water level which should reflect in the LCD but it isnt converting right or something??\n\nEdit 2: Sorry its in C*",
        commentCount: 8,
    },
    // {
    //     "id" : "14nzkr3",
    //     "title" : "List of PIC chips by typical sleep or deep sleep current?",
    //     "text" : "I usually scan data sheets one by one to try to keep up. Some newer parts seem to have 600nA sleep current. A few older parts have 50nA, 20nA, 9nA, and even 1nA sleep current. Is there a list of these lower current parts? The MAPS tool only allows selection of XLP and doesn't differentiate further. Neither digikey nor mouser shows the typical parameter value either.\n\nA couple of additional data points would be good as well such as: wake up options, wake up time (dependent on clock source), peripherals that can run during sleep (at the cost of more current), voltage\/temperature dependencies, operating voltage range, RAM retention voltage, amount of RAM retained during sleep (I've seen as low as two bytes). But this extra data is probably something I'll have to aggregate myself.",
    //     "commentCount" : 6
    // },
    // {
    //     "id" : "z4ib4i",
    //     "title" : "What US tech does ASML use",
    //     "text" : "What US tech does ASML use allowing us to ban the sales of machines to China?",
    //     "commentCount" : 0
    // },
    // {
    //     "id" : "1mlwd75",
    //     "title" : "When should i consider writing my own driver",
    //     "text" : "Why I usually find projects with main peripheral (UART, SPI, ADC, etc) drivers written from scratch instead of using one provided or open sourced drivers even for mcu's with good support (avr, pic).  \nDo they just copy paste needed functions and add it in their files or they really write it from scratch, and when would i need to write one myself fully from the datasheet  \n**EDIT: If you have any bad past experience with specific vendor, please mention it** ",
    //     "commentCount" : 51
    // },
    // {
    //     "id" : "1ml9v7s",
    //     "title" : "Distance between MCU and capacitors",
    //     "text" : "Hello, I am very new in electrical engineering and as a hobbyist I want to build my own development board. I have carefully draw the schematics with all the decoupling and bulk capacitors the datasheet recommends.\n\nNow that I am designing the actual PCB I m not pretty sure about the physical distance these components should have. I know that decoupling capacitors must be placed near the supply pins. But what are the factors that determine this distance? Are there fixed values? And what about the bulk capacitors?\n\nI have also used loading capacitors for the crystal resonator. Do they need to be close to the crystal module?\n\nI would really like to get to know about these details, since I feel like floating in an endless sea with my eyes closed!",
    //     "commentCount" : 16
    // },
    // {
    //     "id" : "1mkvltv",
    //     "title" : "I built a microcontroller-powered request sender you can control from your PC – \"victural-req\"",
    //     "text" : "I’ve been experimenting with combining microcontrollers andPC-side control to create a tool that lets you send network requests directly via a connected microcontroller.\n\nMy projectvictural-req allows you to:\n\nConnect a microcontroller (Arduino, ESP series, etc.) to your computer via USB\/Serial.\nSend commands or request payloads from your PC using a simple Node.js CLI.\nHave the microcontroller execute them — useful for IoT testing, automation, or quick prototyping.\n\nWhy I made this:\nSometimes I want to test network APIs or trigger devices **without** having to set up a full Wi-Fi stack on the microcontroller. This lets the PC handle the heavy lifting while still controlling hardware at the other end.\n\nFeatures so far:\n\n Node.js CLI interface: node . \"&lt;your command&gt;\"\nSerial communication with customizable baud rate.\nJSON parsing support for structured data.\nCompatible with most boards supporting USB serial.\nGitHub Repo:\n [https:\/\/github.com\/ewriq\/victural-req](https:\/\/github.com\/ewriq\/victural-req)\n\nPlanned next steps:\n\nAdd bidirectional JSON messaging with acknowledgment.\nBuild a small desktop GUI for sending requests.\nOptional WebSocket server mode for remote control.\n Example integrations with ESP32 + relays, sensors, and API calls.\n\nI’d love feedback, feature ideas, or collaboration offers.\nWhat’s the first thing you’d build with it?\n\n",
    //     "commentCount" : 0
    // },
    // {
    //     "id" : "1mkjjpy",
    //     "title" : "CO2 Monitor (PPM) with Source and Gerbers",
    //     "text" : "This was a fun project I did a while ago, so I cleaned up the code a bit and posted it here:  \n[https:\/\/github.com\/resonantlabs\/co2-monitor](https:\/\/github.com\/resonantlabs\/co2-monitor)\n\nIt uses a standard STM32 Nucleo and a Senseair CO2 monitor. It works well. I'm surprised that my sun porch has CO2 levels of 700PPM, and our bedroom gets near 1000PPM at night!\n\nFeel free to drop me a line if you have questions about building the project yourself. I'm always happy to help.",
    //     "commentCount" : 5
    // },
    // {
    //     "id" : "s0sx71",
    //     "title" : "Advice for a PIC beginner?",
    //     "text" : "Hi folks.  I am not a total newb.  My first commercial project was interfacing an ADC0816 to a Sinclair ZX-81, to monitor a panel testing flashlight bulb longevities.  Not much has changed in 40 years, right?\n\nI recently became enamoured with PICAXE devices and am having a lot of fun.  They are simple to use and to program, but now I am thinking that maybe I want to directly program PIC devices myself rather than rely on an \"educational\" supplier with an ocean between me and them.\n\nMy problem is getting started.  I don't have thousands to spend on equipment, and I don't want to have my beginner investments turn out to be some marginal branch of the market that will be cut off two months after I get comfortable with it.\n\nMy main asset is that I have a relatively easy time absorbing new languages (if not development environments).  To me the perfect device will execute Perl directly, but I suspect that's unreasonable to expect.  Any advice appreciated.",
    //     "commentCount" : 17
    // },
    // {
    //     "id" : "ru2qe2",
    //     "title" : "Help with PIC programming",
    //     "text" : "Hello, I’m trying to program a pic18F4550. I’m using the pickit3 clone and an external power supply module, I have tried also to supply power to the pic through the programmer but it would still give me the same error which is: “Target device not found, could not detect target voltage”. I did go into the settings and manually select\/deselect the pickit3 as the power source. \n\nThank you\n\nhttps:\/\/imgur.com\/a\/4T19JBl",
    //     "commentCount" : 4
    // },
    // {
    //     "id" : "r5qlzm",
    //     "title" : "Can anyone tell me how to program this in pic18f4580, in this sequence using events and timers? https:\/\/youtu.be\/-jmtrxNkWlE",
    //     "text" : "https:\/\/youtu.be\/-jmtrxNkWlE",
    //     "commentCount" : 1
    // },
    // {
    //     "id" : "r5a4mr",
    //     "title" : "Does anyone know how to program in PIC18F4580?",
    //     "text" : "",
    //     "commentCount" : 4
    // },
    // {
    //     "id" : "1mnyoq5",
    //     "title" : "How to drive 5v logic components with 3.3v MCU using open drain pins?",
    //     "text" : "Hi All,\n\nI am trying to control an A4988 with an STM32F103C8T (BluePill Plus). All the documentation I have read says that I should be able to control the logic and power it with 3.3v but for whatever reason the breakout boards I have cannot. So I am now trying to use the A4988 with open drain outputs over 5V.\n\nThe pins I am using are 5v tolerant but I cannot get the system to work no matter what I do.\n\nSome help and advice would be much appreciated.\n\nAlso, I'm really sorry for my thrown together schematic, I just threw it together so people don't need to decipher my breadboarding.",
    //     "commentCount" : 17
    // },
    // {
    //     "id" : "1mnrma7",
    //     "title" : "ESP32-S3 Wi-Fi Scanner with SQLite logging",
    //     "text" : "Hi, just wanted to share a little project of mine – a Wi-Fi scanner for ESP32-S3 that logs results into an SQLite database on an SD card. Built with PlatformIO in C++, includes a Makefile for easy build\/flash\/monitor and nix-shell support.\nRepo: [github.com\/Friedjof\/WifiScanner](https:\/\/github.com\/Friedjof\/WifiScanner)",
    //     "commentCount" : 12
    // },
    // {
    //     "id" : "z0xunm",
    //     "title" : "Recommendations for magnifying glasses?",
    //     "text" : "I've been using a handheld glass for months and I'd love to free up my other hand. I'm getting tired of putting my face an inch from the board and covering it in my hot breath when I don't use the glass lol. What do you guys use if any?",
    //     "commentCount" : 1
    // },
    // {
    //     "id" : "zkoe2c",
    //     "title" : "Popular PIC models that are frequently in stock?",
    //     "text" : "Hi! I love using the PIC12F\/16F series for small projects like reading inputs and switching outputs. One thing that bugs me a little though is that after choosing a preferred model it sometimes goes out of stock for a long time and I'd have to replace it with a similar model. Recent shortages did not help but I was wondering if perhaps there are more standard or popular models of the PIC12F\/PIC16F series which I should opt for? Generally my requirements aren't too constrained. Alternatively I was thinking of switching to something like the attiny since it seems to be quite popular with hobbyists etc and some stock always seem to be available.",
    //     "commentCount" : 5
    // },
    // {
    //     "id" : "1l42wvt",
    //     "title" : "SAMV71 CAN Module RX interrupt reliability correlation with MCK (Host Clock)?",
    //     "text" : "I am curious to know if anyone has some details regarding the MCAN module on the ATSAMV71Q21B MCU, or just MCAN modules in general. I'm experiencing some unexplainable behaviour and I have scoured the documentation for any details and can't find any.\n\nI am using the SAMV71 Xplained Ultra development board, and I'm experiencing odd issues regarding CAN interrupts when changing MCK frequencies...\n\nI would like to know the correlation (if any) between MCK (Host Clock) frequencies and its effect on the MCAN peripheral. Particularly how the bus-independent clock should be set in accordance with the peripheral clock (as seen in the MCAN Block Diagram Figure 49-1 section 49.3 of ATSAMV71Q21B Reference Manual) . If anyone knows anything about this, it would be appreciated.\n\n# Background Information:\n\nI'm relatively new to the embedded software world.\n\nI have the External Crystal Oscillator enabled, leading into the USB UTMI PLL clock (480MHz, enabled) which leads into the PMC\\_PCK5 (enabled) prescaled down to 80MHz which is of course going to the CAN peripheral (peripheral clock enabled). That is all standard as per the datasheet. I am also fairly confident my bit timings are correct for 1Mbps (MCAN\\_NBTP\\_NSJW(2), MCAN\\_NBTP\\_NTSEG1(10), MCAN\\_NBTP\\_NTSEG2(3), MCAN\\_NBTP\\_NBRP(4) giving 1+NTSEG1+1+NTSEG2+1 = 16tq with 80\/(4+1) giving 16MHz therefore 1MHz or 1Mbps bit timing) given that no errors are present in PSR when the receive interrupt does trigger.\n\nThe confusing part comes with how the Host Clock Controller is setup. When PMC\\_MCKR clock select is set to use MAINCK (12MHz External Oscillator) with no prescaler or divider, the CAN RX interrupts only trigger occasionally (the IR and PSR registers still indicate a normal error-free receive occurred). However, when I set the PMC\\_MCKR to use UPLL clock with prescaler 8 (60MHz) and of course set EEFC\\_FMR.FWS to 6 (flash wait state), the CAN RX interrupt triggers very reliably! Please can anyone shed some light on the importance of MCK\/Host Clock when it comes to the MCAN module? The more detail, the better.\n\n\n\n**tl;dr** Reliability of MCAN RX Interrupt seemingly entirely based upon Host Clock Controller settings. When set to 12MHz MAINCK, MCAN RX interrupt unreliable. When set to 60MHz UPLL clock with prescalers , MCAN RX interrupt very reliable. All with PCK5 set to 80MHz. Message RAM is aligned.\n\n**P.S.** I use Eclipse IDE with GDB OpenOCD debugging. I use the SAMV71-DFP for register definition header files and nothing else, all programming is done manually via direct register control.\n\n\n\n",
    //     "commentCount" : 4
    // },
    // {
    //     "id" : "1etsi6t",
    //     "title" : "Trying to start up with MPlabX on Linux..",
    //     "text" : "I am trying to develop code for an old Pic 16f819.\n\nI am using Fedora 40 KDE\/Wayland in this computer.\n\n\n\nI had install MplabX 6.20, xc8, xc16,xc32 and xc-dsc3.\n\n\n\nI am trying to write a basic firmware, setting RA and RB as Digital, no analogue at all, then declaring some RB(x) as Inputs and some RA(x) as Output.\n\nAlthought i have basic knowledge of C language, i did download a lot of documentation, i am readling it but i am experiencing some strange phenomena, at least strange to me:\n\n\n\nPORTAbits and PORTBbits goes lightblue well on Mplabx;\n\nTRISABits and TRISBbits do not unless i spell them as \"TRISABits\\_t &amp; TRISBbits\\_t\" and even that i get \"unexpected token\" warnings into the IDE.\n\n\n\nAfter including \"xc.h\", \"stdio.h\", \"stdlib.h\" so i can code a switch {} so i can read rb(x) and write in ra(x)s i get further \"unexpected token\" stuff.\n\n\n\nI am noticing that many of the docs i have speak about an \"ANSELD\" and \"ANSELH\" directives, but as i started my project for Pic16f819 it seems it doesnt support them.\n\nI ended using \"ADCON1bits\\_t.PCFG = 0\" most docs i saw speak just about \"ADCON1\" and not about \"ADCON1\\_T\".\n\n\n\nI do not know well how to resolve this. It looks like, maybe, i forgot to install something else, the docs i have could be outdated, maybe i did something wrong when setting MPlabX during installation.\n\nCan i be missing some special adjustment, add on, setting, etc etc that it may be needed in Fedora and not other distributions?\n\n\n\nMPLAB IPE recognizes perfectly my pickit3 and i could get it talking with a pic16f877 from a home appliance board, is not a problem at all.",
    //     "commentCount" : 0
    // },
    // {
    //     "id" : "1bh6kky",
    //     "title" : "MPLAB X v6.20 Options Menu freezes in Linux?",
    //     "text" : "Hi, when trying to open the MPLAB X options menu, I just get a clockwise-rotating-arrow cursor (meaning please wait) and this totally freezes the whole app.  The process (java, java-8-openjdk) must then be killed.\n\nUsing dual monitors on Arch \/ Manjaro \/ Mabox Linux.  [This link](https:\/\/developerhelp.microchip.com\/xwiki\/bin\/view\/software-tools\/x\/troubleshooting\/) suggests trying to move the options window on-screen (just moves the IDE window) and deleting the [`api.properties`](http:\/\/api.properties) file (no change.)  Thought maybe it was the cache, so (archived and deleted) everything in `~\/.cache\/mplab_ide\/dev\/v6.20\/var` but no help.  Any ideas for other things to try would be very welcome, thank you.",
    //     "commentCount" : 8
    // },
    // {
    //     "id" : "137qb7e",
    //     "title" : "Weird problem on dspic33ep128mc202",
    //     "text" : "Edit: the problem was noise on Vdd. I bodged in an RC filter and havent had a problem. I'm guessing most of the pics where just more tolerant of the noisy 3v3 rail than others.\n\n\n\nHello, I am working on a project using a dspic that is controlling a dc motor with a qei interface. Things are going very well except one problem that is driving me insane...\n\nThe problem only happens on like 20% of the pics I have, and replacing the pic solves it. But strangely enough it doesnt even always happen.\n\nThe problem is this, some times, the PWM out going to the Hbridge is randomly just stuck on. So that naturally causes a runaway spaz as the direction pin is just jamming the motor back and forth...\n\nI checked the errata and there is a problem like this, but it's for a different PWM mode, I tried implementing the work around anyways out of desperation but of course it was no use.\n\nI'm to the point where I might just say screw it and bit bang the pwm...",
    //     "commentCount" : 13
    // },
    // {
    //     "id" : "zvhxw8",
    //     "title" : "mplab X ide performance issues",
    //     "text" : "hi is there a way to force mplab to allocate more ram i have 24gigs in my system but mplab is only taking 400mb and displaying a message lack of memory i changed in the conf file -J-Xmx4096m i even set it to take 8gige but nothing please help its laggy",
    //     "commentCount" : 0
    // },
    // {
    //     "id" : "10bmn1v",
    //     "title" : "Just take my money already!",
    //     "text" : "",
    //     "commentCount" : 7
    // },
    // {
    //     "id" : "1mcexha",
    //     "title" : "MPLAB X IDE 6.20 menu glitches on CachyOS \/ Arch KDE\/Wayland",
    //     "text" : "My fresh installation of MPLAB X shows strange behaviour I never experienced on other Linux distributions in the past - even with the same MPLAB Version on other arch based distros within the last month.\n\nMenus sometimes open like 100 pixel left or right of where they should be. Some menus close when I don't hover over the parent menu, which makes them unusable. For example the top left \"File\" option. Other menus are not even clickable. Like everything in the second submenu of \"Right Click Project Name\" -&gt; \"Project\" or \"Analysis\" is not clickable.\n\nPicture for reference:\n\n[Unclickable and unfocusable menu options](https:\/\/preview.redd.it\/qrdfaevv2uff1.png?width=553&amp;format=png&amp;auto=webp&amp;s=99dd0fba47a517560cb1dbcd918d6a9686c453e5)\n\n  \n\n\nSometimes I click a menu, then switch windows and the menu is still open on top of what is now on my screen.\n\nIt looks like this:\n\n[Unclickable menu stays open on top of other windows](https:\/\/preview.redd.it\/j2xy5zah1uff1.png?width=445&amp;format=png&amp;auto=webp&amp;s=5a8e9d73777092c031cfe3e3e2d7f76b02e51de3)\n\nI tried opening the executable as with XWAYLAND environment without any changes.   \nI tried GTK themes without any changes.\n\nDo you have any Idea on what could be the issue at hand and how to fix this?",
    //     "commentCount" : 7
    // },
    // {
    //     "id" : "1mgld4l",
    //     "title" : "Question about a microchip ?",
    //     "text" : "",
    //     "commentCount" : 0
    // },
    // {
    //     "id" : "1lvcqd6",
    //     "title" : "I need a microchip Bluetooth module with development board ?",
    //     "text" : "Hope someone can point me in the right direction? ",
    //     "commentCount" : 1
    // },
    // {
    //     "id" : "1ljt8su",
    //     "title" : "need help finding where to buy this chip",
    //     "text" : "IT5702XQN-128\/BX-GBA1\/S its from a motherboard from a computer, i only know aliexpress for things that are not common but i cant find a site where i can fin this chip ",
    //     "commentCount" : 9
    // },
    // {
    //     "id" : "1ktb2wb",
    //     "title" : "Difference between Microchip Fab and Foundry",
    //     "text" : "I was going through the Microchip press release and trying to understand it. I see that they recently closed a Fab. So, Fab is basically where they prepare the die and package it, right? I am not sure though, but they get the Si Wafers from somewhere in East Asia (please correct if I am wrong). So, they get basically the wafers and they have their own fab to make the die, package and finally the chip. What are foundaries then? What do they do? Whether Microchip owns foundaries? Would really be good if someone can clarify this supply chain for me?",
    //     "commentCount" : 3
    // },
    // {
    //     "id" : "1kkwfee",
    //     "title" : "Buying a Vacuum Chamber",
    //     "text" : "I am in my very early teens and have gotten interested in making my own microchips at home from scratch. I have gotten far but I've still been trying to find a vacuum chamber (that supports up to 10^(-6) torr) for under $500 (I don't know if that is possible) to make a Metal Deposition machine, and I decided on evaporating aluminum, because although it oxidizes more, it is cheaper and needs less heat than gold. Thank you so much! (also, I know that doing this will me hard for someone my age, but I think I can do it)",
    //     "commentCount" : 5
    // },
    // {
    //     "id" : "1mnctbs",
    //     "title" : "Powering up a Raspberry pico W with a 3.7 LiPo battery, TP0456 and a MT3608",
    //     "text" : "Hi there!\nSo I'm trying to power up the pico W with a 3.7V LiPo battery, as I read online its better to use a voltage booster when doing this.\nSo I got the MT3608 booster and a TP0456 to charge my battery, I tried to plug everything together but I got some weird results.\n\nWires are like so:\nBattery + (Red cable) -&gt; TP0456 B+\n\nBattery - (Black cable) -&gt; TP0456 B-\n\nTP0456 OUT+ -&gt; MT3608 VIN+\n\nTP0456 OUT- -&gt; MT3608 VIN-\n\n(Raspi is not connected, since the MT3608 OUT voltage is 0V I didnt bother to connect it)\n\nSo the results with the multimeter were kinda odd to say the least, when I checked TP0456 OUT +\/- I read what I expected- the voltage of my battery (around 4V) but here's the weird part when I checked MT3608 VIN +\/- I got only around 1V when I expected to see the voltage of my battery, and the wires between\nTP0456 OUT+ -&gt; MT3608 VIN+\n\nTP0456 OUT- -&gt; MT3608 VIN-\n\nWere crazy hot!\n\nI also read 0V in the MT3608 OUT +\/-\n\nSo yeah now I'm kinda stuck and I dont really know how to get over this problem, I tried to get a new booster and got the same results, I'll mention I'm using standart dupont wires.\n\nTLDR:\nHooked up the MT3608 and the TP0456 and the voltages between the TP0456 OUT and the MT3608 IN are different.",
    //     "commentCount" : 0
    // },
    // {
    //     "id" : "1mmppug",
    //     "title" : "Where to find STM32 peripheral configuration steps?",
    //     "text" : "I’ve previously worked on the Tiva C series boards, where the datasheet itself contained clear step-by-step instructions for configuring peripherals.\n\nFor example, if I wanted to set up UART on Tiva C, the datasheet would tell me exactly what to do:\n\n1. Enable the UART clock in RCGCUART.\n2. Enable the GPIO port clock in RCGCGPIO.\n3. Configure the GPIO pins for alternate function.   and so on.......\n\nI could do almost everything using just the datasheet (and sometimes the schematic).\n\nNow that I’ve switched to STM32, I see there are **four different documents** — Datasheet, Reference Manual, User Manual, and Schematic — and I can’t figure out:\n\n* Which document contains the actual step-by-step peripheral configuration info?\n* In what order should I use these documents when working on a new peripheral?",
    //     "commentCount" : 7
    // },
    // {
    //     "id" : "1mll4gq",
    //     "title" : "Thank you guys for helping me developing this pedal!",
    //     "text" : "I wanted to thank everyone who helped me on this subreddit.\n\nThanks to your help, I studied STM32 a lot in 2025, and after 1000 hours give or take,\n\nI am releasing my second MIDI pedal, the first with RTOS and STM32 (the first was Arduino).\n\n\n\nHere are the full specs:\n\nSTM32F411 microcontroller\n\n1000mah battery\n\n3 physical MIDI ports (In, Out, Out 2) with MIDI thru\n\nUSB MIDI as a device (no host)\n\n\n\n**MIDI Tempo**\n\nSend a MIDI clock signal to either MIDI OUT or OUT 2\n\n**MIDI Modify**\n\nChange either the MIDI channel or the velocity.\n\nHas a split &amp; layer function, velocity can be changed or absolute\n\n\n\n**MIDI Transpose**\n\nPitch Shift: shift all incoming notes up\/down by a specified number of semitones. Optionally mute the original note.\n\nScale Mode (Harmonizer): Select a scale, mode, and interval.\n\nThe pedal generates a harmonized note based on the input. Optionally mute the original note only to hear the harmony.\n\nLink to the GitHub of the project: [https:\/\/github.com\/RomainDereu\/Midynamite](https:\/\/github.com\/RomainDereu\/Midynamite)\n\nI have a lot of room for improvement on this project and will continue to develop it in the future. I'm curious as to which functionalities you guys would like to see in a MIDI pedal.\n\nCheers!",
    //     "commentCount" : 7
    // },
    // {
    //     "id" : "yeerfs",
    //     "title" : "First Blink program. RB3, RA6 and RA7 flash but RA0 to RA5 do not. What is the issue?",
    //     "text" : "Good day, this is my first step into microchip MCUs after Arduino's. \n\nI have a PIC16F570 running off an internal resonator. I am trying my hand at setting up the blink test. I was initially trying to blink RA0 but it would not work. Then I tried RB ports and those worked just fine. Eventually through experiments I discovered that pins RA0 to RA5 do not output at all but anything past that, starting with RA6, woks just fine.\n\n[https:\/\/ww1.microchip.com\/downloads\/aemDocuments\/documents\/OTH\/ProductDocuments\/DataSheets\/40001684F.pdf](https:\/\/ww1.microchip.com\/downloads\/aemDocuments\/documents\/OTH\/ProductDocuments\/DataSheets\/40001684F.pdf)\n\nHere's my sketch code.\n\n    \/*\n     * File:   newmain.c\n     * Author: owner\n     *\n     * Created on October 26, 2022, 4:06 PM\n     *\/\n    #pragma config FOSC = INTRC_IO  \/\/ Oscillator (INTRC with I\/O function on OSC2\/CLKOUT)\n    #pragma config WDTE = OFF       \/\/ Watchdog Timer Enable bit (Disabled)\n    #pragma config CP = OFF         \/\/ Code Protection bit (Code protection off)\n    #pragma config IOSCFS = 8MHz    \/\/ Internal Oscillator Frequency Select (8 MHz INTOSC Speed)\n    #pragma config CPSW = OFF       \/\/ Code Protection bit - Flash Data Memory (Code protection off)\n    #pragma config BOREN = ON       \/\/  (BOR Enabled)\n    #pragma config DRTEN = ON       \/\/  (DRT Enabled)\n        \n    \n    \n    #include &lt;xc.h&gt;\n    #define _XTAL_FREQ 8000000\n    \n    \n    \n    void main(void) {\n    \n        \n        TRISA = 0b00000000;\n        TRISB = 0b00000000;\n      \n        \n        PORTA=0;\n        PORTB=0;\n        \n        while(1){\n            PORTBbits.RB3=1;\n            PORTAbits.RA6=0;\n            PORTAbits.RA2=0;\n            __delay_ms(500);\n            PORTBbits.RB3=0;\n            PORTAbits.RA6=1;\n            PORTAbits.RA2=0;\n            __delay_ms(500);\n        }\n    }\n     \n\nAccording to the documentation, RA0 to RA5 pins can also be as ADC pins so perhaps I am not setting them to be digital. That's my speculation.\n\nI tried ADCON0 = 0; to no avail and ADCON1 gives me a definition error during build, presumably because this mcu does not have an ADC channel 1.\n\nThe LED verified to be alive, polarity is connected correctly, ground is verified and the LED is connected to pin 4 which is RA2.",
    //     "commentCount" : 4
    // },
    // {
    //     "id" : "1mjmoo8",
    //     "title" : "Distributed high throughput bus design",
    //     "text" : "I am working on a scientific data acquisition instrument (low volume, cost insensitive) that uses 64 identical units to collect data. Each unit is based around a RP2350 and produces around 250kB\/s of data that I need to somehow aggregate for further processing and writing to disk. \n\nThe aggregator program is complex enough that I would rather it be running Linux so I have been looking at microprocessors. The problem is that while 250kB\/s is very reasonable for the usual contenders (SPI, UART, I2C) putting all 64 devices on the same bus results in a very unmanageable 15MB\/s. I have yet to find a microprocessor that has even 8 independent SPI peripherals to split up the units and bring the data rates down.\n\nSo my question is, how are such situations typically handled? What peripherals do microprocessors have that let them ingest over 10MB\/s from accompanying custom hardware?\n\nMy search so far has brought me to look at memory controllers (TI's GPMC or ST's FMC among others) that have the bandwidth but are not explicitly designed to receive streaming data. It seems like it might be tricky to treat each of my units as a distinct memory address and issue repeated reads (from my reading it is unclear if such a mode is supported) . The nice thing about using the RP2350 is that the PIO is flexible enough to emulate many simple bus peripherals. My specific application has 11 pins to spare and a full PIO.\n\nAt the end of the day it feels like I can't be the first person to have this problem. Any advice is appreciated... I do know that an FPGA PCIe card in an x86 machine is always an option but I am attempting to keep things simple. I am happy to elaborate on the application if more information would be helpful. ",
    //     "commentCount" : 25
    // },
    // {
    //     "id" : "1mjd8uj",
    //     "title" : "Made (yet another) C++ CLI lib for embedded systems. Works over Wifi for ESP32!",
    //     "text" : "Hey everyone,\n\nI've been putting together a minimal C++ CLI library that I could use across my various dev boards, and after a lot of socket\/telnet debugging pain, I have a fairly stable version working for the ESP32 over Wifi in STA mode. I figured this is probably the best place to share it and get feedback!\n\nThe premise of the CLI is:\n\n\n* Be non-blocking so it can work in super-loop applications\n* Use IO adapters to allow for communication method abstraction\n   * As proof-of-concept I have adapters for ESP32 Serial &amp; Wifi STA (as shown), as well as the Arduino Serial lib.\n* Use templated context injection for the CLI functions so that...\n   * the functions can use drivers\/etc. without a bunch of global weirdness or singletons\n   * you get compile time safety and meaningful intellisense feedback\n   * it's C++ so at least one or two templates are required\n* Have the core be not-OS dependent and bare-metal friendly (although the ESP adapters use FreeRTOS for the drivers)\n\nIn the gif I'm using telnet via PuTTy, and trying to show off the general snappiness and reconnect-ability.\n\nIf you're interested in checking this out, using it, or giving feedback, I'd really appreciate it (new-ish reddit account so its ok I'm doxing myself):\n\n* Library: [https:\/\/github.com\/ryanfkeller\/mcli](https:\/\/github.com\/ryanfkeller\/mcli)\n* Examples: [https:\/\/github.com\/ryanfkeller\/mcli-examples](https:\/\/github.com\/ryanfkeller\/mcli-examples)\n\nOther embedded (C) CLI projects I came across:\n\n* [https:\/\/github.com\/funbiscuit\/embedded-cli](https:\/\/github.com\/funbiscuit\/embedded-cli)\n* [https:\/\/github.com\/matt001k\/openembeddedcli](https:\/\/github.com\/matt001k\/openembeddedcli)\n* [https:\/\/github.com\/Helius\/microrl](https:\/\/github.com\/Helius\/microrl)\n* [https:\/\/github.com\/marcinbor85\/microshell](https:\/\/github.com\/marcinbor85\/microshell)\n* [https:\/\/github.com\/dimmykar\/microrl-remaster](https:\/\/github.com\/dimmykar\/microrl-remaster)\n\nThere are some very strong contenders here, but I think I still carved out a niche with the compile-time safety via templated context injection, non-blocking design, and out-of-the box telnet support. I'm definitely missing some QOL features like tab-to-complete and line editing, but those are on the radar.",
    //     "commentCount" : 4
    // },
    // {
    //     "id" : "1mn7gdk",
    //     "title" : "Tx and Rx lines when using THVD1426DR (Auto Direction control RS 485 transceiver)",
    //     "text" : "When using the THVD1426DR from the datasheet I concluded the Rx is to be pulled up(MCUs Rx), What about the Tx ? The MCU does not ideally keep it high. Should I pull it up ?",
    //     "commentCount" : 12
    // },
    // {
    //     "id" : "1mmzjrc",
    //     "title" : "PCB fabrication at home",
    //     "text" : "Hi, \n\nI'm trying to find an easy way to make double-sided SMD boards. I'm trying to do it at home so that the board can take a few hours to be ready. I don't like the solution of ordering them as it takes time and costs more (assuming I found an affordable way to do it).   \nProject constraints:\n\n1- up to 500 USD\n\n2- A PCB can't exceed a few hours to make\n\nMethod I'm exploring:\n\n1- Metal 3D printing. Print the copper tracks instead of removing. -&gt; stupedly expensive \n\n2- Use [MSLA resin printer ](https:\/\/youtu.be\/RudStbSApdE?si=ABAO-rzo4mwepL9C)looks promising, but I can't have a stencil, and all the copper is exposed. I can apply a coat, but then I need a way to remove the coat from the soldering points \n\n3- Use CNC. This looks promising to me. I can get a stencil and automate everything. I'm afraid that the tolerances could be too high. I saw Reddit posts and YouTube, but I feel that people have different opinions about this.\n\n4- copper electroplating (I have no idea if this makes sense) \n\n5- Conductive FDM printing (I'm still reading about this) \n\nWhat do you think? What ideas should I search for more? ",
    //     "commentCount" : 39
    // },
    // {
    //     "id" : "1mkrl4x",
    //     "title" : "BLDC motor is not spinning continuously",
    //     "text" : "Hi everyone!\n\nA while ago I got a BLDC motor off Amazon, specifically this one:  \n6374 170KV High Efficiency Brushless Motor 2800W 24V\/36V for Four-Wheel Balancing Scooters Electric Skateboards (with Motor Holzer)  \nAbout this item\n\n* KV value: 170KV\n* Power (24V): 2800W\n* Input voltage: 24V\/36V is applicable (higher voltage needs to reduce KV customization)\n* Shaft diameter: 10.0MM\n* Plug: 4.0 banana head\n\nIt has hall sensors, but after testing with MCUs and logic analyzers, I discovered the sensors are faulty, and do not produce correct states for rotation, and also give many invalid states (000 and 111).\n\nI also got an BLDC Controller, 36V 16A because it was the only one available, which has a TF-100 throttle, and needs hall sensor input from motor.  \nI have a 36V Li-Ion battery.\n\nSince the hall sensors on the motor are faulty, i got a 2 phase quadrature encoder. Using an MCU, I converted the 2-phase reading to 3-phase reading based on the angle and number of poles on the motor. The motor has14 poles and is 12-slotted. This works well and I get an accurate mechanical angle and electrical angle, with the only difference being that it will always start from phase A, and the phase alignments will not be accurate on the motor, since I cannot detect it with hall sensors.\n\nNow the issue is I am using these simulated 3-phase hall sensor readings from the MCU to the BLDC controller, produced by the quadrature encoder. At first when I connect everything, the motor spins well as I press the throttle. After a few minutes, the motor wires start heating up slightly, and eventually, the motor will stop spinning. When I press the throttle, the motor seems to draw current since the wires heat up, and tries to spin, but doesn't. The motor controller also heats up. After some time, when I let it all cool down, it can spin again for a few minutes.\n\nAnother issue I encountered was the motor spinning in one direction, and after some time, it started spinning in the other direction, despite using the same motor controller, which is forward drive, and it stayed spinning like that.\n\nHow could I solve this issue, to be able to spin the motor without overheating, at limited speed and torque?",
    //     "commentCount" : 7
    // },
    // {
    //     "id" : "1mkan90",
    //     "title" : "As a newbie, I'm very confused",
    //     "text" : "I learned a little C programming at school, so I know the basics. However, I am not proficient in C programming; in fact, I am still at a basic level. I only know the concepts, the basic keywords, and what they do. That's all. Thinking that this level of C knowledge was sufficient, I wanted to move on to embedded systems and purchased an STM32F407 Discovery. I purchased a few Udemy courses. I am trying to learn STM32 with these courses. However, the thing that keeps bothering me is that \"I don't fully understand C programming.\" This is constantly on my mind. If you asked me to write a linked list right now, I wouldn't be able to do it because I haven't practiced enough. I can’t solve an average Leetcode question in C because I haven’t practiced enough. Is it right to continue with STM32 when I’m not very proficient in C? I’m questioning this at this point. Should I go back and learn C thoroughly, or should I learn it over time while developing projects on embedded systems?\n\nIf I want to learn C, how should I go about it? Should I study books or solve Leetcode problems? I researched Leetcode on this subreddit, and generally people say that Leetcode is unnecessary for the embedded field, but I don't quite understand how I can fully master a programming language without solving algorithm problems.\n\nIn short, my mind is very confused. I would be very grateful if you could guide me with your experience.",
    //     "commentCount" : 55
    // },
    // {
    //     "id" : "1mjub5q",
    //     "title" : "What to learn and from where as a beginner for embedded systems dev?",
    //     "text" : "Hi, I always had an interest in embedded systems, I am even gonna got to Uni Inshaallah to major in CE, but I never got the chance to learn, my older brother did bye me a Arduino starter kit like three years ago, but I never got the chance, since inititally she did not let me do extracurriculars, then I started learning python using CS50's Introduction to Programming with Python, then I started CS50: Introduction to Computer Science, now I am pretty fluent in both python and C, not C++ tho. Can you suggest me some resources to start embedded systems dev, I would appreciate it if those resources had like practical assignments or self projects to actually learn the concepts and not be stuck in tutorial hell. Also I will be starting my 12th grade this September.[](https:\/\/pll.harvard.edu\/course\/cs50-introduction-computer-science)",
    //     "commentCount" : 1
    // },
    // {
    //     "id" : "1mne28i",
    //     "title" : "FreeRTOS -dspic33e Family",
    //     "text" : "Hello,\n\nI am trying to run FreeRTOS (V11.1.0) on a Microchip dsPIC33EP128GP504 using XC-DSC compiler.  \nI have already tested the `PIC24_dsPIC` port from the official FreeRTOS repository and Microchip examples, but they do not work for my device.\n\nThe current `port.c` and `portasm_dspic.S` files either do not have the `__dsPIC33E__` specific context save\/restore macros (`portSAVE_CONTEXT` \/ `portRESTORE_CONTEXT`) or they cause the system to reset repeatedly after starting the scheduler.\n\nCould anyone share a tested `port.c` and `portasm_dspic.S` that are confirmed to work for the dsPIC33EP128GP504 family? ",
    //     "commentCount" : 0
    // },
    // {
    //     "id" : "1mn0kbp",
    //     "title" : "Running Coremark in multiple memory regions",
    //     "text" : "So I am trying to run Coremark on stm32 devices and want to test it in different memory regions(FLASH, TCM, SRAM etc...)\nI decided to use CMSIS-Toolbox with Csolution project structure to allow me to test different compilers seemlesly as well.\nHowever I am struggling to figure out how to only make the Coremark code go into the target memory region, I have thought about making it a static library and modifying the linker files afterwards but I am not sure about the portability since I am implementing ee_printf function with a UART peripheral.\nI have also thought about making a bootloader that writes the code to the memory region specified but then I have no idea how to only load coremark specifically.\nI do not want to use __attribute__ as that will pollute the Coremark source code which I do not want to touch\nHas anyone worked on something similar?\nAny other suggestions?",
    //     "commentCount" : 6
    // },
    // {
    //     "id" : "1mmortn",
    //     "title" : "Best way to share protocol structs\/ enums between C and Python without duplication?",
    //     "text" : "Hi,\n\nI have a .h file that defines a communication protocol for an MCU using structs and enums. Now, I want to write a Python program to communicate using that protocol.\n\nHowever, I don’t want to maintain two separate files (one .h for C and another Python file) because if I update the protocol, I’d have to update both, which can cause synchronization issues.\n\nIs there a way to directly read or import those enums and structs from the .h file in Python? Or is there a tool that can generate both the .h and the .py files from a single source, so I don’t have to worry about one getting out of sync?\n\nThanks in advance for any advice!",
    //     "commentCount" : 16
    // },
    // {
    //     "id" : "1mnmpw6",
    //     "title" : "AI tools for extracting info from reference manuals in embedded development",
    //     "text" : "Does anyone use AI tools (co pilot etc) for extracting information from datasheets\/reference manuals to write boiler plate code for using peripherals? \n\nGoing through the data sheet for MCUs can be quite tedious  so I wondered if there were any tools to do this.",
    //     "commentCount" : 10
    // },
    // {
    //     "id" : "1mneah6",
    //     "title" : "Is keyboard-computer tranmission simplex?",
    //     "text" : "Idk if this is the right sub for this question but I'll ask anyways. So I took in my computer science classes in addition to a search on google that keyboard-computer transmission are simplex. But I've thought about it and it doesn't make sense. \n\n1) Lots of keyboards have rgb that can be controlled right from the computer. That can't be possible with simplex transmission right? Or is there another way.\n\n2) Say you have 2 keyboards connected to a computer. They both have LED's for capslock. If you turn capslock on one keyboard, you'll find that not just the keyboard u pressed LED lights up, but the other one too. So how else could it know that capslock has been opened?",
    //     "commentCount" : 13
    // },
    // {
    //     "id" : "1mk321q",
    //     "title" : "IIoT open standard data object model",
    //     "text" : "**Background**: The startup company I work for is planning for IIoT, and needs a communication API between server and embedded field devices. I've been tasked with specifying the communication format. In the past, I encountered and read about the dlms\/cosem standard for smart metering. In it, there is a book (Blue book), that defines the data objects and interface objects, and also defines their format.\n\n**Progress Made:** I have viewed some of the standards used for industrial communication. I am seeing potential in MQTT combined with Spark plug B, and OPC UA pubsub.\n\n**Question:** The Spark plug B specification does not provide a standard collection of objects representing data measured in the field. Is there an open standard that specifies an object model defining data and commands?\n\n**Edit:** I want to take the time and appreciate this platform. I've been following this subreddit for a while, and it has been instrumental in my development, and my first job. Thank you!!",
    //     "commentCount" : 2
    // },
    // {
    //     "id" : "1mmvltd",
    //     "title" : "STM32H7B3I-DK LVGL issue",
    //     "text" : "Hey! I have encountered a weird behavior of stm32h7b3i-dk with official lvgl. The project is ported directly from lvgl's github with barely any changes. After downloading the project to board everything works fine until I reset the device. Doesn't matter if it's a static 'hello world' or demo_benchmark. After resetting or connecting to power the screen is blank and nothing is displayed. Has anyone encountered something similar? Does someone know how to fix this?\nThe board isn't faulty, because project uploaded from TouchGFX works fine.",
    //     "commentCount" : 1
    // },
    // {
    //     "id" : "1mmpf54",
    //     "title" : "CPU usage x86",
    //     "text" : "To measure the CPU usage percentage, do I need to create an idle process to understand how much the CPU is being used? Something like an HLT instruction and have a timer that calculates how much time the CPU spent in the idle process is what I said correct?\n\n",
    //     "commentCount" : 11
    // },
    // {
    //     "id" : "1mm4h6a",
    //     "title" : "Learning Embedded Systems - Trying to program and design my own device",
    //     "text" : "Hey everyone!\n\nI have some C experience and have worked with Arduino in the past, so I’ve already got the basics down. I’ve also played around with MicroPython.\n\nNow I’m looking to take the next step: programming a simple device with an e-ink display and a temperature sensor. Later, I’d like to add BLE or Zigbee.\n\nThe goal? To learn how to program this type of device and eventually design my own PCB. I want it to be energy-efficient and battery-powered in the long run.\n\nI assume I actually want a nice IDE for this, to learn.\n\nI’ve used Simplicity Studio before and found it to be rubbish.\n\nAfter some research, I’m leaning towards either an STM32 (e.g., Nucleo) or an nRF52 board.\n\nIf I do go the IDE route, which one (STM32 \/ nRF52) is the easiest and most beginner-friendly for getting started without too much pain? I usually rely on YouTube tutorials to learn and troubleshoot (sometimes AI as well lol).\n\n  \nTLDR: What development board to buy and what IDE is best to learn",
    //     "commentCount" : 10
    // },
    // {
    //     "id" : "1mnt3kc",
    //     "title" : "SPI Issue with Reading Data from MAX31865 using STM32F446RE Nucleo",
    //     "text" : "Edit: TL;DR Can't read data from MAX31865 using STM32F446RE Nucleo with SPI always reading 0 yet appropriate pins are being measured correctly with multimeter \n\nHi everyone, I'm trying to interface a MAX31865 converter on an Adafruit breakout board with my STM32F446RE Nucleo board using SPI and I've wired everything according to the [datasheet](https:\/\/www.analog.com\/en\/products\/max31865.html) and then followed the [2 Wire Diagram](https:\/\/learn.adafruit.com\/adafruit-max31865-rtd-pt100-amplifier\/pinouts) from Adafruit at the bottom of the webpage. I interpreted the instructions as soldering the two labeled pads and then simply attaching a wire from one side of the plastic slot to one leg of the RTD element and repeating for a second slot to the remaining RTD element. Then following the MAX31865 datasheet's description on single byte read and single byte write as shown [here](https:\/\/imgur.com\/a\/sCASj6G) (the link also includes screenshots of my IOC and SPI settings) I interpreted the picture as transmitting a byte and then reading the next byte for the single byte read operation and then transmitting two bytes for writing a single byte to the device for configuration. \n\nMy code was written as this:\n\n\n    const uint8_t ConfigReadMax = 0x00;\n    const uint8_t RTDMSBReadMax = 0x01;\n    const uint8_t RTDLSBReadMax = 0x02;\n    const uint8_t FaultStatusReadMax = 0x07;\n    const uint8_t ConfigWriteMax = 0x80;\n\n     void SPIConfigMax(void)\n    {\n\t\/\/ Register addr and data bytes to send to Max\n\tuint8_t ConfigDataMax[2] = {ConfigWriteMax, 0b10100011};\n\n\tHAL_GPIO_WritePin(GPIOA, GPIO_PIN_4, GPIO_PIN_RESET);\t\t\t\t\t\t\t\t\t\/\/ Pull CS low\n\tHAL_SPI_Transmit(&amp;hspi1, ConfigDataMax, 2, 100);\t\t\t\t\t\t\t\t\t\t\/\/ Init SPI Transfer\n\n\tHAL_GPIO_WritePin(GPIOA, GPIO_PIN_4, GPIO_PIN_SET);\t\t\t\t\t\t\t\t\t\t\/\/ Pull CS High; Conversion starts\n\tHAL_Delay(65);\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\/\/ Delay of 65 ms\n    }\n\n    \/*\n     * Read data from Max31865 drop fault bit and convert data into readable format\n     *\/\n    float SPIReadMax()\n    {\n\n\tuint8_t DataMSBMax;\n\tuint8_t DataLSBMax;\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\/\/ Data to read\n\tHAL_GPIO_WritePin(GPIOA, GPIO_PIN_4, GPIO_PIN_RESET);\t\t\t\t\t\t\t\t\t\/\/ Pull CS low to read\n\n\tHAL_SPI_Transmit(&amp;hspi1, &amp;RTDMSBReadMax, 1, 100);\t\t\t\t\t\t\t\t\t\t\/\/ Write address byte to then read from\n\tHAL_SPI_Receive(&amp;hspi1, &amp;DataMSBMax, 1, 100);\t\t\t\t\t\t\t\t\t\t\t\/\/ Init SPI Receive starting at MSB Register then LSB Register\n\n\tHAL_SPI_Transmit(&amp;hspi1, &amp;RTDLSBReadMax, 1, 100);\n\tHAL_SPI_Receive(&amp;hspi1, &amp;DataLSBMax, 1, 100);\n\n\tHAL_GPIO_WritePin(GPIOA, GPIO_PIN_4, GPIO_PIN_SET);\t\t\t\t\t\t\t\t\t\t\/\/ Pull CS high to finish read\n\n\tuint16_t RawDataMaxWFault = ((uint16_t)DataMSBMax &lt;&lt; 8 | DataLSBMax);\t\t\t\t\t\/\/ Combine Raw ADC value into 16 bit value\n\tuint16_t RawDataMaxNoFault = RawDataMaxWFault &gt;&gt; 1;\t\t\t\t\t\t\t\t\t\t\/\/ Drop fault bit\n\n\tfloat ConvertedDataMax = ((float)(RawDataMaxNoFault * RREF) \/ 32768);\t\t\t\t\/\/ Convert Raw ADC value by multiplying with RREF and dividing with 2^15\n\n\treturn ConvertedDataMax;\n    }\n\nHowever when debugging, my results that should be placed into DataMSBMax and DataLSBMax come out to 0 every time yet when measuring my CS Pin it's being pulled high and low when the pin is put in its reset or set state. I'm not sure where I had gone wrong as it does not seem that the hardware is the issue and that it's software. Any help would be much appreciated, thank you!",
    //     "commentCount" : 0
    // },
    // {
    //     "id" : "1mnqbpu",
    //     "title" : "FileX SD standalone, sdmmc1 stm32u545 IO error.",
    //     "text" : "Hi, I wanted to implement FileX standalone with a SD card. I could do file open , file close , file write but whenever i wanted to do file flush on the sd card i have sd io error 144. How can i get through this error? \n\nI have posted about this in st community however no reply\/response from anyone. Did any of you could manage to do it with stm32u5 ? \n\nI have a sd card SDIO breakout board from adafruit and a STM32u545 nucleo board. \n\nhere is the link to my detailed question or current situation from st community.  ",
    //     "commentCount" : 0
    // },
    // {
    //     "id" : "1mnpm31",
    //     "title" : "Simple Assmebly Question",
    //     "text" : "Hi, I am doing the exercises at the end of the 1st chapter of Embedded Systems with Arm Cortex M by Dr. Zhu. (self-studying).  The question is:\n\n&gt;4. Write an assembly program that calculates the sum of two 32-bit integers stored at memory addresses A and B. The program should save the sum to the memory address C. Suppose only three types of instructions are available, as shown below. There are a total of eight registers, named r1, ..., r7.\n\n|**Instruction**|**Meaning**|\n|:-|:-|\n|`Load r1, x`|`xr1`Load a 32-bit integer from memory address   to register|\n|`Store r1, x`|`r1x`Save the value of register   to memory address|\n|`Add r3, r1, r2`|`r3 = r1 + r2`|\n\nI have attached  my answer in the images. I would like to check if it is correct or not.\n\nChatGPT says:\n\n&gt;Load r1, A     ; r1 = value at memory location A\n\n&gt;Load r2, B     ; r2 = value at memory location B\n\n&gt;Add  r3, r1, r2 ; r3 = r1 + r2\n\n&gt;Store r3, C    ; store result in memory location C\n\n* However I do not trust it and would like human confirmation as to whether it or I am correct.\n\n[Answer](https:\/\/preview.redd.it\/u7gsrgb7jgif1.jpg?width=2999&amp;format=pjpg&amp;auto=webp&amp;s=ade813ca42d1d3e0d2c661c557a6c1554d91f82f)",
    //     "commentCount" : 20
    // },
    // {
    //     "id" : "1mlarsq",
    //     "title" : "[Schematics Review] I designed this GPS module with a custom bias-tee and an active antenna",
    //     "text" : "Project Description: A hand-held GPS device with a built-in screen.\n\nI designed the schematics for the GPS part of the system, I made a custom simple bias-tee (since bias-tee modules on digikey are either too bulky or too expensive), the objective here is to have the capacitor reactance less than 50ohms to pass the RF and block DC, and the Inductor to be more than 50ohm (I used 500ohms) to block RF and pass DC. Furthermore, the NAND gate here is to utilize the \"ANT\\_OFF\" feature of the GPS module, when it detects antenna faults (according to the reference sheet). \"ANT\\_OFF\" is normally 0 (Logic), and \"VCC\\_RF\" is obviosuly normally 1. Truth table is shown in the schematics.\n\nI am concerned over the RF part of the design since I've never done any RF before (And still have not taken Microwave Engineering class yet), I appreciate any feedback.\n\nThank you in advance.\n\nhttps:\/\/preview.redd.it\/5auoqee0uvhf1.png?width=1079&amp;format=png&amp;auto=webp&amp;s=b5e82d169c0c6f37f81a6338000b9dca0acb35b7\n\nParts : [GPS Module](https:\/\/www.digikey.com\/en\/products\/detail\/stmicroelectronics\/TESEO-LIV3R\/10222210), [Antenna](https:\/\/www.digikey.com\/en\/products\/detail\/sparkfun-electronics\/23847\/22321062), [Coaxial Connector RF](https:\/\/www.digikey.com\/en\/products\/detail\/cinch-connectivity-solutions-johnson\/142-0701-851\/273369), [NAND Gate](https:\/\/www.digikey.com\/en\/products\/detail\/texas-instruments\/SN74LVC1G132DCKR\/863599), [MCU](https:\/\/www.digikey.com\/en\/products\/detail\/stmicroelectronics\/STM32F446RET6\/5175962)",
    //     "commentCount" : 1
    // },
    // {
    //     "id" : "1mmtshp",
    //     "title" : "Thermal printer serial debugging",
    //     "text" : "I picked up a no-name thermal printer module a bit ago from a surplus store. I've always wanted to mess with one, and my current goal is to get it talking over a serial to usb converter so I can more easily send text to it.\n\nI picked up this [converter cable](https:\/\/www.adafruit.com\/product\/954) from Adafruit, but I've been having some issues getting it to talk to the printer.\n\n* If I connect my Arduino to the printer over serial and print something to the serial port, the printer spits it out with no issue\n* If I connect the converter cable to the Arduino, I can read and write over the serial port using picocom on my linux pc\n* If I connect the converter cable to the printer directly... nothing.\n\nI know my baud rate and other settings are correct - the printer can spit out a debug page with UART info on it. I know the cable works, since it talks to the Arduino, and I know my wiring is correct, since the Arduino can talk to the printer. But something about the cable talking to the printer causes an issue. I've tried using the Arduino IDE and picocom for communication, neither seem to work.\n\n  \nWas wondering if anyone had advice on how to try and debug whatever is going on, and hopefully get this cable talking to the printer.",
    //     "commentCount" : 2
    // },
    // {
    //     "id" : "1mmtmdy",
    //     "title" : "Trending topics in Embedded Systems",
    //     "text" : "I am currently working on Embedded C programming at my job. My company is kind off old school and very slow in adapting new techs. So I wanted to start my self learning journey in Embedded Systems. Please suggest me topics i can skill up and what do you think would the futuristic Technology in Embedded Systems so that I can prepare for that?",
    //     "commentCount" : 29
    // },
    // {
    //     "id" : "1mmnbep",
    //     "title" : "Flashing esp32 Custom PCB",
    //     "text" : "I have this custom pcb that I am trying to flash basic firmware into, at the very least the esp32 firmware. I don’t have a uart port but the board is set up to be able to flash the chip using the usb-c port. Yet when I plug it into my computer with the correct data cable, Arduino doesn’t pick up any signal.\n\nI checked all the connections and there is continuity. I have the correct battery connection and power(not pictured).\n\nHow do I go about flashing it? Or even checking for signs of life. Thanks in advance!",
    //     "commentCount" : 4
    // },
    // {
    //     "id" : "1mmiy3h",
    //     "title" : "FreeRTOS resources",
    //     "text" : "Hello everyone!\n\nI'm a student learning STM32. I recently got into FreeRTOS and have been learning from here and there. Can y'all suggest me some online tutorials or other resources that help me understand FreeRTOS throughly? Also, can y'all suggest some basic to intermediate projects that I can implement to get a better grasp of things?\n\nThankyou in advance!",
    //     "commentCount" : 27
    // },
    // {
    //     "id" : "1mmcfz2",
    //     "title" : "This open-source framework turns an ESP32 into a high-performance voice AI interface",
    //     "text" : "Hey everyone,\n\nWas looking for a solid way to build a voice interface for a hardware project and stumbled on something really impressive: **TEN-framework**. They have a demo showing how to use an Espressif ESP32-S3 Korvo V3 board as the real-time voice front-end for a full conversational AI system.\n\nThe framework is designed to stream audio to and from the microcontroller with very low latency. It runs on a host server and handles all the complex parts of the pipeline—things like high-performance streaming VAD (voice activity detection) and full-duplex turn detection so the conversation feels natural and interruptible.\n\nEssentially, it lets you use a simple, cheap board for the audio I\/O, while the framework orchestrates the ASR, LLM, and TTS services on the backend. This seems like a fantastic solution for adding a proper voice to a custom gadget, a robotics project, or a standalone smart device without having to build the entire complex audio infrastructure from scratch.\n\nThe repo is here if you want to check out the architecture:  \n[https:\/\/github.com\/ten-framework\/ten-framework](https:\/\/www.google.com\/url?sa=E&amp;q=https%3A%2F%2Fgithub.com%2Ften-framework%2Ften-framework)\n\nWould love to hear what you build with it!",
    //     "commentCount" : 0
    // },
    // {
    //     "id" : "1mo0pbn",
    //     "title" : "RS485: Device stops responding randomly",
    //     "text" : "I have been using RS485 to communicate with multiple devices. As in RPi will be my master and other MCUs the slave. The communication is happening properly but the problem is when I am polling the devices back to back some of the device stop responding. Say I am calling n registers from each of the MCU, At first it works fine. Randomly does not respond to some register call and after sometime the device itself stops responding. Why would that happen ?",
    //     "commentCount" : 9
    // },
    // {
    //     "id" : "1mlwz78",
    //     "title" : "Edge AI - generating training data",
    //     "text" : "I've been working on object detection projects on constrained devices for a few years and often faced challenges in manual image capture and labeling. In cases with reflective or transparent materials the sheer amount of images required has just been overwhelming for single-developer projects. In other cases, like fish farming, it's just impractical getting good balanced training data. This has led down the rabbit hole of synthetic data generation - first with 3D modeling in NVIDIA Omniverse with Replicator toolkit, and then recently using generative AI and AI labeling. I hope you find my video and article interesting, it's not as hard to get running as it may seem. I'm currently exploring Cosmos Transfer to combine both worlds. What are your experience with synthetic data for machine learning?   \nArticle: [https:\/\/github.com\/eivholt\/edgeai-synthetic-cosmos-predict](https:\/\/github.com\/eivholt\/edgeai-synthetic-cosmos-predict)   \nHere are some misc embedded prosjects I've done over the years: [https:\/\/www.hackster.io\/eivholt\/projects](https:\/\/www.hackster.io\/eivholt\/projects)",
    //     "commentCount" : 0
    // },
    // {
    //     "id" : "1mo422d",
    //     "title" : "Automate hash publication into layer",
    //     "text" : "I am looking for a solution to ease the burden of publishing changes made in source code repos into our yocto layer.\n\nOur current workflow is as follows:\n1. Merge PR in source code repo\n2. Open\/merge PR in recipe matching the source code repo above to set the new SHA in the recipe\n3. Open\/merge PR in top level repo to pull the latest SHA for the layer repo.\n\nSteps 2 and 3 are trivial and tedious to do.\nI would like to automate them such that when the source code PR is merged the recipe in the layer is updated with the new SHA and the top level repo updated too.\n\nDoes this make sense? It feels like a common use case yet I am not seeing any off the shelf solutions. Or am I missing something?\nAny suggestion on how to do this?\nI’m trying to not reinvent the wheel…",
    //     "commentCount" : 5
    // },
    // {
    //     "id" : "1mo38pz",
    //     "title" : "Custom Shield for H755 nucleo 144 dev board",
    //     "text" : "Do you have any experience in developing a custom shield for the stm32h755 devkit ? ",
    //     "commentCount" : 3
    // },
    // {
    //     "id" : "1mnewjd",
    //     "title" : "Tools for learning embedded - what would you recommend? (OWON, SIGLENT, etc...)",
    //     "text" : "Dear redditors,\n\n  \nI started learning embedded 2 months ago, on the end of the 4th semester (learning computer science engineering), and I think I can finally state that this is the specialization I want to work with in my thesis.\n\n  \nI am doing basic practice tasks (frequency measuring, then display it on 7 segment, PCA projects, GPIO projects, timer projects, etc...), and later I want to start doing more complex projects.\n\n  \nAs I move toward to my thesis, I want to upgrade my equipments, to have the right tools for the project.\n\n**Later I will need:**\n\n* programmable PSU (not premium brands - Riden RK6006 is on the way, right now I am using a horrible, ultracheap modul, which can't safely drive an LCD - later I would upgrade to an RD6006, to have 2 psu)\n* oscilloscope (OWON, Siglent?)\n* multimeter\n* signal generator - FY6900 maybe\n\n\n\nMy question is, what kind of equipments would you recommend to get, if I think in longterm?\n\n\n\nFor oscilloscope, I found I could get an OWON SDS1102, which wouldn't be too expensive, but as far as I know, it's not too accurate, and is lack of protocol decoding, like I2C, UART, etc..\n\n  \nSiglent SDS1104X-E would be a much better oscilloscope, but I'm not sure that if I need it, it's much more expensive.\n\n*  What is your experience with OWON vs SIGLENT? Would you recommend buying the siglent if I think in long term, or it's overkill for the next 3 years?\n* What is your experience with FY6900, and other chinese products?\n* What tools would you recommend for a beginner, who thinks in embedded seriously?\n\n  \nI am looking forward for your advices.",
    //     "commentCount" : 24
    // },
    // {
    //     "id" : "1mkjmsl",
    //     "title" : "Help with SPI, I’m trying to share the SPI pins across multiple RFID readers.",
    //     "text" : "Anyone really good with spi that can give me advice? I’m trying to get 5 RFID readers  (basic mfrc522 units from Amazon) to work with an mcu (STM F411 Black Pill). I’ve got the clock slowed as much as possible. Solid code. Decent solder skills on the perfboard.  The sucky part is that the spi wires between the mfrc522 units are between 2 and 3.5 feet. I can get the two longest working together. But I cant get more than two working at once. \n\nMy question is: how practical is this and how can I best finish my project? I’m testing in STM Cube IDE debug with a pulse_check\/ version_result (I’m not sure if these are universal spi codes or mfrc522 specific) and I can see when a unit is working fine, code (base ten) 146 (there is a hexadecimal equivalent but I don’t have it memorized), when there is no response, 0, and failures of 255 (mosi or pin select not working) and 128 (I’m not sure what this indicates). \n\nI’ve got a 0.1uF and a 10uF capacitor soldered on to the power and ground for each mfrc522 unit for power spikes and such. The perf board is a bit messy because it’s 7 wires per reader, 5 are shared (miso mosi, clock, 3v3, &amp; GND) pics included of what the perf board looks like. \n\nThanks so much for ANY advice!",
    //     "commentCount" : 19
    // },
    // {
    //     "id" : "1mkki84",
    //     "title" : "I made a DIY hotas system for flight sims",
    //     "text" : "Just wanted to share another build I finished today. If you didn't know already, HOTAS stands for hands on throttle and stick. A hotas system usually consists of a throttle quadrant and a joystick quadrant, which are used to control the aircraft. I used a software called Freejoy (open source) for my throttle system. It allows you to program a STM32 BluePill according to however you wired it, without the need to code anything, making it very beginner friendly (I was able to make a working throttle and joystick despite being a teenager with little hardware experience). The joystick quadrant features the the joystick itself (obv) but also a large latching toggle switch for controlling the landing gear. The throttle quadrant has significantly more controls, such as a similar toggle switch for flap controls, as well as rotary encoders and numerous toggle switches. ",
    //     "commentCount" : 2
    // },
    // {
    //     "id" : "1mkk822",
    //     "title" : "I made an alarm clock that detects if you try to sneak back into bed",
    //     "text" : "I built a DIY smart alarm clock using Arduino that makes you solve a randomly selected puzzle or math problem to stop the alarm. You control it with a joystick module, and the puzzles are simple games (math quiz, dodge game, maze). It also has an ultrasonic sensor that watches for movement after the alarm is stopped. If you walk away and then try to sneak back into bed, it detects the change and re-triggers the alarm.\n\nThe main screen shows time, temperature, and humidity, with readings from a DHT11 sensor and a DS3231 RTC module which keeps track of the time even if the alarm clock loses power. Everything is displayed on a 128x64 LCD screen.\n\nIt is easy to assemble, being made of components on a breadboard inside a 3D-printed case, using an arduino mega although boards with &gt;\\~2.5kb dynamic memory (not uno), 5v and i2c capabilities should work.",
    //     "commentCount" : 20
    // },
    // {
    //     "id" : "1mn8fou",
    //     "title" : "AliExpress August promo codes are all updated and ready to go!",
    //     "text" : "[removed]",
    //     "commentCount" : 1
    // },
    // {
    //     "id" : "1mmm19m",
    //     "title" : "Humidity temp sensor , esp32, etc",
    //     "text" : "Humidity \/ temp sensor led, lcd, esp32, soldering\n\n\nThis is my attempt taking my breadboard \/ dev version of my humidity , temp sensor more permanent.  I am new to electronics so am learning how to solder, design circuits, strip board, everything.  \n\nThis version has.\n- power conditional to allow various power supplies (12v, 9v) using buck converter to fixed 5v\n- esp32 mounted sockets\n- I2C header block for up 4 devices\n- 4 row lcd on ic2\n- shtcx temp and humidity sensor on i2c\n- first attempt and addressable rgb led, connected with soldered pigtail\n- photo switch so the led is on only when the room is not dark\n- the software is ESP Home on home assistant.  \n- circuit designed with DIY Layout Creator\n\nI had one bad solder solder on the led strip but other than that it worked the first time (I tested step by step as I went)\n\nMy soldering got better through this project, but it's still not very good.  Also cutting the jumper wires to fit was not all that easy. If anybody has any tutorials or videos, they would recommend on this I would appreciate it.\n\nAnyway, there's a lot of fun. I'm a software guy so hardware is a stretch.\n\nNext is to design and 3-D print and enclosure.\n",
    //     "commentCount" : 3
    // },
    // {
    //     "id" : "1mlhn4x",
    //     "title" : "M-Audio Fast Track Pro – Canal 1 sem áudio (causa inusitada)",
    //     "text" : "ero compartilhar um caso recente que pode ajudar outros técnicos que se depararem com defeito semelhante.\n\nEquipamento: M-Audio Fast Track Pro\nDefeito relatado: Canal 1 sem áudio\n\n⸻\n\nSintomas iniciais\n\t•\tCanal 2 funcionando perfeitamente.\n\t•\tAo injetar sinal no canal 1, nada chegava ao software.\n\t•\tAo tocar no jack de insert do canal 1, o áudio aparecia normalmente.\n\nIsso já indicava que o problema estava antes do insert, possivelmente no conector combo, componentes de entrada ou no caminho até o pré.\n\n⸻\n\nPrimeiros passos\n\t•\tTroca preventiva dos CIs U17, U18 e U20 por JRC4580.\n\t•\tTroca dos capacitores C53, C47, C49, C41 e C86.\n\t•\tInspeção visual em trilhas, soldas e conectores.\n\t•\tTentativa de medir tensões e rastrear o sinal com osciloscópio.\n\n⸻\n\nA grande dificuldade\n\nA M-Audio adotou um projeto de placa em sanduíche: duas PCBs sobrepostas, dificultando (ou melhor, impossibilitando) o acesso a muitos componentes com o aparelho energizado.\nIsso inviabiliza medições diretas no pré ou acompanhamento de forma de onda no osciloscópio, algo crítico em diagnósticos de áudio.\n\nPara fazer qualquer medição, seria necessário:\n\t•\tTrabalhar com extensores de placa (inexistentes no mercado para esse modelo)\n\t•\tOu desmontar parcialmente, mas aí a interface não liga.\n\nEssa escolha de design é péssima para manutenção e atrapalha demais a vida de quem repara.\n\n⸻\n\nSolução encontrada\n\nApós várias tentativas e já com a parte eletrônica revisada, o defeito se revelou mecânico:\n\nUma ponta quebrada de plug P10 estava presa dentro do jack de insert do canal 1, mantendo o contato interno aberto e interrompendo o sinal.\n\nFoi só remover o pedaço do plug que o áudio voltou a funcionar perfeitamente.\n\n⸻\n\nConclusão \n\nA M-Audio poderia:\n\t•\tProjetar a placa de forma que permita acesso aos pontos de teste com o equipamento montado.\n\t•\tFacilitar a manutenção com serigrafia mais clara e layout que não dependa de desmontagem completa.\n\nMesmo sendo um equipamento de custo intermediário, o pós-venda e a vida útil seriam muito maiores com um projeto mais amigável para reparo.\n\nEspero que esse relato ajude outros técnicos e usuários a economizarem tempo.\nSe o seu canal 1 da Fast Track Pro “morreu”, não descarte a possibilidade de algo tão simples (e escondido) quanto um pedaço de plug dentro do insert.",
    //     "commentCount" : 0
    // },
    // {
    //     "id" : "1moi6u5",
    //     "title" : "Rate My “Time and Temp” build tv",
    //     "text" : "Inspired by the Small Town Bank that proudly had a Time and Temp display, back in the 1980s!",
    //     "commentCount" : 0
    // },
    // {
    //     "id" : "1mogr0y",
    //     "title" : "ESP - subwoofer help",
    //     "text" : "\nHey,\n\nI’m currently trying to play a low frequency sine wave (30hz) through this subwoofer. I’ve been messing about with it for a while now and can’t figure out what’s wrong.\n\nIn the pictures you’ll see an esp32 connected to the gnd terminal (red wire) and also connected to an amplifier (black and white cables). The esp is connected to the amp through an RCA jack (left terminal). The left terminal L+ is then connected to coil 1 + (red wire) and L- connected to coil 2 - (black wire). The subwoofer is connected in series via the black wire wrapping round the back. \n\nThe amplifier is also connected to a suitable PSU.\n\nAny help would be appreciated, I’m losing my mind😂",
    //     "commentCount" : 3
    // },
    // {
    //     "id" : "1mofl8f",
    //     "title" : "💡Could you help me sanity-check an idea for making BOM sourcing faster &amp; cheaper?",
    //     "text" : "",
    //     "commentCount" : 0
    // },
    // {
    //     "id" : "1mobocg",
    //     "title" : "How To Record With Multiple High Quality Cameras",
    //     "text" : "Hey all. \n\nI'm doing a project where multiple (5-8) cameras will record the same object simultaneously, for a minute or two. These cameras should be of rather high quality, like 12MP. \n\nI'm not sure how to set this up. Could a raspberry pi handles all this incoming data? could a PC? Are there cameras with internal memories that could save the video (but that are not these huge security cameras)?\n\n  \nThanks a bunch!",
    //     "commentCount" : 2
    // },
    // {
    //     "id" : "1moacrv",
    //     "title" : "[SHOWCASE] LED Matrix Controller: Turn Your RGB LED Panels into a Digital Canvas with C++ &amp; Plugins! [Open Source!]",
    //     "text" : "Hey everyone! 🌈\n\nJust wanted to share a fun project I’ve been building: **LED Matrix Controller** – an open source C++ app that turns your RGB LED panels into a digital art display, info dashboard, music visualizer, and even a retro game arcade. If you’ve got a Raspberry Pi, four 64x64 LED panels and an adapter board laying aorund, this is for you!\n\n# Showcase\n\n[Weather Overview on the left, the AudioVisualizer on the right](https:\/\/i.redd.it\/xyxxn2tenlif1.gif)\n\n# What’s it do?\n\n* **Loads of plugins:** Clocks, starfields, weather, Spotify album art, fractals, Tetris\/AI games, music visualizer, and way more\n* **Control from anywhere:** There’s a web app, a mobile app (Android for now), everything driven by a REST API\n* **Super easy setup:** One-line install script or just grab the latest release and go\n* **Smart scheduling:** Set up the display to change automatically for work hours, evenings, weekends, ect.\n* **Automatic updates:** Stays up to date with new effects and features\n\n# How to get started\n\n**Easiest way:** Put this in your terminal on your Pi (Raspberry Pi 4 recommended, but Pi 3B+ works too) when everything is properly wired:\n\n    curl -fsSL https:\/\/raw.githubusercontent.com\/sshcrack\/led-matrix\/master\/scripts\/install_led_matrix.sh | bash\n\nAnd check out the [GitHub repo](https:\/\/github.com\/sshcrack\/led-matrix) for all the details, manual builds, and pro tips.\n\n# Why did I make this?\n\nHonestly, I wanted my own “smart art wall” that I could customize like crazy – add games, music vis, weather, and info, and have it look GOOD. I was frustrated by the lack of truly flexible, hackable matrix software out there, so I wrote my own (and made it super modular so you can add your own stuff).\n\n# Looking for feedback!\n\n* Got some cool plugin ideas? I wanna here them.\n* Find a bug? Open an issue!\n* Want to contribute? PRs and suggestions are super welcome.\n\nWould love to see pics or videos if anyone gives this a try – or ideas for scenes or features. I’m still actively developing and would love to build some community around this.\n\n**Repo link:** [https:\/\/github.com\/sshcrack\/led-matrix](https:\/\/github.com\/sshcrack\/led-matrix)\n\nThanks for reading!\n\n*P.S. Star the repo if you think this is cool!*\n\n*P.P.S. I'll attach some images of the built project tomorrow*",
    //     "commentCount" : 0
    // },
    // {
    //     "id" : "1moj4h5",
    //     "title" : "How to make compact dc-dc buck converter circuit using ic mp1495?",
    //     "text" : "",
    //     "commentCount" : 1
    // },
    // {
    //     "id" : "1mo9wmy",
    //     "title" : "unwanted noise with diy electret microphone",
    //     "text" : "i recently followed [a video by LAURI’S DIY CHANNEL TV](https:\/\/www.youtube.com\/watch?v=PYx3Y0rpB9U) where he make a pair of small electret microphones\n\ni tried to follow the video as closely as i could (i used the same transistor, capacitor and resistor and i used the same kind of capsule)\n\n[this is what the microphone ended up looking like](https:\/\/preview.redd.it\/l7bx3n1qjlif1.jpg?width=1536&amp;format=pjpg&amp;auto=webp&amp;s=08d1d08fadce87b9086c09ecf545363a4845a49d)\n\nafter a while of messing around and only getting noise, i finally managed to get sound out of it but there was still noise\n\ncurrently the audio output is unusable and i have no idea how to fix it\n\nthe mic is connected to a Yamaha mg10xu, the mixer was purchased in Japan and so it is not grounded(as far as i know) - i believe this might just be the problem\n\ni have checked with a multimeter and the aluminum pipe i use as the body is connected to ground pin on the xlr so it should be shielding it\n\nif you have any questions or suggestions of how i could eliminate this noise i would very much appreciate it\n\nthanks in advance (:\n\n",
    //     "commentCount" : 3
    // },
    // {
    //     "id" : "1moivuf",
    //     "title" : "How to wire voltage regulator and charger.",
    //     "text" : "I’m building my first pack, 4s2p. I’ve wired up the bms, and a 4s usb-c charging board. My project needs 14v. Can solder the voltage regulator to P+ and P-, with the charging board. ChatGPT says it’s okay, but I want to make sure. If it matters here’s the charging board, and the regulator. https:\/\/www.amazon.com\/gp\/aw\/d\/B07T5X5LSH?ref=ppx_pt2_mob_b_prod_image\n\nhttps:\/\/www.amazon.com\/gp\/aw\/d\/B0CHRZ391X?psc=1&amp;ref=ppx_pop_mob_b_asin_title",
    //     "commentCount" : 3
    // },
    // {
    //     "id" : "1mnu5ms",
    //     "title" : "How to get into the design and manufacturing of audio amplifiers?",
    //     "text" : "Hello, how are you, I am very interested in being able to put audio amplifiers of different classes into practice, A-B-AB-C, ETC. I know them, I studied them, but I want to put it into practice and see the behavior of the signal through an oscilloscope and other tools. But I would like if you can recommend a book or text that is oriented towards the theory and practice of these circuits.\nI know analog electronics books that explain amplifiers, and they are not what I'm looking for. My goals are to design, print and create music speakers, although if the efficiency is \"bad\" I just want to make them for fun.\nI found a book that explains all this, it's called \"analog electronics, by Newton c. Vargas\" but I can't download it. If anyone helps me with their ideas, projects, info. Ect. I just want to share this messages for people who like these contents. Greetings from Argentina.",
    //     "commentCount" : 3
    // },
    // {
    //     "id" : "1mjaumr",
    //     "title" : "First project questions",
    //     "text" : "I apologize in advance if this is the wrong place to ask this, but here we go.\n\nI want to make a neck fan device for my classmates, similar to an \"O2Cool\" necklace fan like pictured above. I understand these are VERY cheap to purchase and that I can save myself a lot of trouble by purchasing these rather than making them myself. The class is not exactly about electronics but we did learn soldering, and direct\/alternating currents and the other students found my little neck fan to be neat, so I want to make custom ones to commemorate our class.\n\nAs far as I can tell, it's a blower fan, a power source, and a switch as far as electrical components go. I'd like to make mine with some form of rechargeable battery rather than the disposable AAs the O2Cool uses, and have very surface-level looked into using an 18650 or small LiPo connected to a USB-C charging board.\n\nI suppose to shorten all of that in a few questions; What is recommended for a newer DIYer between a a single 18650 or a single LiPo cell, if I stick to 3.7v on either? I assume the 18650 is \"safer\" as I dimly recall over the years that LiPo cells in airsoft were at times a risky business. Am I missing anything component wise, or with a rechargeable version of the device would it still be; Power source, on\/off switch, blower fan, and now adding a charging controller board?\n\nIf anyone has recommendations I'd be very thankful to hear them.",
    //     "commentCount" : 7
    // },
    // {
    //     "id" : "1mo4hml",
    //     "title" : "Need Help",
    //     "text" : "This were from an old, cheap rc car that used 4 1.5v batteries to run, then I wanna upgrade the battery by using a 9v battery, can I directly hook it up? \n\n\nIt's for our project in school and I'm not really familiar with this, I only know some basics. \n\nThank you! ",
    //     "commentCount" : 7
    // },
    // {
    //     "id" : "1mo1df3",
    //     "title" : "How do you fix a wireless head phone that has zero volume on right side.",
    //     "text" : "Basically the title. I accidentally dropped my head phones from a table in my school and I don't know if I need to resolder or replace the speaker. If I need to resolder can you give me some guides on how to do so? it would be greatly appreciated. even in wired mode the right side has no sound ",
    //     "commentCount" : 4
    // },
    // {
    //     "id" : "1mnv8ga",
    //     "title" : "Help identifying broken component",
    //     "text" : "A button switch from a torch I'm trying to repair is broken. It was pushed from the top to make connectivity from the bottom to the side. \n\nThe broken pieces are pictured. I'm pretty new to this kind of thing and am a bit lost\n\n",
    //     "commentCount" : 1
    // },
    // {
    //     "id" : "1mm96gb",
    //     "title" : "Is there a beginner friendly way for me to make this decoration powered by solar charged batteries? Currently it's powered by 2 C batteries.",
    //     "text" : "I've got one of these decorative traffic lights and I was thinking it would be neat to convert it to solar power like garden lights (off during day, on at night) and put it outside the entrance to my garage. My first thought was maybe just using a solar garden light and soldering the power in but there doesn't seem to be any that run on C batteries. I know the voltage is the same in AA batteries but I'm guessing the amperage is what'll stand in the way of that idea. \n\nAny suggestions? or is this too much for a beginner? ",
    //     "commentCount" : 10
    // },
    // {
    //     "id" : "1mntrp8",
    //     "title" : "Salvaging Nest Protects",
    //     "text" : "",
    //     "commentCount" : 0
    // },
    // {
    //     "id" : "1mnswqh",
    //     "title" : "How to mount SOP14 ICs?",
    //     "text" : "I ordered some  ICs expecting to get DIPs but instead got SOP-14s.  With my limited soldering skills I could probably solder fine wires to one and embed it in glue on the perf board, but is there a better way, eg, a module that has tiny pads that fits the IC and breaks out into something that's easier to use?\n\n[MCP6004-I\\\/SL sop-14](https:\/\/preview.redd.it\/hws44uuw6hif1.jpg?width=2587&amp;format=pjpg&amp;auto=webp&amp;s=18a200fe0c83e07b8c0d46811e7fb022b873088d)\n\n",
    //     "commentCount" : 6
    // },
    // {
    //     "id" : "suz88h",
    //     "title" : "Command-line tool for PIC programming?",
    //     "text" : "Is there a way to program PICs from the command line (under Windows)? I would like to get away from  using the IDE.",
    //     "commentCount" : 3
    // },
    // {
    //     "id" : "1moiewo",
    //     "title" : "12v Pumps and MCU on same 12v supply?",
    //     "text" : "Hi, I am refining a circuit that I use for automatic water changes on my saltwater aquarium. Essentially it is just an ESP32 microcontroller controlling a few relays to turn some pumps on and off. Controlled by some fancy software. Other forms of this have some sensors and stuff, but this is a simplified diagram. I know this system works because I'm already using something similar. But I've had some requests to share my design so I'm trying to refine it a little bit and put together a guide and software for others in the reefing hobby to make their own.\n\nThe main challenges have been around the shared power supply. I would like to drive all of this circuitry from the same 12v source. I'm using a 12v power supply \"brick\" like you'd use for a laptop. The version I use right now is all hand soldered and hacked together a bit so I'm looking for ideas for making it a little more beginner-friendly.\n\nQuestions:\n\n\\- Are WAGO wire nuts appropriate for this use case? I have some with up to 5 conductors so I should be able to power up to 4 pumps\/relays with those. I know they are \"code\" compliant because I've used them in AC outlets, but any other considerations here? Would you trust this in your home or recommend it to others?\n\n\\- Is it recommended to have any isolation between the relay\/pumps and the MCU board? The board I'm currently using (BEVRLink Relay Module) takes a 12v input and has its own regulator. Are there any reliability risks regarding RF interference or noise if this is all contained in the same enclosure?\n\n\\- I may take this a step further and design a custom PCB to optimize space and eliminate the wire nuts altogether. In that case, could I just run 12v traces directly to the COM input of the relay from the same power supply driving the rest of the circuit (MCU and 3v sensors)? Not an expert on PCB design, but that feels a little too easy. I haven't seen any relay boards that do this. Most are isolated for separate power supplies.\n\n  \n\\-----\n\nMeta commentary - This is a repost from r\/AskElectronics because the mods there suck and keep removing it for being off topic???",
    //     "commentCount" : 2
    // },
    // {
    //     "id" : "1mjtnxq",
    //     "title" : "Trying to build a mini-DIN to RCA cable for a weird black-and-white CRT monitor, need guidance or DIY help",
    //     "text" : "Hey, so I’ve got this old Sylvania black-and-white CRT monitor (SY5S1021C) with a 6-pin mini-DIN port on the back. From what I’ve found, it was originally used with a security camera system. I'm *assuming* the port sends power to the camera and receives the video signal back through the same connection.\n\nHere’s what I’m trying to do:  \nHook up an original Xbox to this thing *for the memes*. Yes, really. This is fully intentional. I want to see Halo 2 in crusty grayscale glory.\n\nI don’t have the original camera or the proprietary cables, and I can’t find the pinout anywhere online. I’m open to making a custom cable **if that would even work**, but I need help figuring out:\n\n* Would this kind of mini-DIN cable technically “convert” on its own, or would I also need components in-line?\n* Is it possible to reverse-engineer the pinout safely, or should I just go hunting for someone to help me build this?\n* If I do need help, where would I go for that? Etsy? Discord groups? Forums?\n\nThis is for fun, but I am taking the build seriously. I just don’t want to hear “get a different monitor” this is the monitor. It’s not about practicality.\n\nAppreciate any real help!",
    //     "commentCount" : 1
    // },
    // {
    //     "id" : "1mo6813",
    //     "title" : "PWM fans blowing up outputs",
    //     "text" : "",
    //     "commentCount" : 0
    // },
    // {
    //     "id" : "1mo42zc",
    //     "title" : "Schematic \/ wiring diagram software",
    //     "text" : "What is the simplest software to make easily edited diagrams of wiring schematics?\n\nI won't be making pcbs, it's just for personal reference during prototyping. atm I just use eg draw.io or Inkscape, but it's awkward to edit. \n",
    //     "commentCount" : 8
    // },
    // {
    //     "id" : "1mns4aj",
    //     "title" : "Use a motorcycle throttle trigger as a speed controller",
    //     "text" : "I’ve had this idea and design in my head for a long time. Lately, it’s been driving me crazy, and I want to find a solution.\n\nSee the picture — I want the electronic control to look like this - a motorcycle throttle trigger, or very close to it.\n\nHere’s what I’m trying to achieve:\n\nBigger trigger (the clutch) → 3 speed settings:\nLevel 1: Slow (default) = 30 RPM\nLevel 2: Faster = 60 RPM\nLevel 3: Fastest = 90 RPM\nSmaller trigger → Variable speed control:\nFrom Slow: adjustable from 30 RPM up to max speed\nFrom Faster: adjustable from 60 RPM up to max speed\nFrom Fastest: adjustable from 90 RPM up to max speed\nI’ve searched extensively and haven’t found an off-the-shelf solution. My hunch is this could be done through a Raspberry Pi.\n\nMost important → I want the controller to look like a motorcycle throttle trigger, or very close to this configuration. \nYour constructive criticism, comments and feedback are appreciated!\n\nThank you!",
    //     "commentCount" : 2
    // },
    // {
    //     "id" : "1mnqkbv",
    //     "title" : "Work just gave me three of these",
    //     "text" : "Out of nowhere. My manager asked me if I had any use for these lol",
    //     "commentCount" : 1
    // },
    // {
    //     "id" : "1mnplmj",
    //     "title" : "At least I know they work",
    //     "text" : "I bought a bunch of these 16-segment displays on eBay for a couple of projects. They're hellaciously-bright even at 11 mA, and the linear regulator on that 5v power supply is surprisingly-hot after a few seconds. ",
    //     "commentCount" : 22
    // },
    // {
    //     "id" : "1mnnero",
    //     "title" : "Protection for solenoids on bench supply",
    //     "text" : "I'm gonna be building something with a bunch of solenoids, and it'll be handy to test them (and the surrounding mechanical bits) on my bench supply. It's an older but pro model, should I assume it'll handle flyback from the solenoids or should I do a little surgery and add a diode between the clips?\n\n\nEdit: they're pretty small 12v jobbers",
    //     "commentCount" : 0
    // },
    // {
    //     "id" : "1mnn1q9",
    //     "title" : "Can someone recommend me a fuzz pedal schematic I can breadboard with discrete components and a 9V power supply?",
    //     "text" : "",
    //     "commentCount" : 2
    // },
    // {
    //     "id" : "yhhnqw",
    //     "title" : "How would one make use of these programmer pins without losing ability to flash the pic mcu?",
    //     "text" : "",
    //     "commentCount" : 15
    // },
    // {
    //     "id" : "t0jrvd",
    //     "title" : "PIC: using RA0 and RA1 as outputs",
    //     "text" : "I am switching from a 20-pin PIC device to a 14-pin device. As a result, I now want to use RA0 and RA1, which were previously unused, as output pins. Is this safe to do with a PICKit4 programmer? I am assuming that those pins on the programmer are driven by tri-state devices that revert to a high-impedence state once programming is complete. Those pins in the target circuit are connected to grounded LEDs through current-limiting resistors of a few hundred ohms. Will the programmer have sufficient output to perform the programming with these loads? There are no series diodes, pull-ups or capacitors on those lines, and I do not intend to use the debugger.\n\nWill this work? Any other recommendations? Thanks!",
    //     "commentCount" : 1
    // },
    // {
    //     "id" : "snwodg",
    //     "title" : "Using 1-wire between two PCBs?",
    //     "text" : "I have a simple project that I'd like to make using a couple ATTINY1626. The first PCB flashes some LEDs in a sequence, and once that sequence is complete, the same thing happens on the second PCBs. (both PCBs are identical in design and software). When the second PCB is done, the first starts flashing again.\n\nI think that maybe I could use 1-wire and connect the two PCBs with 18awg wire since the wire length will only be a couple feet at the very most.\n\nDoes this sound feasible? Is there some other protocol or technology that I should be looking at?",
    //     "commentCount" : 3
    // },
    // {
    //     "id" : "snm1ut",
    //     "title" : "Help programming SAMD21 in C?",
    //     "text" : "",
    //     "commentCount" : 0
    // }
];
