export const comments = [
    {
        id: "hyb0i6l",
        body: "Which PIC are you using? \n\nWhich version of assembler?\n\nWhat exactly is the error message?",
        parentId: null,
        postId: "sn2o3f",
    },
    {
        id: "hybiy0g",
        body: 'I am using a 16F15344 with version 2.32 of the XC8 Assembler.\n\nThe error message is simply "error: (876) syntax error". This is followed by complaints about a missing ENDIF, but these seem to be just a consequence of the failure to parse the IF statement.',
        parentId: "hyb0i6l",
        postId: "sn2o3f",
    },
    {
        id: "hv64gi8",
        body: 'Update : surprisingly I just managed to find solutions to both issues. I will not remove the post though but retain as it may be beneficial in case someone has similar problems.\n\nWriting the contents of EEPROM with 0&gt;1 bit problems:  It turned out it may be a bug in the PICKit software. The issue only happens when only the EEPROM is written. In case *both* the Program Memory and the EEPROM is programmed everything works.\n\nRemoving Code Protection : External power of 5.2V and the following sequence : Erase - Unclicking of "Enable Code Protect \/ Data Protect", Erase. As a result the PIC is now back to squre one with all addresses showing FFFFFFs and FFFFs and programmable.',
        parentId: null,
        postId: "si0otn",
    },
    {
        id: "hv7xsc3",
        body: "Thanks for updating, always useful.",
        parentId: "hv64gi8",
        postId: "si0otn",
    },
    {
        id: "hvamebl",
        body: "It may just be coincidence, but I've had *terrible* luck programming much of anything with my two PICKIT3s. Use a PICKIT2 or -4, and everything works fine. I think there's something wonky with the PICKIT3s (and mine are both from Microchip.)",
        parentId: null,
        postId: "si0otn",
    },
    {
        id: "hvap2h5",
        body: "In fact I was close to give up on my Chinese clones and buy a genuine Microchip PICKit3. However as you pointed out those may not be perfect either, interesting.\n\nTo make it more fun one of my Chinese ones is 3.0 while the other one is 3.5. Overall the lesson is learned; sometimes it is a trial and error process to make them work.\n\nShould I face more issues I will simply go for a v2 or v4 based on your comment, appreciated.",
        parentId: "hvamebl",
        postId: "si0otn",
    },
    {
        id: "ix8ergv",
        body: "Something like this:\n\n[https:\/\/www.amazon.com\/Magnifying-Magnifier-Equipement-Adjustable-Gooseneck\/dp\/B07Z7XMDG5](https:\/\/www.amazon.com\/Magnifying-Magnifier-Equipement-Adjustable-Gooseneck\/dp\/B07Z7XMDG5)\n\nI take a photo with my cell phone to read the package markings. Then expand to see fine details.",
        parentId: null,
        postId: "z0xunm",
    },
    {
        id: "hmoxwq7",
        body: "Maybe contact the author that YouTube video and see if they will share the source code with you. Then modify it to run on PIC18F4580. Good luck!",
        parentId: null,
        postId: "r5qlzm",
    },
    {
        id: "hmlulwi",
        body: "Install mplabx , learn c or picasm \n\nAnything more specific",
        parentId: null,
        postId: "r5a4mr",
    },
    {
        id: "hmmn44x",
        body: "Yes.",
        parentId: null,
        postId: "r5a4mr",
    },
    {
        id: "hmmrlpb",
        body: "Or MikroC.",
        parentId: null,
        postId: "r5a4mr",
    },
    {
        id: "hps9jgc",
        body: "There is also assembly language.",
        parentId: null,
        postId: "r5a4mr",
    },
    {
        id: "jvdzutx",
        body: "Are you using MCC? Or are you using your own bare code?",
        parentId: null,
        postId: "15auh5e",
    },
    {
        id: "jtocn2y",
        body: "this is a very complicated subject😅 thanks for showing interest in helping guys sorry for the late update i was trying to solve it on my own with my team before anything else",
        parentId: null,
        postId: "15auh5e",
    },
    {
        id: "jtmvouv",
        body: "Can you elaborate more?",
        parentId: null,
        postId: "15auh5e",
    },
    {
        id: "jtn7v0l",
        body: "Yeh you should share more detail",
        parentId: null,
        postId: "15auh5e",
    },
    {
        id: "jtnsuro",
        body: "What pic and what compiler?",
        parentId: null,
        postId: "15auh5e",
    },
    {
        id: "jtqfsn0",
        body: "What compiler are you using? XC8 does not support C++.",
        parentId: null,
        postId: "15auh5e",
    },
    {
        id: "ju5x2e8",
        body: "Narrow down your problem .\n Does the XC8 compiler not work for you\n Are you having trouble compiling with no errors? Does the compiler work and you can't get the chip to work with your compiled c code? You left too many questions on the table for anyone to effectively help you.",
        parentId: null,
        postId: "15auh5e",
    },
    {
        id: "jqaxoz2",
        body: "Have you tried contacting tech support at Microchip? That's asking for a pretty extensive list.\n\nThe only answer that I know is that almost every 8b PIC for 15yr has had at least one timer that can run during sleep (not counting the watchdog timer). Newer devices like PIC16F1xxxx (one followed by 4 digits) have more different peripheral clocking options, including run-in-sleep.",
        parentId: null,
        postId: "14nzkr3",
    },
    {
        id: "jqbdxay",
        body: "I recently saw the DOZE feature, which runs the core at Fosc\/4. Interesting for some applications. The timer running in sleep, in the case I saw, requires an external oscillator or 32.768kHz crystal and the timer runs in async mode. Both make sense, and present interesting use cases. Wake up time for some devices is pretty high, I think I saw millisecond range, but others are single-digit microseconds.",
        parentId: "jqaxoz2",
        postId: "14nzkr3",
    },
    {
        id: "jqc2p7x",
        body: "The DOZE feature runs even slower than fosc\/4 (iirc it's fosc\/128). Newer devices can also leave the oscillators running (w\/wo the connected peripherals) when the cpu sleeps, saving the power consumption of the memories. If you need simple logic functions, the CLCs are active all the time.",
        parentId: "jqbdxay",
        postId: "14nzkr3",
    },
    {
        id: "jqc3uha",
        body: "I think you will just have to go through the data sheets of all the parts you are interested in to get that specific information.",
        parentId: null,
        postId: "14nzkr3",
    },
    {
        id: "ms0c6qx",
        body: "This subreddit is for Microchip brand products.",
        parentId: null,
        postId: "1kkwfee",
    },
    {
        id: "mrxwf9y",
        body: "Sir, this is a Wendy’s.",
        parentId: null,
        postId: "1kkwfee",
    },
    {
        id: "mw3wohq",
        body: "Ambitious venture. Can't provide any help but good luck!",
        parentId: null,
        postId: "1kkwfee",
    },
    {
        id: "mwdiuac",
        body: "Thank you!",
        parentId: "mw3wohq",
        postId: "1kkwfee",
    },
    {
        id: "mrxzzjz",
        body: "might have to make it.",
        parentId: null,
        postId: "1kkwfee",
    },
    {
        id: "n7tik1e",
        body: "when the ones available dont do what you need, but the underlying hardware can.",
        parentId: null,
        postId: "1mlwd75",
    },
    {
        id: "n7u56mj",
        body: "This is good advice; I would add a corollary -\n\n\nIf the existing driver implements EVERYTHING and is too complex because of it, it might be worth it to write a simple wrapper to simplify setup and configuration for your usage.",
        parentId: "n7tik1e",
        postId: "1mlwd75",
    },
    {
        id: "n7tn3r9",
        body: "I'd probably pick the closest and gut it where necessary.\n\nI can only think of one time I went Tabula Rasa on a device driver of any kind, and I was doing it to show someone how device drivers get made.",
        parentId: "n7tik1e",
        postId: "1mlwd75",
    },
    {
        id: "n80ut8g",
        body: "If I'm looking to watch someone demonstrate how device drivers get made, do you have any recommendations?",
        parentId: "n7tn3r9",
        postId: "1mlwd75",
    },
    {
        id: "n84c90v",
        body: "Can always look at a vendors HAL and datasheet side by side",
        parentId: "n80ut8g",
        postId: "1mlwd75",
    },
    {
        id: "n86nr18",
        body: "That, and when the available ones use infinite layers of abstraction that add no value to your PARTICULAR application, but rather debug complexity and unnecessary cpu load.",
        parentId: "n7tik1e",
        postId: "1mlwd75",
    },
    {
        id: "hs4nly9",
        body: "I would recommend getting a PICkit4 from Microchip as it will program &amp; debug any device they make. All of the software tools you will need are free from Microchip. You should get MPLAB X IDE, XC8 C Compiler, and MCC code generator plug-in for MPLAB X. That will let you do 99% of what you need.\n\nAlso check out their free Microchip University classes. There are several that will help you get started.",
        parentId: null,
        postId: "s0sx71",
    },
    {
        id: "hs7lq6b",
        body: 'The little "nano boards" are under $20—built-in programmer.\n\nI use a handful of them for "rapid tools" Often, I am injecting signals, measuring things, but I want something battery-powered and straightforward. Nano with a bit of programming makes for simple test measurements.\n\n[https:\/\/www.microchip.com\/en-us\/tools-resources\/evaluation-boards\/curiosity-nano](https:\/\/www.microchip.com\/en-us\/tools-resources\/evaluation-boards\/curiosity-nano)\n\nMy biggest challenge is, "wtf do you mean, an 12 month lead time".  Meanwhile, I can snag a nano, expansion board, and have 100s of CLICKs around so I can rapidly prototype. \n\nI remember seeing a complete video game on a PIC24 running Donkey Kong? Created by a brilliant Russian guy, I can not remember his name.  I kept thinking there is no way he did that on a PIC24, but he just did it. So running PERL, I\'d think PIC32, but I\'m not the sharpest person around here!',
        parentId: "hs4nly9",
        postId: "s0sx71",
    },
    {
        id: "hs8acqo",
        body: "What's a CLICK, and why would you have hundreds?\n\nSounded like something I'd like then I clicked the link and didn't understand a word of it.  Maybe I am spoiled by PICAXE.  With PICAXE I make a serial interface with two resistors, then connect it to the serial port of my PC and download the program from the PICAXE editor directly into the MCU chip.  I don't have to navigate any modules (whether hardware of software) or \"development environments\" or any hoo hah of that sort.  Write program, connect, download.  So I was imagining there must be a thing like that for PIC chips too, maybe with a ZIF socket.",
        parentId: "hs7lq6b",
        postId: "s0sx71",
    },
    {
        id: "hs8gfej",
        body: "Microchip Curiosity Board are older. Embedded programmer with a DIP socket. ZIF sockets are expensive, so they used a plain socket. \n\nSame idea, but I'm using a USB connection to the Nano boards\n\nhttps:\/\/www.microchip.com\/en-us\/development-tool\/EV10N93A\n\nClick Boards:\n\nhttps:\/\/www.mikroe.com\/click\n\nAdapter board allows me to use three CLICK boards with one nano.\n\nIn the past I'd do a board, surface mount, solder and prototype. Even with cheap $10 china board it still needed a few weeks.  With a nano &amp; a few CLICK boards I can go from thought to working prototype in an afternoon.   Having debugged hardware (plug in modules) makes it fast.",
        parentId: "hs8acqo",
        postId: "s0sx71",
    },
    {
        id: "hs87ue7",
        body: "Thanks!",
        parentId: "hs4nly9",
        postId: "s0sx71",
    },
    {
        id: "hs6ce44",
        body: "I recommend going with an AVR or even better SAM. I have used all of PIC, AVR, and SAM for work and hobby and of them all I think AVR is the easiest and most pleasant (documentation, build tools, and ide) to use, but SAM parts are only slightly more complex but much more powerful.\n\nEdit: with AVR and SAM parts you can use standard JTAG tools. I think segger has an education edition debugger for less than $100 and the ATMEL ICE supports AVR and SAM parts and it's just a little more than $100.",
        parentId: null,
        postId: "s0sx71",
    },
    {
        id: "hs8dhil",
        body: "I gather that AVR and SAM are both single-chip MCUs, too?  My question was about PIC, but thanks for the suggestions.",
        parentId: "hs6ce44",
        postId: "s0sx71",
    },
    {
        id: "hs98xyf",
        body: "I gotcha. My advice is to avoid PIC but I understand if that's not helpful.",
        parentId: "hs8dhil",
        postId: "s0sx71",
    },
    {
        id: "ht5txg6",
        body: "I wouldn't avoid PIC.  They have the widest selection of micros and peripherals of any micro range on the market.  They have open libraries with full source code available, for free, and a great selection of dev tools and hardware.\n\nPIC use the MPLABX IDE, which AVRs will also eventually use, so your learning curve is well invested.\n\nThe MCC tool for configuring is excellent, no need for data sheets. (I know AVR has START, but it isn't quite up to MCC.)\n\nAVR definitely has it's strengths - the touch library is second to none.  But don't avoid PIcs.",
        parentId: "hs98xyf",
        postId: "s0sx71",
    },
    {
        id: "ht2xpr6",
        body: "Well, that I am coming from PICAXE may be the giveaway -- I want to repeat things that I have done on PICAXE only with the advantages that going straight to the *same chip* may have, without any significant changes to any hardware the chip happens to be built into.\n\nIf you are unfamiliar with PICAXE, a PICAXE chip is just a regular PIC chip pre-programmed to download a compiled a PICAXE programme via RS232 (with the aid of two resistors for level conversion).\n\nI would be interested in hearing why you prefer things other than PIC, but that's separate from my question is all.",
        parentId: "hs98xyf",
        postId: "s0sx71",
    },
    {
        id: "j993515",
        body: "Have you any experience with the free AVR compiler? Thoughts on that would be nice if you have any.",
        parentId: "hs6ce44",
        postId: "s0sx71",
    },
    {
        id: "j9ahmt0",
        body: "The only free AVR compiler I have used is avr-gcc. Not sure if that's the one you're talking about, but gcc is basically *the* compiler and you'll have a hard time finding a better on.",
        parentId: "j993515",
        postId: "s0sx71",
    },
    {
        id: "hs4z74f",
        body: "XC16 and XC32 if you working with bigger chips. I do recommend some chip sockets for quicker development.",
        parentId: null,
        postId: "s0sx71",
    },
    {
        id: "hs674uv",
        body: "What company do you work for",
        parentId: null,
        postId: "s0sx71",
    },
    {
        id: "hs8b9zb",
        body: "I'm open to offers.",
        parentId: "hs674uv",
        postId: "s0sx71",
    },
    {
        id: "ht5tghf",
        body: "Where abouts are you based? If UK, I can send you a Curiosity Nano - excellent little dev board.  Built in programmer\/debugger.",
        parentId: null,
        postId: "s0sx71",
    },
    {
        id: "htzttkb",
        body: "Canada.  We share a monarch but not any geography. :)  Thanks though!",
        parentId: "ht5tghf",
        postId: "s0sx71",
    },
    {
        id: "hqwjlex",
        body: "You will absolutely need to connect ground, the error is because you are not connecting Vdd, I would connect that too.\nI would also add a pull-up resistor to the MCLR line.\nSo connect up all 5 wires, it will likely work, if not, add a pull-up to.",
        parentId: null,
        postId: "ru2qe2",
    },
    {
        id: "hrkb608",
        body: "Yes! Thank you very much it worked!",
        parentId: "hqwjlex",
        postId: "ru2qe2",
    },
    {
        id: "hqwnw88",
        body: "Yes all are required for programming PIC. See data sheet and don’t forget configuration bit setting.",
        parentId: null,
        postId: "ru2qe2",
    },
    {
        id: "hqwrbj3",
        body: "Also note that the MPLAB PIC power settings only seem to take effect after you shutdown and restart MPLAB.",
        parentId: null,
        postId: "ru2qe2",
    },
    {
        id: "hp4tj7e",
        body: "Edit: turns out some more knowledgeable folks have a great answer to that question:\n\nhttps:\/\/www.microchip.com\/forums\/m1143675.aspx\n\n\"Figaro IDS 7\" is the tool, and it's quite old.\n\n---\n\nOriginal comment:\n\nThis article suggests you need \"Atmel FPGA Designer 6.0\": https:\/\/www.eetimes.com\/atmel-and-synplicity-provide-synthesis-solution-for-at40k-freeram-fpg\/\n\nThat was in 1999... I'm not seeing anything about a newer version of that software or where its functionality might have gotten rolled into. It looks like the bitstream has been documented, but support hasn't been added to any open-source projects.\n\nIf you want to try your luck, here's a sketchy download link:\nhttps:\/\/atmel-ids.software.informer.com\/",
        parentId: null,
        postId: "rjodji",
    },
    {
        id: "hp4uar5",
        body: "Thanks, I'll give it a try tomorrow. You reassure me a bit that I find anything with some stuff from 1999 haha",
        parentId: "hp4tj7e",
        postId: "rjodji",
    },
    {
        id: "k1z3qmk",
        body: "Unless you have a very, very, very specific use case, the real world difference between anything less than a microamp is essentially nil.  Self discharge of even a CR2032 is more significant and will ultimately dictate the lifetime of the product.\n\nOnce you're into the nanoamps, it really does become pretty irrelevant unless **maybe** you're on a space craft.  I've seen projects where they counted nano-amps, but they're extremely niche.",
        parentId: null,
        postId: "14nzkr3",
    },
    {
        id: "n84relm",
        body: "Do you have the schematics?",
        parentId: null,
        postId: "1mmnbep",
    },
    {
        id: "n8a88mg",
        body: "Just curious here, what are some examples of what you could use this for?",
        parentId: null,
        postId: "1mnrma7",
    },
    {
        id: "n8bxm74",
        body: "I tried modding the scan example once, and found that the limit was in the non-open part of the ESP-IDF libraries, and was like 15 or 20 SSIDs, with no way to get the rest.",
        parentId: "n88nj93",
        postId: "1mnrma7",
    },
    {
        id: "n86ox31",
        body: "\\&gt; However I do not trust it\n\nHave you tried it to see if it works ??",
        parentId: null,
        postId: "1mnpm31",
    },
    {
        id: "n8bey4p",
        body: "No I did not know there were such things as emulators! So will use on the future.",
        parentId: "n86ox31",
        postId: "1mnpm31",
    },
    {
        id: "n8bkivm",
        body: "Do you have the assembler working ? Do you know how to use the assembler\/compiler ??\n\nMaybe do this first. Compile and load a simple LED blink program. Or maybe a serial port test program.\n\nOnce you know how to load code, then you can try out the assembler.\n\nOnce you have this in your head, you can just look at a program and see if it will work.\n\nShitGPT will not teach you anything. Except how to cheat.",
        parentId: "n8bey4p",
        postId: "1mnpm31",
    },
    {
        id: "n8c04gz",
        body: "I have not had a chance today to look at it again, as I do language classes. And I know chatgpt is fairly useless, hence why I asked here.\n\nI will also have an FPGA dev board with arm processors on it soon, so will look to build the LED programme, and run on that. \n\nThank you for the advice, appreciate it. I'll let you know how I get on!",
        parentId: "n8bkivm",
        postId: "1mnpm31",
    },
    {
        id: "n8c5tp7",
        body: "Use cpulator!!!! I also have an assembly course and I do not have the hardware at home and the lab is getting renovated. Cpulator has support for arm cortex m and you can see registers, memory, step in, like a normal assembly ide",
        parentId: "n8c04gz",
        postId: "1mnpm31",
    },
    {
        id: "n872qsn",
        body: "Onlinegdb.com allows you to write programs in assembly and run them.  Best way to confirm your outcome, in my opinion.",
        parentId: null,
        postId: "1mnpm31",
    },
    {
        id: "n87no1n",
        body: "Does it have a switch to change from x86 to Arm assembly?",
        parentId: "n872qsn",
        postId: "1mnpm31",
    },
    {
        id: "n88ilfh",
        body: "Thank you I will look into this!",
        parentId: "n872qsn",
        postId: "1mnpm31",
    },
    {
        id: "n86nlbh",
        body: 'Why do you load a value into a register and then "reload" that value into a different register?',
        parentId: null,
        postId: "1mnpm31",
    },
    {
        id: "n88jc43",
        body: "I think I am confusing addresses with address values. I was attempting to follow an example given in the book. I will post later.",
        parentId: "n86nlbh",
        postId: "1mnpm31",
    },
    {
        id: "n86mqwb",
        body: "You should not be using AI to solve this problem for you, it defeats the point of learning to program.\n\nTo learn to program you must learn how to solve problems and this means starting at the basics and building up your ability until you can create complex solutions to problems.\n\nYou must also learn how to test that the programs you create work and not rely on asking Reddit.\n\nAs the solutions you create get more and more complex, it will be impractical to post them to Reddit and ask people to confirm they are right!\n\nYou must take responsibility for your own learning and also take responsibility for verifying your solutions.\n\nMost programming problems can be solved by breaking the problem up into smaller chunks until you understand how to solve each chunk. Then building up your overall solution by connecting the solutions to the smaller chunks, testing each stage as you go.\n\nThe problem you have been given here is: add a number stored in memory address A to a number stored in memory address B and store the result in memory address C.\n\nYou've been given three instructions to work with, a load from memory to a register, a store to memory from a register and an instruction to add two registers together.\n\nCan you work out a way of combining these instructions (and not necessarily limiting yourself to a single usage of each instruction) to achieve the overall goal?",
        parentId: null,
        postId: "1mnpm31",
    },
    {
        id: "n86nwhf",
        body: "OP already took a stab at a solution and then used AI to check it, then posted here for feedback.",
        parentId: "n86mqwb",
        postId: "1mnpm31",
    },
    {
        id: "n86olyd",
        body: "You can't trust AI to test software, you must learn how to test it properly.",
        parentId: "n86nwhf",
        postId: "1mnpm31",
    },
    {
        id: "n89dlp1",
        body: "&gt;However I do not trust it\n\nDid you try reading the OP?",
        parentId: "n86olyd",
        postId: "1mnpm31",
    },
    {
        id: "n8cnmfc",
        body: "Please sybau...",
        parentId: "n86olyd",
        postId: "1mnpm31",
    },
    {
        id: "n88ibej",
        body: "I didn't use AI to solve it. I am self studying. I did it myself and used AI to see if my answer was correct. I have even attached my attempt at the solution-as wrong as I now know it is!",
        parentId: "n86mqwb",
        postId: "1mnpm31",
    },
    {
        id: "n87rmq8",
        body: "After you perform the addition, you have the value in rs, so why move it to rb? This seems redundant and an extra cycle for nothing.\n\nYou also never specifically loaded address C into r7, so where did it get saved to?",
        parentId: null,
        postId: "1mnpm31",
    },
    {
        id: "n88dyz9",
        body: "Seems to me you need to declare your address constants, then your value constants, before executing any code.  \n\nYour logic looks ok, but I've never programmed ASM for a microprocessor; those register references don't fit my experience ;-) but as I said, in this context there is none.\n\nThe ASM code I've written before would say \"logically sensible\" but my bet is it won't compile like that.",
        parentId: null,
        postId: "1mnpm31",
    },
    {
        id: "n88sw6b",
        body: "You obviously don't understand what you're doing, and that's because you don't have context. I've had a look at the book's table of contents and it doesn't seem to contain a chapter explaining what a CPU and an ISA are.\n\nYou need to learn the following:\n\n* A CPU block diagram showing its \"real estate\" (e.g. registers) and its relationships with the rest of the system (e.g. memory).\n* What the core of its instruction set consists of (e.g. with RISC-V, it's RV32I, \\~50 instructions). This will help you grasp what a CPU can do and how it can do it.\n* Pick any microcontroller's reference manual and have a look at the memory map to understand how the memory space is used.\n* GAS (GNU assembler)'s syntax and pseudo-instructions.\n\nWith this knowledge, you will be able to understand why you only need 4 instructions to implement you exercise (plus 3 pseudo-instructions to make it clearer).",
        parentId: null,
        postId: "1mnpm31",
    },
    {
        id: "n88xrcj",
        body: "Ok thank you for the help. Appreciate it.",
        parentId: "n88sw6b",
        postId: "1mnpm31",
    },
    {
        id: "n7jbdfi",
        body: "0 is just reading all lows (MISO held low) and 255 is reading all highs (MISO held high).\n\n\nHow are you handling the chip select? And what's the 7th pin?\n\n\nYou can try termination resistors, but I doubt that's the reason.",
        parentId: null,
        postId: "1mkjmsl",
    },
    {
        id: "n7jbvnr",
        body: "Yes I’m using chip select and reset. Yes I’m using a chip select and reset pins. Miso blue, mosi green, clk grey\/white, sel orange, rst yellow.",
        parentId: "n7jbdfi",
        postId: "1mkjmsl",
    },
    {
        id: "k201vv5",
        body: "Agreed. [Here's a good estimate](https:\/\/hackaday.io\/project\/11864-tritiled\/log\/72554-determining-maximum-runtime-176-to-202-years-cr2032) of self discharge calculated at 257nA. The difference in 1nA to 50nA in deep sleep is like going from a 0.5% to 20% increase in self discharge for a CR2032, for a circuit that stays primarily in deep sleep. This is increased of course by leakage of capacitors and mosfets in the power path (primarily source-drain leakage) and input path (gate leakage) that sustain voltage on their terminals. And all of this is very much affected by temperature. Most capacitors and MOSFETs are specified by the manufacturer at 1uA leakage (and more for source-drain), so finding low-leakage supporting components is difficult. Niche, yes.",
        parentId: "k1z3qmk",
        postId: "14nzkr3",
    },
    {
        id: "j02lg9v",
        body: "If it’s popular it’s either out of stock currently or soon will be. I have been redesigning old projects for my clients to take DIP versions as our usual suspects disappear.\nDoing a digikey search to see stock levels helps, best option is to buy what you need for the next year when you find stock.",
        parentId: null,
        postId: "zkoe2c",
    },
    {
        id: "j03jhp9",
        body: "It’s a nightmare. We’ve been ordering a year ahead for our current production and will soon have to move to 18 months ahead.\n\nI want a particular PIC18 to make some development and test units of a new product design. I had some on back order but the distributor has just today cancelled my back order because they don’t know if or when they’ll ever get them. \n\nI think the only solution is to decide early on which PIC you want, based on what you can get, then do a lifetime buy while it’s in stock. The risk of course is you later find your code is too big, or you’ve run out of IO or something, and you’re back at square one with a huge mountain of PICs you can’t use.",
        parentId: null,
        postId: "zkoe2c",
    },
    {
        id: "j03vbpr",
        body: "Yeah exactly, are there other microchip controllers that are more popular? Perhaps from the Atmel series. Though I'm used to and I like PIC.",
        parentId: "j03jhp9",
        postId: "zkoe2c",
    },
    {
        id: "j4au1jk",
        body: "Long time ago I standardized all my products to use four PIC variants:\n\n- PIC12F1840-I\/P: 8K flash, easy to replace socketed DIP-8. Perfect for glue logic and super simple devices. This was a clear winner over the years, as 8-pin PICs are often on stock.\n\n- PIC16F1847-I\/P: 14K flash, socketed DIP-8. Good for small devices. Only problem with this choice is that Microchip seem to be slowly phasing out 18-pin PICs in favor of 20-pin ones. I'll probably have to move away from these package in the future, and move to DIP-20.\n\n- PIC18F2xK40-I\/SP: 32-128K flash, socketed DIP-28. This was a pure win, because Microchip has PIC16, PIC18, PIC24 all the way up to PIC32 in DIP28 packages, mostly pin-to-pin compatible. So if one part is out of stock, I simply find another one, or another architecture altogether.\n\n- PIC18F67Kxx-I\/PT: 128K flash, TQFP-64. Another pure win because Microchip has all favors in TQFP-64 which are more or less pin compatible. Downside is that many other people think the same way, so it's rather hard to find a TQFP-64 PIC with more than 32K flash on stock.\n\nAs time goes by, I think that maybe it's time to move away from Microchip products altogether and make that long overdue switch to ARM. Plenty of chip makers, so there'll be plenty of stock. You only need a good Segger probe instead of buying a new Pickit\/ICD every few years to keep up with Microchip shenanigans. STM32F1 is basically the industry standard, and also if Western chip makers fail to deliver, there are always cheap and readily available Chinese clones. (I haven't seen the Chinese ever copying a PIC18. So that should tell me something.)",
        parentId: null,
        postId: "zkoe2c",
    },
    {
        id: "j4f666i",
        body: "A friend suggested using ARM's lower specs range for simple stuff. Still need to get familiarised with them however. Thanks for the quick summary btw. Probably the PIC12F1840 can be substituted with a pin to pin equivalent if hardware specs are suitable for the software you're running.",
        parentId: "j4au1jk",
        postId: "zkoe2c",
    },
    {
        id: "kvdt4ww",
        body: "Maybe something like this?\n\n19.1   Installation &amp; Launch Issues\nMPLAB X IDE hangs at splash screen after launch: This is due to an incompatibility between some proxy servers and certain PCs as well as the java run time. This issue has been found on HP and Lenovo computer systems but may affect others.\nSolution: Disconnect your PC from any internal networks. Once MPLAB X IDE boots up, go to Tools &gt; Options &gt; General and change “Proxy Settings” to “No Proxy”. After this you can reconnect to local networks and MPLAB X IDE will start normally going forward.",
        parentId: null,
        postId: "1bh6kky",
    },
    {
        id: "kvikwfd",
        body: "While useful, my issue is that I can not open the options, so can not get to the proxy settings.  Incidentally, didn't notice this issue in previous versions.",
        parentId: "kvdt4ww",
        postId: "1bh6kky",
    },
    {
        id: "kvh41jr",
        body: "Might not help your issue directly.\nI've used it on windows and it has been a pain to use.\n\nThere is a VScode plugin VSLABX, I now only have MPLABX open for Harmony 3 generation.",
        parentId: null,
        postId: "1bh6kky",
    },
    {
        id: "kvhnjus",
        body: "Do you know if you can use any of the Microchip debug tools like PICkit 4 or ICD 4 with VScode?",
        parentId: "kvh41jr",
        postId: "1bh6kky",
    },
    {
        id: "kvhrngg",
        body: 'I use the cortex-debug plugin for debugging and there is quite some customization options possible, but I\'m not use about those. \n\nFor jlink it still relies on the "normal" jlink tools to actually debug. \n\nThis is my launch.json\n&gt;     {\n&gt;         "version": "0.2.0",\n&gt;         "configurations": [\n&gt;             {\n&gt;                 "name": "Debug with JLink",\n&gt;                 "cwd": "${workspaceFolder}",\n&gt;                 "executable": "F:\/firmware\/project.X\/dist\/SAMD51\/production\/project.X.production.elf",\n&gt;                 "request": "launch",\n&gt;                 "type": "cortex-debug",\n&gt;                 "runToEntryPoint": "main",\n&gt;                 "servertype": "jlink",\n&gt;                 "serverpath": "C:\/Program Files\/SEGGER\/JLink\/JLinkGDBServerCL.exe",\n&gt;                 "device": "ATSAMD51J19A",\n&gt;                 "interface": "swd",\n&gt;                 "svdFile": "F:\\\\ATSAMD51J19A.svd",\n&gt;                 "armToolchainPath": "C:\\\\Program Files (x86)\\\\GNU Arm Embedded Toolchain\\\\10 2021.10\\\\bin\\\\",\n&gt;                 "showDevDebugOutput": "raw",\n&gt;                 "liveWatch": {\n&gt;                     "enabled": true,\n&gt;                     "samplesPerSecond": 5\n&gt;                 },\n&gt;                 "rtos": "FreeRTOS",\n&gt;                 "rttConfig": {\n&gt;                     "enabled": true,\n&gt;                     "decoders": []\n&gt;                 }\n&gt;             },\n&gt;             {\n&gt;                 "name": "C\/C++: gcc.exe build and debug active file",\n&gt;                 "type": "cppdbg",\n&gt;                 "request": "launch",\n&gt;                 "program": "${fileDirname}\\\\${fileBasenameNoExtension}.exe",\n&gt;                 "args": [],\n&gt;                 "stopAtEntry": false,\n&gt;                 "cwd": "${fileDirname}",\n&gt;                 "environment": [],\n&gt;                 "externalConsole": false,\n&gt;                 "MIMode": "gdb",\n&gt;                 "miDebuggerPath": "C:\\\\.....",\n&gt;                 "setupCommands": [\n&gt;                     {\n&gt;                         "description": "Enable pretty-printing for gdb",\n&gt;                         "text": "-enable-pretty-printing",\n&gt;                         "ignoreFailures": true\n&gt;                     },\n&gt;                     {\n&gt;                         "description": "Set Disassembly Flavor to Intel",\n&gt;                         "text": "-gdb-set disassembly-flavor intel",\n&gt;                         "ignoreFailures": true\n&gt;                     }\n&gt;                 ],\n&gt;                 "preLaunchTask": "C\/C++: gcc.exe build active file"\n&gt;             }\n&gt;         ]\n&gt;     }',
        parentId: "kvhnjus",
        postId: "1bh6kky",
    },
    {
        id: "kvilc8u",
        body: "Thanks, I've heard of others using code as well, will look into it. This isn't the first \"issue\" I've had with MPLABX.",
        parentId: "kvh41jr",
        postId: "1bh6kky",
    },
    {
        id: "lfs74mb",
        body: "In case this problem is still present or someone else stumbles upon this, here's a good solutoin from the mircochip-forums: [https:\/\/forum.microchip.com\/s\/topic\/a5C3l000000Md2AEAS\/t380101](https:\/\/forum.microchip.com\/s\/topic\/a5C3l000000Md2AEAS\/t380101)",
        parentId: null,
        postId: "1bh6kky",
    },
    {
        id: "l7u4o4s",
        body: "Okay so... battling this, found a workaround.  Do these two things: 1. Install lib32-gtk-engines (unsure if this helped.)  2. Create a shell script in your \\~\/bin folder named mplabx, chmod +x it, and put the following into it:\n\n    #!\/bin\/env bash\n    # the options menu will not open when mplab is started from usual link... \n    # options opens fine when mplab started from terminal however...\n    \/opt\/microchip\/mplabx\/v6.20\/mplab_platform\/bin\/mplab_ide\n\nNot sure why, but starting mplabx from a terminal (instead of the usual launcher) will allow me to open the options window and project properties.  Things get a little funky if MCC is run, but that is a whole other topic.",
        parentId: null,
        postId: "1bh6kky",
    },
    {
        id: "jiubx0n",
        body: "Do you have a scope tied directly to the pic that indicates the problem is pic related? Motors generate a lot of noise.",
        parentId: null,
        postId: "137qb7e",
    },
    {
        id: "j4aw690",
        body: "Supply shortages have sucked enough. \n\nNice to see something finally being on stock for a change.",
        parentId: null,
        postId: "10bmn1v",
    },
    {
        id: "j4b0p0h",
        body: "Yeah, well it was sad as I thing most of the companies because of fear overstocked materials and inflated this 'no stock' bubble + brokers and suppliers saw this fear and sources mcu's to become monopoly.",
        parentId: "j4aw690",
        postId: "10bmn1v",
    },
    {
        id: "j4b2zzt",
        body: "Yeah, guilty as charged... I filled my Microchip stock in early 2021, just in time before shortages became unbearable.",
        parentId: "j4b0p0h",
        postId: "10bmn1v",
    },
    {
        id: "j4c9kn7",
        body: "[deleted]",
        parentId: "j4b2zzt",
        postId: "10bmn1v",
    },
    {
        id: "j55m9vt",
        body: "Did you had some issues  with quality?",
        parentId: "j4c9kn7",
        postId: "10bmn1v",
    },
    {
        id: "j55m5ko",
        body: "Well we did that too. And recently had to look where to empty our stock, while in my mind I disagree and think that we should still have a bit of overstock",
        parentId: "j4b2zzt",
        postId: "10bmn1v",
    },
    {
        id: "jh53put",
        body: "That warms my heart lol",
        parentId: null,
        postId: "10bmn1v",
    },
    {
        id: "mw8npan",
        body: "For this level of detail I would recommend contacting your local Microchip FAE.",
        parentId: null,
        postId: "1l42wvt",
    },
    {
        id: "mwbcvyc",
        body: "Yeah, I contacted them at the same time I wrote this. Figured I'd cover as many bases as possible. You don't happen to have any experience with CAN in general do you?",
        parentId: "mw8npan",
        postId: "1l42wvt",
    },
    {
        id: "mwenz6m",
        body: "Not enough to help.  You might also try the Microchip Community Forums.\n\nhttps:\/\/forum.microchip.com\/",
        parentId: "mwbcvyc",
        postId: "1l42wvt",
    },
    {
        id: "mwsp50l",
        body: "If anyone is curious to know the answer. It's in section 49.4.2. of the ATSAMV71Q21B reference manual (48.4.2 of SAM-E70-S70-V70-V71 Family reference manual). It states:\n\n\"In order to achieve stable function of the MCAN, the system bus clock must always be faster than or equal to the CAN clock\"\n\nMeaning MCK (which controls the system bus clock - otherwise known as the peripheral clock) must be greater than the 80MHz clock (PCK5) I have set for the MCAN's bit timing. This does mean the 60MHz I had it set to in my question is still in violation of this so it has been increased using UPLL\/4 prescaler (120MHz).",
        parentId: null,
        postId: "1l42wvt",
    },
    {
        id: "n5vwsqt",
        body: "Looks like they recommend Ubuntu 18.04 in the release notes. Maybe enough differences with your distro to cause the problems? I don’t know much about Linux so can’t really say.",
        parentId: null,
        postId: "1mcexha",
    },
    {
        id: "n5tt96e",
        body: "As I know 6.25 is latest. Do you have any need not to go on latest version? May be fixed there.",
        parentId: null,
        postId: "1mcexha",
    },
    {
        id: "n5uczg7",
        body: "Sadly I have a reason. Versions above 6.20 don't support most of ICDs and Pickits we have in R&amp;D and production, so the whole team stays on 6.20 for now. But your right , I will try latest. I can just use IPE 6.20 for most of the old hardware.\n\nThing is I used 6.20 on other arch based distros the past year without issues.",
        parentId: "n5tt96e",
        postId: "1mcexha",
    },
    {
        id: "n5vlt8s",
        body: "I think pretty much everybody avoids changing to a newer version or running any update! If you change nothing will ever work again.",
        parentId: "n5uczg7",
        postId: "1mcexha",
    },
    {
        id: "n659rjo",
        body: "I don't even know if this is supposed to be sarcasm. :D",
        parentId: "n5vlt8s",
        postId: "1mcexha",
    },
    {
        id: "n65elga",
        body: "I’m serious. I always reject the update messages. I keep the same version of MPLAB throughout the development of a new product. When I’ve completed development I put a copy of the MPLAB version in the same folder I archive all the project files in. \n\nIf I develop a new product using an existing product as the starting point (my usual approach) then again I’ll continue with the same MPLAB version.\n\nI always need to be dragged kicking and screaming to a new MPLAB version, and then I’ll plan for a whole weekend to get things working again. \n\nI hate it.",
        parentId: "n659rjo",
        postId: "1mcexha",
    },
    {
        id: "n659tr6",
        body: "Its the same with latest sadly",
        parentId: "n5tt96e",
        postId: "1mcexha",
    },
    {
        id: "n24xim2",
        body: "If you're looking for a Microchip Bluetooth module with a development board, consider the **RN4870\/RN4871** for BLE 5.0 (with UART interface and AT commands; dev board: RN4870 PICtail – DM164146), or the **BM70\/BM71** for BLE 4.2 applications needing HID or transparent UART (same PICtail board). For classic Bluetooth with Serial Port Profile (SPP), go for the **RN42 or RN41** with the **RN-42-EK** dev kit. If you're also exploring Zigbee or Thread alongside BLE, the **WBZ451 Curiosity Board (DM182037)** is a great all-in-one option. All these modules are easy to use and well-supported in Microchip's ecosystem.",
        parentId: null,
        postId: "1lvcqd6",
    },
    {
        id: "mzmi1ez",
        body: "This Subreddit is for Microchip Technology brand products. But that looks like a custom part number.",
        parentId: null,
        postId: "1ljt8su",
    },
    {
        id: "mzmvk94",
        body: "I see where do you thing i could find advice?",
        parentId: "mzmi1ez",
        postId: "1ljt8su",
    },
    {
        id: "mznb433",
        body: "Try and find a schematic for the motherboard is all I can think of.",
        parentId: "mzmvk94",
        postId: "1ljt8su",
    },
    {
        id: "mznf6o8",
        body: "Thainks i alredy found the component",
        parentId: "mznb433",
        postId: "1ljt8su",
    },
    {
        id: "n0patao",
        body: "[removed]",
        parentId: "mznf6o8",
        postId: "1ljt8su",
    },
    {
        id: "n0pb5eu",
        body: "Its a ethernet chip for a b550m aorus elite v1",
        parentId: "n0patao",
        postId: "1ljt8su",
    },
    {
        id: "mttl872",
        body: "Foundries supply many companies.     \n\nTSMC (Taiwan Semiconductor Manufacturing Process) Started in 1987. I worked for a US company that realized it didn't have the money to go to the latest fab process, and the company was an early investor in TSMC.   \n\nHad we built our fab, we would have only needed 20% of the capacity. TSMC realized they could supply five companies and run closer to 100% capacity. Those five companies would then focus on everything else but let someone else build their chips.\n\nYou can not drop a design from FAB XYZ into TSMC, but you can work with their engineers to ensure that it works in their fabs.\n\nAt Microchip Masters, there is an excursion to tour the fab. I highly recommend taking that tour. The best part is that everyone is a geek, so we had fun with side discussions.   My boss didn't want to send me, but I joked, \"Who in their right mind goes to Phoenix in August?\". Travel approved.",
        parentId: null,
        postId: "1ktb2wb",
    },
    {
        id: "mttm37x",
        body: "This is a cool graph of TSMC processes. \n\n[https:\/\/www.tsmc.com\/english\/dedicatedFoundry\/technology\/logic\/l\\_3nm](https:\/\/www.tsmc.com\/english\/dedicatedFoundry\/technology\/logic\/l_3nm)",
        parentId: "mttl872",
        postId: "1ktb2wb",
    },
    {
        id: "mtt1cup",
        body: "An actual Microchip company post to r\/microchip!",
        parentId: null,
        postId: "1ktb2wb",
    },
    {
        id: "n7zay1u",
        body: "ST provides HAL.  This eliminates (in their eyes) the need for those types of instructions.",
        parentId: null,
        postId: "1mmppug",
    },
    {
        id: "n7zei8e",
        body: "I think that in the reference manual is where things are more explained. But it's true that it's not always so clear and hard to start from there.\n\nThe best approach, at least at the beginning, is to use the CubeMX app to define the peripherals that you need. Just find one peripheral, enable it, and the parameters available will guide you, more or less. Check also the clock tree for possible conflicts or adjustments.\n\nThen take a look to the generated code. It will configure and initialize each peripheral, and it adds the needed headers.  You can get an idea on how the peripheral works and how to continue, better in your own folders and files, out of the auto-generated messy files.\n\nNote: you should use the cubemx, but (luckily) you don't need to use the cubeide. You can generate the code for cmake and vscode.",
        parentId: null,
        postId: "1mmppug",
    },
    {
        id: "n82rx8z",
        body: "The reference manual will have this usually after the functional description of the peripheral.",
        parentId: null,
        postId: "1mmppug",
    },
    {
        id: "jiuc3zg",
        body: "Yeah if I desolder the Hbridge and scope the PWM it's still just high 100% of the time",
        parentId: "jiubx0n",
        postId: "137qb7e",
    },
    {
        id: "jiucwjg",
        body: "To clarify, it starts working then randomly stays high with absolutely no load on the PWM pin?\nIf you are doing development with a pickit is there a way to put a break point in when a certain condition occurs? Have the registers loaded in the Watch space.",
        parentId: null,
        postId: "137qb7e",
    },
    {
        id: "jiufkix",
        body: "It happens randomly when the board is powered on, the moment a write to PDC1(the dudy cycle register) with a value anything other than 0. The register is being written every ms with the results from the PID.I suppose  I could try slowing down the PID timer? I'll try running some break points and watches. The whole thing is just very strange because like I said it isnt all my pics, the first 2 boards I built up fidnt do it. Then after pulling my hair out on the 3 board I just replaced the pic and it was fine. I've had one board do it since then in like the 10 I've built.",
        parentId: "jiucwjg",
        postId: "137qb7e",
    },
    {
        id: "jiuhj9t",
        body: "Do you write to it I  the interrupt? If so, try setting a flag and do it outside the interrupt.",
        parentId: "jiufkix",
        postId: "137qb7e",
    },
    {
        id: "jizo4aq",
        body: "Ok I tried everything here, still keeps happening.. I think I might just bit bang the PWM. I dont really have time to be stuck on this...",
        parentId: "jiuhj9t",
        postId: "137qb7e",
    },
    {
        id: "jizoi3b",
        body: "Maybe post this on the Microchip forum.  Apparently there are a lot of their engineers that monitor it.",
        parentId: "jizo4aq",
        postId: "137qb7e",
    },
    {
        id: "jizsaz9",
        body: "Ok I'll do that thanks.",
        parentId: "jizoi3b",
        postId: "137qb7e",
    },
    {
        id: "jiugi2u",
        body: "Try setting it to a single 50% duty cycle and never changing it again.  Never even update the register. See how long it lasts.\n\nTry several things like stopping the module before changing the value. Then start it up again. \n\nDisable interrupts while changing the value.",
        parentId: null,
        postId: "137qb7e",
    },
    {
        id: "jiui1dc",
        body: "If you have priority interrupts and this is in a low priority interrupt, I again suggest disabling interrupts while changing the value.",
        parentId: null,
        postId: "137qb7e",
    },
    {
        id: "jiuiqnx",
        body: "I will try that thank you. I do have the PID timer running on the highest priority though.",
        parentId: "jiui1dc",
        postId: "137qb7e",
    },
    {
        id: "jjble7f",
        body: "If you have found the solution,  or reason for your problem, please post as this may help someone else. Thanks.",
        parentId: null,
        postId: "137qb7e",
    },
    {
        id: "jjbog5d",
        body: "I will. I may have to switch chips also due to availability.  The chip shortage is really taking the fun out of my job. When I layed out the board digikey had around 1k of them in stock. Enough to run what we need for boards. After I was happy with the prototype it went to 0.",
        parentId: "jjble7f",
        postId: "137qb7e",
    },
    {
        id: "ity8f7i",
        body: "You have to set the ANSEL REGISTER bits to 0 because the default at power up is 1 which is analog mode.\nAnd also turn off comparators in CM1CON0 and CM2CON0 - COMPARATOR CONTROL REGISTERS.",
        parentId: null,
        postId: "yeerfs",
    },
    {
        id: "itzm9ks",
        body: "&gt;PORTAbits.RA2=0;\n\nI got it working. Also I'm a moron. I was setting the PORTAbits.RA2 to 0 in both places of the blink program.\n\nBy the way, the docs don't seem to be mentioning UART or USART anywhere, does this mean this particular IC is not equipped with serial communications on board?",
        parentId: "ity8f7i",
        postId: "yeerfs",
    },
    {
        id: "iu1ripa",
        body: "Glad you got it working! You are correct, no serial comms on this part. But they have plenty of other parts that do along with most of the analog stuff on this PIC16F570.",
        parentId: "itzm9ks",
        postId: "yeerfs",
    },
    {
        id: "itzj0gc",
        body: "&gt;ANSEL REGISTER\n\nAlrighty. I believe I've set this up correctly. RA0-5 pins aren't currently responding yet but let's see if I have done this right. So since I don't need any analogue pins, I've set them all to 0 which is the same as 0b00000000\n\n        CM1CON0 = 0;\r\n    CM2CON0 = 0;\r\n    ANSEL = 0b00000000;\n\n Now the docs page 62 mention that the CM1CON0 and CM2CON0 comparator enable is bit 3. I assume that if I set that to 0, other bits don't matter as the whole comparator is disabled?\n\nThere's probably something else here in the way of getting RA0 to RA5 pins working as digital.",
        parentId: "ity8f7i",
        postId: "yeerfs",
    },
    {
        id: "n7cclsh",
        body: "Could use two Quad-SPI peripherals.  Plenty of MPUs have that.  15MB\/s is quite manageable with an 8-bit wide (equivalent) SPI bus.\n\nOther option is to run something like a Zynq-7000 or Ultrascale as your MPU.  Run Linux on the (PS) ARM core and create as many high speed buses (SPI or otherwise) as you want in PL fabric.  This is probably the route I would take.  Even the -7000 can easily take in 250Kb\/s from 64 Independent SPI controllers - though you may have to cut that in half just because you'll run out of physical pins.  Two slave devices per bus is still very reasonable though.\n\nWith that much available processor and PL horsepower though - it's worth considering if you would even need the RP2350s or if the Zynq could collect the data directly..",
        parentId: null,
        postId: "1mjmoo8",
    },
    {
        id: "n7cel5k",
        body: "Quad-SPI seems like a very reasonable choice! Thanks, I'll look into it further to see if it can work for me.\n\nI am trying to avoid the FPGA route but I do have to admit that it seems like the best option at this point. I'm a little surprised that there aren't more \"good\" options for this use case. It seems like this is a natural problem when building distributed systems...",
        parentId: "n7cclsh",
        postId: "1mjmoo8",
    },
    {
        id: "n7cfaz9",
        body: "Don't be afraid of the Zynq.  It's a very usable part, and there are some great resources to help learn (search MicroZed Chronicles on Google).  I use them **all the time** for very similar use cases to yours.  They are friggin wonderful.\n\nBeing able to \"create\" a processor with 32 UARTs or 24 I2C buses or 12 MicroBlaze processors is just.. magical..  the design I just finished a few months ago, in fact, had 64 RS-485 buses on it - like I said, very similar use cases to yours.\n\nIn fact, instead of SPI, I might actually suggest 64 simple UARTs to talk to your RP2350s.  That's only 128 pins, which is easily within the IO capability of the -7000 and 2 Mega Baud is no big deal if they're physically close by.\n\nThey're simply amazing for layout too.. connect everything up wherever it physically fits best, and then assign the pins in PL.  Zynq designs are always super clean.",
        parentId: "n7cel5k",
        postId: "1mjmoo8",
    },
    {
        id: "n7cpvkc",
        body: "The Zynq looks like a really nice chip. I have little FPGA development experience making it a bit daunting but it does seem like the right way to go.\n\nCan you share any more details on your work?",
        parentId: "n7cfaz9",
        postId: "1mjmoo8",
    },
    {
        id: "n7cq5mu",
        body: "DM me and I'll share some details that I don't want in the general public domain.  These designs all \"technically\" belong to my company, not me personally, so I can't have them all over the Internet.\n\nFor what you need, you won't have to write a single line of VHDL\/Verilog.  You can do the entire thing using Xilinx's GUI drag-drop tool which will make it very simple.",
        parentId: "n7cpvkc",
        postId: "1mjmoo8",
    },
    {
        id: "n7dktwt",
        body: "It looks like there's USB 1.1 on the RP2350. \n\nUSB to each and a bunch of hub chips?",
        parentId: null,
        postId: "1mjmoo8",
    },
    {
        id: "n7j2t7f",
        body: 'USB is viable with a bunch of hub chips. I was hoping to avoid a "complex" standard like USB or ethernet since it seems overkill for a point to point application where I control both sides.',
        parentId: "n7dktwt",
        postId: "1mjmoo8",
    },
    {
        id: "n7jl0gc",
        body: "USB on RPi side and a bunch of Prolific usb uart ICs so the remote units only deal with uart. Neither side needs any usb specific code since Linux handles USB uarts transparently.",
        parentId: "n7j2t7f",
        postId: "1mjmoo8",
    },
    {
        id: "n85dog0",
        body: "Find an example project, copy paste it's initialization code, then guess-change-check settings until it's right. Ive found a fair amount of success with having copilot explain what each setting does and what functions need called. Just actually verify everything it says cause it hallucinates TF out of STM registers lol.\n\nSTM's HAL documentation is mostly useless. It's just a copy-paste of the code comments which also do very little to explain what any particular bit of code does. They assume you're gonna use cubeide or already know exactly what you want.",
        parentId: null,
        postId: "1mmppug",
    },
    {
        id: "n87g7iw",
        body: "Wiki has what you need here\n\n[https:\/\/wiki.st.com\/stm32mcu\/wiki\/Getting\\_started\\_with\\_UART](https:\/\/wiki.st.com\/stm32mcu\/wiki\/Getting_started_with_UART)",
        parentId: null,
        postId: "1mmppug",
    },
    {
        id: "n88t5ip",
        body: "Use all. For most peripherals it doesn't take long to search for the right sections. I use a pdf reader which opens and can search across multiple files. Fox something.\nAdobe reader is craaaap for this.",
        parentId: null,
        postId: "1mmppug",
    },
    {
        id: "n7zsjeq",
        body: "Careful looking at those HAL peripheral drivers. They are a rats nest of multiple options requiring all kinds of weird setups to account for all possible configurations of that peripheral. Good luck getting that code validated for a regulated implementation. The ST UART HAL is about 90% useless noise for something as simple as a terminal interface.\n\nI also find that they terribly obscure how the peripheral interface is structured in poor attempt at a general purpose, do-all abstraction. \n\nThe User Manuals for the processor spell out what needs to done… most of the time. They can be a bit difficult to navigate. And they don’t do a particular good job of describing the how and why of the peripheral bus  configurations.  I would urge close attention to the discussions on bus configuration.\n\nIf that doesn’t provide enough guidance, you may have to search for the specific MCU and peripheral combination to see existing approaches.\n\nIf all else fails, hit up ChatGPT for low level or bare metal driver. Yes. They can be garbage. Review with a HEALTHY dose of skepticism. My experience has been that the AI gen’d code has about a 2 in 5, maaaayyybeeee  3 in 5 chance of being close enough to learn from. \n\nI find CubeMX gen’d code to be a crutch to avoid.\n“main.h”?  Seriously?",
        parentId: null,
        postId: "1mmppug",
    },
    {
        id: "n7uef3y",
        body: "I'm rarely optimizing existing drivers. Too much trouble to sort through someone else's code in a different style and make it what I want. I'd rather rewrite it myself, possibly using the existing code as a reference. For the USB driver modifications I mentioned, it's stuff like adding support for certain MSD encapsulated SCSI commands that aren't exposed by the normal driver.",
        parentId: "n7tfop5",
        postId: "1mlwd75",
    },
    {
        id: "n7tmqck",
        body: "What's the third way?",
        parentId: "n7tfop5",
        postId: "1mlwd75",
    },
    {
        id: "n7td329",
        body: "Most of the “Arduino” open source drivers use only basic use case, typically polling mode. One may wqjt to integrate DMA or use any other feature\n\nThis is just an example",
        parentId: null,
        postId: "1mlwd75",
    },
    {
        id: "n7tdl24",
        body: "im not talking about arduio, i mean finding driver pre-written on github or mcc for pic (from mplab)",
        parentId: "n7td329",
        postId: "1mlwd75",
    },
    {
        id: "n7tie1z",
        body: "The MCC\/Melody drivers are good for the basic cases and for limited applications (like UART queuing with interrupts). Improvements are made every year but not all use-cases are covered yet.\n\nThe biggest reason for rejecting these is the learning curve. A lot of people prefer \"to do it a different way\" or don't want to adapt their existing code to the new API even if the shim is just a few #define statements. An I2C interface can be tricky on a good day, and the devil you know is preferred to the devil you don't know.",
        parentId: "n7tdl24",
        postId: "1mlwd75",
    },
    {
        id: "n7v5yua",
        body: "Also sometimes the driver wants to be all things to everyone and you just need a tiny bit of it's functionality that can be done in 1\/10th of the footprint.",
        parentId: "n7tie1z",
        postId: "1mlwd75",
    },
    {
        id: "n7tmje7",
        body: "What's tricky about an I2C interface? Is there some gotcha I'm not seeing?",
        parentId: "n7tie1z",
        postId: "1mlwd75",
    },
    {
        id: "n7trj1f",
        body: "I2C on the IC side isn't always implemented according to the standard, especially on designs prior 2005 ( I2C was licensed back then). Also, if someones pulling clk or data low due to a glitch, you need a way to recover from fault which isn't implemented in standard hals",
        parentId: "n7tmje7",
        postId: "1mlwd75",
    },
    {
        id: "n7wt48l",
        body: "Yeah I lost quite some time once to fix I2C driver locking up on a SiLabs part.  Intermittent problem of course and only happened once every few days :(",
        parentId: "n7trj1f",
        postId: "1mlwd75",
    },
    {
        id: "n7u5szj",
        body: "There's just a lot of ways it could be implemented, so a lot of physical details can end up leaking into your sensor specific drivers. For example, if you're reading a register, do you send the address and then a stop\/start, or do you use a repeated start? A generic i2c driver should be able to support both.\n\nAnother challenge I've specifically run into is trying to make the same interface work for a i2c peripheral which pushes everything onto the bus the moment you set it (STM32L100), and one where the peripheral has you queue up all the transaction details (e.g. length, if to ack) first, so it can fire it all off at once (STM32L400). I never got something I was particularly happy with.",
        parentId: "n7tmje7",
        postId: "1mlwd75",
    },
    {
        id: "n7ukfq5",
        body: "That first one isn't a gotcha, it's the protocol.\n\nThe second one, yeah, I see what you're saying, now. MCU implementation of the I2C controller, vs I2C as an interface.",
        parentId: "n7u5szj",
        postId: "1mlwd75",
    },
    {
        id: "n7tmhnp",
        body: 'Because I rarely want to do the same thing the same way twice. At this point, spinning up a spi or i2c driver is second nature, is a good warmup, and lets me experiment with new language features, design patterns, and so on. \n\nI\'ve been using more c++ in embedded stuff lately, for example, and template-based register access is kinda fun. So is strongly typing everything and avoiding void pointers everywhere. \n\n"Publicly available reviewed code is better" is very subjective for many values of "better."',
        parentId: null,
        postId: "1mlwd75",
    },
    {
        id: "n7u8xa5",
        body: "Because the existing one does too little or too much. Little red riding hood's problem.",
        parentId: null,
        postId: "1mlwd75",
    },
    {
        id: "n7uf6uu",
        body: "Generally I try to make use of the vendor code when possible. One project the vendor HAL turned out to do far too much and wouldn't meet the external ADC timing. Going down to the register level and making sure that the critical path was as minimal as possible I got everything to easily meet the timings needed. \n\nSo my take is use the HAL when possible until you find a case, timing, code space, ram usage where you need to optimize further.",
        parentId: null,
        postId: "1mlwd75",
    },
    {
        id: "n7tgnht",
        body: "For practice, fun or never ideally.\n\nEveryone thinks they got the best solution and that they can do better but they'll just re-do others work and add lots of bugs.\n\nThe best code is the one that is already written and tested. The more people that looked at it the better.",
        parentId: null,
        postId: "1mlwd75",
    },
    {
        id: "n7ttp58",
        body: "Just that several manufacturers offers terrible code. Awful.\n\nHello,  FreeScale - let's have the UART use malloc() for the printouts and sometimes lock up the code for a long time...",
        parentId: "n7tgnht",
        postId: "1mlwd75",
    },
    {
        id: "n7u5ezb",
        body: "That one was nasty. Was this for a bare-metal or a linux machine?\n\nEDIT: vendors sometimes don't feel enough incentive to do it properly sadly...",
        parentId: "n7ttp58",
        postId: "1mlwd75",
    },
    {
        id: "n7temq4",
        body: "Depends on your needs and the quality of what's available. You'll virtually never write a driver for something like a USB peripheral - though I do find myself having to dig in to particular class drivers and modify them for my needs.\n\nFor something like SPI I'll very frequently write my own. The main problem is typically that vendor-provided drivers can be very heavy. I've done many projects where I needed fast and efficient access to SPI NOR flash, for example, and the vendor's driver has so much setup per transaction that the back-and-forth required to check the flash status register and send the command, address, and data can take longer than the whole block transfer.\n\nI've written some fairly elaborate UART drivers, for example to interface with a WiFi module, where I needed to minimize latency and the DMA-enabled driver had to be tailored specifically to the protocol used by the WiFi module. A general-purpose driver was far less efficient and slower.\n\nIn any case, I always have my own abstraction layer so my application never touches any hardware driver code directly. For example, I've got a spi\\_block\\_write() function that works the same across HCS08, ColdFire, Kinetis, and LPC parts. Some implementations call a vendor-provided driver and some use my own, but when I'm porting a project from one device to another I don't have to touch any application code if I've done it right.\n\nYou'll also find that vendor drivers are frequently missing important features. Like maybe the DMA controller supports a half-complete interrupt for double buffering but the driver doesn't. Or a UART driver will be missing support for idle line detection. The NXP I2S driver I'm using now allows double buffered DMA transfers but doesn't provide any feedback on which buffer was just sent so desynchronization can be a big problem.\n\nFor the sake of productivity you should use proven, supported drivers when they're available and meet your requirements, but again I always do that with an extra layer of abstraction, even if it's just a macro to wrap the HAL\/SDK function names.",
        parentId: null,
        postId: "1mlwd75",
    },
    {
        id: "n7tfop5",
        body: "You often find yourself optimizing proven drivers or writing them from the datasheet fully from scratch",
        parentId: "n7temq4",
        postId: "1mlwd75",
    },
    {
        id: "n7os965",
        body: 'Bulk capacitors - throw them anywhere.  Decoupling and loading capacitors, honestly - "As close as practical".\n\nOn a BGA, directly behind the pin.  On a QFP\/QFN\/SO, right next to the pin.  The goal here is to reduce the impedance and inductance as much as possible.',
        parentId: null,
        postId: "1ml9v7s",
    },
    {
        id: "n7ov16o",
        body: "&gt; Bulk capacitors - throw them anywhere.\n\nNo, not really. You don’t want to make oscillating circuit by accident. ",
        parentId: "n7os965",
        postId: "1ml9v7s",
    },
    {
        id: "n7oxfyo",
        body: "Why would that happen? Does capacitance create oscillation?",
        parentId: "n7ov16o",
        postId: "1ml9v7s",
    },
    {
        id: "n7p21ux",
        body: "Traces have some resistance and inductance. They create RLC circuits.\n\nIf values align just right you may have a situation where after pulsed power consumption one capacitor is discharged more than the other, when they recharge one finished sooner, but the current still flows in traces and inductance causes voltage to rise (this is how boost converters work). Now second capacitor has higher voltage and discharges towards the first. It can repeat a few times. \n\nFor digital logic it’s usually way beyond the level that would cause issues, but if you have audio amplifier or DAC it can cause problems. ",
        parentId: "n7oxfyo",
        postId: "1ml9v7s",
    },
    {
        id: "n7ouh3f",
        body: "Hmmm... Sounds much more simple than I thought. And it also makes sense.\nBut how can I recognize whether a capacitor is a bulk or decoupling one? On the MCU it's clear enough and it's based on the datasheet.\nWhat about the resonator? Or the regulator where I have placed one 1uF near Vin and one 1uF near Vout? Are they considered to be decoupling?",
        parentId: "n7os965",
        postId: "1ml9v7s",
    },
    {
        id: "n7p5gvh",
        body: "For the resonator, absolutely place them close.  They're so small (picofarads) that adding trace length effectively changes the value. \n\nFor other cases.. the answer is.. it depends.\n\nIf a capacitor is necessary for the stability of a part, consider it similar to a decoupling cap and keep it close.  For a lot of voltage regulators, the capacitor is necessary to ensure stability of the closed loop.",
        parentId: "n7ouh3f",
        postId: "1ml9v7s",
    },
    {
        id: "n7owq1o",
        body: "&gt; I know that decoupling capacitors must be placed near the supply pins. But what are the factors that determine this distance?\n\nAs close as design permits. And take care of how your route traces through them - eg you shouldn’t route the power through the pin from under the chip and into capacitor. \n\nRemember that each trace is not just a connection. It is some resistance, some inductance and parts and pads have also some capacitance. You’re creating rlc circuit with each connection \n\nSee the AN https:\/\/www.ti.com\/content\/dam\/videos\/external-videos\/de-de\/9\/3816841626001\/6313253251112.mp4\/subassets\/notes-decoupling_capacitors.pdf\n\n&gt; And what about the bulk capacitors?\n\nBest to place them where the voltage “enters” the particular domain (connector, regulator) and if you have several keep them close. \n\nMost of the times it doesn’t matter much but currents and return currents create em field gradients over your board, you can sometimes make a resonant circuit between poorly placed capacitors. \n\n&gt; Do they need to be close to the crystal module?\n\nYes. ",
        parentId: null,
        postId: "1ml9v7s",
    },
    {
        id: "n7seujc",
        body: "For PCB:\n\n* Place lowest capacitance value as **close as reasonably possible** to the IC power pin \/ input pin of voltage regulator \/ crystal.  If there are two or more decoupling capacitors, such as 100nF + 10uF, place next higher capacitance value on the other side of the lowest capacitance value.\n\n* For higher capacitance values, place one near incoming power connector, place others near current-spiking loads.\n\nFor Schematic:\n\n* I typically layout capacitors on my schematic similar to the PCB, I place the lowest capacitance values closest to the IC symbol, to make both the same.",
        parentId: null,
        postId: "1ml9v7s",
    },
    {
        id: "n7x2q5e",
        body: "The lowest capacitance values are considered to be the decoupling capacitors, right? For the MCU I use (stm32f401ret) the datasheet suggests that each VDD\/VSS pair (there are 4 of them) should have a (decoupling I guess) 100nF next to it and then all together a 4.7uF (which I think its the bulk one). Similar for the VSSA\/VDDA. So my schematics look like this.\n\nhttps:\/\/preview.redd.it\/tu8h55hc36if1.png?width=1324&amp;format=png&amp;auto=webp&amp;s=382e3d091efaaf89c8c605cebcf00dc5b2e0e24d\n\nI am trying to understand why it is correct. Why does it mean that every pair is individually connected with a 100nF and all together with a 4.7uF?\n\nWhen it comes to PCB editor I place the low values the way you suggest me, very close to the mcu. But the 4.7uF one can not be connected to all the pairs at the same time...",
        parentId: "n7seujc",
        postId: "1ml9v7s",
    },
    {
        id: "n8cwa65",
        body: "My original comment meant the PCB, so I clarified my comment.",
        parentId: "n7x2q5e",
        postId: "1ml9v7s",
    },
    {
        id: "n7sz8uh",
        body: "The closer the better.",
        parentId: null,
        postId: "1ml9v7s",
    },
    {
        id: "n7pndj5",
        body: "Just follow what Phil's Lab does on YouTube",
        parentId: null,
        postId: "1ml9v7s",
    },
    {
        id: "n7x1pz6",
        body: "This is what I am doing hehe, it is a great point to start but again it makes me wonder of how can I be sure. maybe I am overthinking",
        parentId: "n7pndj5",
        postId: "1ml9v7s",
    },
    {
        id: "n7yjo0f",
        body: "You are overthinking. I thought the same way when I was starting out. Over time you'll realize some things aren't going to be exact and that's okay. However it doesn't mean that you can just guess the important things. Here, just following the application note for capacitors is fine. However, let's say you are trying to impedance match a transmission line using capacitors and inductors. Then you need to make an initial calculation.",
        parentId: "n7x1pz6",
        postId: "1ml9v7s",
    },
    {
        id: "n7r8nba",
        body: "Look at the PCB manufacturers website to see how close they can place components. You can place these values in your program. The program should flag you if you place the component too close. That is how I plan to place the components as close as possible lol",
        parentId: null,
        postId: "1ml9v7s",
    },
    {
        id: "n7ub4fl",
        body: "Bare metal microcontroller - LPC54xxx. The very target where most companies do not allow any dynamic memory allocation at all. Or at most only for initial allocation before entering the main loop.\n\nBut the rest of their driver code for that processor family is just as bad. The most junior developers (aka cheapest) must have been used.\n\nWhen they implemented the UART, they forgot that the FIFO needs a timeout to force an interrupt to read out any stuck characters if it hasn't reached the watermark level. When first getting bug reports, they implemented a workaround by having the main loop read out any stuck data. Then users after a while complained that the receive buffer sometimes had received characters switching order - not strange since if the UART gets more incoming data then it can get the watermark interrupt while the main loop still has read out a character but not moved to the receive buffer. So then they needed to change their workaround to reprogram the watermark to one (1) character by a timer.\n\nAnd their code turns off interrupts just about everywhere - when needed or not needed. Just as if they don't understand how and when to protect structures from simultaneous accesses and goes for a random hunt for workarounds.\n\nCommunicating CAN? The CAN receive FIFO code turns on receive interrupt when you can a FIFO-receive function. After first frame is received? Then the interrupt handler instantly turns off the receive interrupt and expects the main loop to turn it on again for any more frames. Quite odd design choice.\n\nBut it isn't just their microcontroller developers that are unlucky. The guys writing the MCUXpresso support are just as unlucky. They have wizards if you want to avoid coding yourself for setting up processor pins. The wizard? Uses O(N\\^2) so it starts quickly for the first pins. Select a chip with 160 pins and at maybe 80-100 pins it takes 1-2 seconds for the interface to react when you click on a pin. I haven't tried the response time for getting past 150 pins configured... It's their logic for trying to see that not multiple pins are having colliding configuration that uses exploding logic that makes it feel like they are solving a traveling salesman problem.",
        parentId: "n7u5ezb",
        postId: "1mlwd75",
    },
    {
        id: "n7tyzym",
        body: "&gt;Everyone thinks they got the best solution and that they can do better but they'll just re-do others work and add lots of bugs.\n\nI disagree with this right on the face of it. There are many, many good reasons to write things that have already been done.\n\nPlenty of us can implement quality drivers on a whim.\n\n&gt;The best code is the one that is already written and tested.\n\nI get what you're trying to say here, and it's a good loose rule of thumb, but there's no \"best\" implementation of anything and it is in no way an absolutely true statement.\n\nA bug-free driver that doesn't fit my design goals isn't best for me no matter how good the implementation is. A perfect driver distributed under a license I don't like or can't use isn't best for me. A driver with a coding style that throws me off isn't best for me.\n\nHell, a driver written by someone(s) I have a personal beef or prior bad experience with isn't \"best\" for me if it makes me grumble and puts me in a bad headspace whenever I use it.",
        parentId: "n7tgnht",
        postId: "1mlwd75",
    },
    {
        id: "n7u4zl8",
        body: "You forgot to quote the key part:  \"ideally\"\n\nIt's a cost to rewrite and therefore a bad thing, sometimes necessary.\n\nI'm not sure why you're arguing that the wrong driver is still wrong even if bug free? If there is no correct driver or if it has the wrong licence than it has not really already been done in my opinion, which means we're in agreement... We could argue about the semantics but I don't think that is worthwhile.\n\nI'm not really saying there is a best code for everything. It says that code which is tested and already written, which means a lot of eyes looked at it and a lot of hours went in to it is going to be the best when compared to code which needs to be written, isnt tested, only one pair of eyes on it and never enough hours.\n\nRewritting an entire driver because the code style bugs you or you dislike the author is just childish and you must have very generous budget...",
        parentId: "n7tyzym",
        postId: "1mlwd75",
    },
    {
        id: "n7uf5q3",
        body: 'I may have turned it into a semantics argument, which wasn\'t really my intent, but semantics matter. I still don\'t think the "ideally" caveat is accurate, but I have problems with words like "ideal" and "best" in subjective discussions.\n\nThere\'s also a wide gulf between holding these opinions when someone is paying me versus when I\'m burning my own candle. Which is yet another factor in "best," and probably got lumped into your "fun or practice" categories.\n\nI think we\'d fundamentally agree if we more carefully defined our terms, that\'s my fault.',
        parentId: "n7u4zl8",
        postId: "1mlwd75",
    },
    {
        id: "n7wg5m2",
        body: "True! \nI just saw your user name btw, amazing!\n\nIt's hard writing up a proper reply because either  you keep it short and leave to much to interpretation or you keep on going and going... or both. At least that's how I end up doing it many times.",
        parentId: "n7uf5q3",
        postId: "1mlwd75",
    },
    {
        id: "n7v8xy9",
        body: "I agree with you on practice and fun - but ideally everyone should go through the process at least once. Not for production code, not for anything other than self-knowledge really, but there is something to be gained (especially within the embedded space) by looking below the water.",
        parentId: "n7tgnht",
        postId: "1mlwd75",
    },
    {
        id: "n7whhpe",
        body: "Absolutely!\n\nI've found plenty of code I thought was complete garbage only to eventually end up in almost the same spot because there were quite a few things I didn't see or thought of initially that made me make similar decisions and it can be really hard to tell without doing it yourself. \n\nWhich was what I really wanted to convey. Maybe there is a reason for that ugly solution? Are you sure you really considered everything? And so on... before you start an unnecessary rewrite.\n\nThere is so many rabbit holes to go down indeed!",
        parentId: "n7v8xy9",
        postId: "1mlwd75",
    },
    {
        id: "n7yj56c",
        body: "Completely agree. It's not something you should do for production. There is a Linus rant, or there was since I can't seem to find it, that basically outlines the case for en-mass system rewrite being akin to self sabotage. But like you say, until you've taken the time to do the exercise you might not understand the reasoning behind the existing paradigm. Today's problem was yesterday's solution.",
        parentId: "n7whhpe",
        postId: "1mlwd75",
    },
    {
        id: "n8144ht",
        body: "Exactly…they rewrite theirs and spend more time debugging their spaghetti.",
        parentId: "n7tgnht",
        postId: "1mlwd75",
    },
    {
        id: "n7unieo",
        body: "In the case of writing code at work, i have to go through a lengthy approvals process from the legal team to incorporate third-party code. Could take weeks or more to just be allowed to clone a repo, put it on our artifact server, and use it in a project. \n\nImportantly, for most of that time, its totally out of my control. I wait for legal to approve it, i wait for a technical exec to poke someone, i wait for the IT guy to get around to putting it on the server.\n\nMeanwhile, if its just a basic library, i can write it myself in an afternoon, put it up for PR, and have someone handwave it through the code review and be done within the day",
        parentId: null,
        postId: "1mlwd75",
    },
    {
        id: "n7vcr41",
        parentId: null,
        postId: "1mlwd75",
    },
    {
        id: "n7u0at5",
        body: "Most Hal layers are absolutely not an abstraction that is usable the chip vendors do this on purpose",
        parentId: null,
        postId: "1mlwd75",
    },
    {
        id: "n7vwkoh",
        body: "When the one provided does not do the job:). For example a motor driver I used a while ago controlled by I2C required an 100us delay between bytes transfer. The I2C driver did not have that option, so I needed a custom one.",
        parentId: null,
        postId: "1mlwd75",
    },
    {
        id: "n7wd397",
        body: "Fuck TI's lazy engineers for that. I'm currently dealing with those chips and there are some just bizarrely awful things about them. Chiefly; a complete lack of configurability of the device I2C address, except by using I2C - meaning that if you want more than one on the same bus, you somehow have to talk to one of them individually and change the address, completely negating the advantage of I2C in the first place.",
        parentId: "n7vwkoh",
        postId: "1mlwd75",
    },
    {
        id: "n7z6zxz",
        body: "Or you could use a multiplexer IC, also available for sale by TI ;)",
        parentId: "n7wd397",
        postId: "1mlwd75",
    },
    {
        id: "n7zaj0v",
        body: "What I'm actually doing is investigating moving the development away from TI motor drivers altogether. It's a shame, because I've had great experiences with TI parts before, but these just have a few too many fatal flaws.",
        parentId: "n7z6zxz",
        postId: "1mlwd75",
    },
    {
        id: "n8g1i53",
        body: "Really? I avoid TI like the plague 😂",
        parentId: "n7zaj0v",
        postId: "1mlwd75",
    },
    {
        id: "n7wv0wa",
        body: "There are cases where you need to interface with a peripheral device, where the device in question requires some strange variation of common protocols (i see it mostly with i2c). Also, different devices have different read\/write sequnces where one device allows continuous writing to subsequent memory locations and others dont. \n\nI personally write my own avr assembly for every project I work on, its easier to debug when you know whats going on without abstractions.",
        parentId: null,
        postId: "1mlwd75",
    },
    {
        id: "n7zyejg",
        body: "When the one you have is from the STM32 HAL",
        parentId: null,
        postId: "1mlwd75",
    },
    {
        id: "n813urz",
        body: "Lots of products use that HAL though. It’s fine for most use cases.",
        parentId: "n7zyejg",
        postId: "1mlwd75",
    },
    {
        id: "n817uxk",
        body: "Yeahhhh I’m just being snarky. It’s mostly fine but some of the drivers have been downright broken in the past",
        parentId: "n813urz",
        postId: "1mlwd75",
    },
    {
        id: "n7vvqrh",
        body: "When the drivers provided by the vendor have been caught DUI",
        parentId: null,
        postId: "1mlwd75",
    },
    {
        id: "n7wlx87",
        body: "When you have to…",
        parentId: null,
        postId: "1mlwd75",
    },
    {
        id: "n8fy6kx",
        body: "I always write my own driver. That doesn’t mean I write my own hardware layer. Usually that comes from the factory these days unlike when I started. \n\nI don’t want anything except the driver to have access to this non-lintable mess that was probably put together by some summer student in a week, however.",
        parentId: null,
        postId: "1mlwd75",
    },
    {
        id: "n7tr2xe",
        body: "Because you can't trust vendors. UART, I2C, and SPI are fairly simple to implement using very basic code. Vendors add-on a lot of features which can often screw things up more than they help. So if you want to trust your code across underlying platform code upgrades and changes over the years and across vendors, write your own and use it everywhere.",
        parentId: null,
        postId: "1mlwd75",
    },
    {
        id: "n7y6crd",
        body: "always",
        parentId: null,
        postId: "1mlwd75",
    },
    {
        id: "n7r72t1",
        body: "Well done",
        parentId: null,
        postId: "1mll4gq",
    },
    {
        id: "n7r7nq7",
        body: "Thanks ;)",
        parentId: "n7r72t1",
        postId: "1mll4gq",
    },
    {
        id: "n7xkhc2",
        body: "Hello, newbie here. I have a lot of questions, I have sent you a dm",
        parentId: null,
        postId: "1mll4gq",
    },
    {
        id: "n81g08e",
        body: "Hi, replied in the DM, let me know if I can post the reply here, might help others too.",
        parentId: "n7xkhc2",
        postId: "1mll4gq",
    },
    {
        id: "n83edu0",
        body: "Yes you may.",
        parentId: "n81g08e",
        postId: "1mll4gq",
    },
    {
        id: "n83gcmu",
        body: "Thanks man. You asked me about STM32 ressources for beginners. Here is my answer:\n\n\nHey frostyyiceberg. I'm not sure what your programming level is, but STM32 isn't too hard but it has a high learning curve at the very beginning. There's a lot of ressources, but this should get you started: I liked those Youtube series as a general introduction and what to expect when programming STM32 compared to Arduino: https:\/\/www.youtube.com\/watch?v=rfBeq-Fu0hc",
        parentId: "n83edu0",
        postId: "1mll4gq",
    },
    {
        id: "n83gqm5",
        body: "Now after you finished the series, I would continue with this book: https:\/\/www.carminenoviello.com\/mastering-stm32\/ I avoid Ebooks if I can but I bought other books and this is the only one you need. Personnaly, I bought it and then printed the PDF. This should get you started. ",
        parentId: "n83gcmu",
        postId: "1mll4gq",
    },
    {
        id: "n7tavrm",
        body: "Your simulated hall sensor angle is offset relative to what your real hall sensors would be outputting.\nYour BLDC controller wont be creating the magnetic field vector within the stator to perfectly pull on the rotor and spin it, instead it will also be pulling outwards on the rotor meaning it has to work harder to get the rotor to spin at the desired speed.\nIt will probably stop after a while because the motor sits in a spot where the magnetic field is pulling the rotor outwards more than it is pulling it axially.\n\nBasically your measured angle needs to be perfectly relative to the stator angle, otherwise it will perform badly",
        parentId: null,
        postId: "1mkrl4x",
    },
    {
        id: "n7yk6ud",
        body: "I will try changing the sensors or making a 3d printed mount around the motor with 3 hall sensors 120 degrees apart. I am unsure if it will work like that, though. I have an outrunner BLDC motor.",
        parentId: "n7tavrm",
        postId: "1mkrl4x",
    },
    {
        id: "n7ysvcq",
        body: "I think your best bet is opening the motor and seeing if the halls havent been glued down correctly, I've had the same issue in the past.\nYou will need circlip pliers to remove the circlip at the base of the motor shaft, and then you should be able to remove the rotor",
        parentId: "n7yk6ud",
        postId: "1mkrl4x",
    },
    {
        id: "n7ye7fk",
        body: "2800W???? Thats an industrial power house, are you sure about that? Anyways, how are you controlling the motor? The control circuit does everything and you only have to provide the speed reference?",
        parentId: null,
        postId: "1mkrl4x",
    },
    {
        id: "n7yjwds",
        body: "Yes, via throttle.\nThe motor, according to specifications, peaks at 120 Amps, however it no way can be continuous for the awg 14 wires it has..  The peovided details are directly from the seller amazon page.\n\nRegarding control, I bought an ESC with a TF100 throttle for electric scooter, so it can only go forward when you press the throttle",
        parentId: "n7ye7fk",
        postId: "1mkrl4x",
    },
    {
        id: "n7ym4i1",
        body: 'I see. And you use a mcu to "push" the throttle as desired to achive the reference speed, nice. \nWhy does the hall sensor need to be simulated? The esc can only take the motor speed data that way?',
        parentId: "n7yjwds",
        postId: "1mkrl4x",
    },
    {
        id: "n7ys96i",
        body: "The ESC needs hall sensor inputs from motor, otherwise it wont work",
        parentId: "n7ym4i1",
        postId: "1mkrl4x",
    },
    {
        id: "n7ls9c8",
        body: "Thank you. I designed the PCB  altium but Kicad is a great\/better free tool.\n\nI like the idea of adding a display, maybe I'll make a PCB if there's an interest.",
        parentId: "n7jlj8i",
        postId: "1mkjjpy",
    },
    {
        id: "n7x1tge",
        body: "The problem here is that I am going to hand solder almost everything. I do have access to a hot air solder station but its limited and I want to have a handmade project xD",
        parentId: "n7r8nba",
        postId: "1ml9v7s",
    },
    {
        id: "n7j7njv",
        body: "https:\/\/preview.redd.it\/9do083m2kphf1.jpeg?width=523&amp;format=pjpg&amp;auto=webp&amp;s=9ef778146e00dec24727be4a05e66a9c8998daac\n\nCO2 PPM concentration health effects",
        parentId: null,
        postId: "1mkjjpy",
    },
    {
        id: "n7lkbsh",
        body: "My CO2 sensor saw the CO2 rising to 2000ppm. But outside it sits at normal levels so I don't the callibration is that far off.\n\nHow did you callibrate yours ?",
        parentId: "n7j7njv",
        postId: "1mkjjpy",
    },
    {
        id: "n7jlj8i",
        body: "That’s pretty sick, did you design the plugin card PCB yourself? What was the motivation for making it? \n\nLooks like you’re reading the telemetry over serial? — an LCD or some type of display would be a really great enhancement to this, followed by a battery. Would be a cool and useful project to actually keep out around the house.",
        parentId: null,
        postId: "1mkjjpy",
    },
    {
        id: "n7hbqot",
        body: "C is just a tool. C++ is a tool, rust is a tool, python is a tool, assembly is a tool, theyre all just tools to solve problems.\n\nYou should understand the devices at their core at a basic fundamental level.\n\n\\&gt; \"i cant write a linkedlist right now.. i cant answer leetcode...\"\n\nyou should instead understand what is happening and what problems youre trying to solve, and how you can use C (or whatever language\/tools are available) to solve them.\n\nCan you use conditionals? can you use if\/else? Loops? do you understand pointers? Do you know what it means to write to registers \/ memory? That's really basic C.\n\nTo answer your overall question: You should learn fundamentals of C. Not high level algo crap. They have their uses, but they are also just tools, not the end all be all.\n\nthat's more important than leetcode. Without the fundamentals, it's easy to feel lost.\n\nblink an LED. Use interrupts. Play with buttons. Try a CLI.\n\nAs you do this in whatever language\/platform you want, seeing how they solve problems, dive more into the fundamentals. \"Ok I blinked an LED with the sample. What's happening on the device? PORT\/PIN? SYSTEM CLOCK? INTERRUPTS?\" etc...\n\nit will make more sense how you're using C to get there if you learn those things.\n\nit's kind of like worrying about how to make really fancy inlays\/dovetails before you even know how to use wood glue and a chisel.",
        parentId: null,
        postId: "1mkan90",
    },
    {
        id: "n7hgtvn",
        body: "Thank you very much, this is a really detailed advice. Actually, I'm not worried about the embedded systems side of the business; I'm worried that I don't know enough C programming... I see people on Youtube, Github, Discord who are very fluent in C\/C++ programming and I'm far from this level. They are doing graphics programming, writing shells, making simulations, etc. After seeing such examples, I look at myself and say to myself \"oh, we are in a very dire situation, maybe I should put aside dealing with LEDs for now and focus on C, but how will I learn C?\" ... As far as I understand from your advice, you are saying that I should continue to be interested in embedded systems and not deal with things like leetcode etc.",
        parentId: "n7hbqot",
        postId: "1mkan90",
    },
    {
        id: "n7hwo7o",
        body: "focus on the fundamentals and building up your programming aptitude as you go.\n\ni dont see much on youtube\/discord\/whatever because my algo is 99% woodworking and cooking, but i worked in MAANGAFANGwhateverthehell people call that shit and the most confusing things are very little amounts of code that take care of a lot of things in a smart way.",
        parentId: "n7hgtvn",
        postId: "1mkan90",
    },
    {
        id: "n7of7vi",
        body: "Another advice is to not trust youtubers\/social coders. 90% of the time they write the code, study It and then pretend to be writing the code for the First time ...",
        parentId: "n7hgtvn",
        postId: "1mkan90",
    },
    {
        id: "n7wes5p",
        body: "Thanks :D",
        parentId: "n7of7vi",
        postId: "1mkan90",
    },
    {
        id: "n7msqhk",
        body: "You can't write a novel without knowing the alphabet, sentence structure, paragraphs, the heroes journey, antagonists\/protagonists, foreshadowing, etc.\n\nYou start from the basics and build upon each before moving to the next level of competence in any field.  There is no way around this process.",
        parentId: "n7hgtvn",
        postId: "1mkan90",
    },
    {
        id: "n7nhiwk",
        body: "I decided to take my C and embedded system studies at the same time. I think I will learn the concepts that I do not know or have difficulty in C by practicing from time to time, I think I will write 1-2 console applications that I will use that concept rather than solving leetcode. Apart from that, I will mostly focus on embedded systems. This is how I decided after the answers to the question I asked :)",
        parentId: "n7msqhk",
        postId: "1mkan90",
    },
    {
        id: "n8892yo",
        body: "I also feel very benefited from what you said.",
        parentId: "n7hbqot",
        postId: "1mkan90",
    },
    {
        id: "n7hbzik",
        body: "Forget about leetcode until you feel ready to start applying for jobs, it’s unnecessary and not a good way to learn how programming languages are actually used in my opinion.\n\nFocus on building projects using the STM32 to get familiar with how you’d use C in actual development. Alongside that there’s probably some great books you can read that others could recommend to you. Don’t be too hard on yourself nobody is born an expert.",
        parentId: null,
        postId: "1mkan90",
    },
    {
        id: "n7hi3pe",
        body: "I'm very new to doing things with stm32, but I can't say that I've learnt much C in the things I've done so far (I think it's probably a normal situation). In other words, I haven't dealt with C much except using simple led lights and some small if-else structures, so I thought maybe I should go into deeper topics with C, such as socket programming, network, graphics.... What do you think about these topics? Is it necessary or should I not waste time with them?",
        parentId: "n7hbzik",
        postId: "1mkan90",
    },
    {
        id: "n7huyk8",
        body: "You can absolutely learn C directly in an embedded device. That's equivalent to what those of us who learnt C back in the early '90s did.\n\nAnd you will learn a lot that you'll bring to your future self. But boy, you are not going to like the path.\n\nWhy? You're missing out on the modern tools available that will help you find your bugs as you do all the mistakes that every C programmer have done:\n\nUse-after-free\nNull-pointer reference\nStack overruns\nArray-out-of-bounds accesses\nDivision by zero\n\nFor all of those (and others, too), there exists PC-based tools that detects these errors earlier. Tools that may or may not be possible to use on an embedded MCU. \n\nI certainly recommend you to get \"fluent\" in C, before digging into the embedded world. Yes, there are a class of patterns that you cannot (easily) practice on a PC:\n\nInterrupt handling\nLinker file customization\nMessage passing (yes, it is possible on a PC, too) \nHardware adaptions\nRunning short of flash, RAM or MCU processing power\n\nBut without a solid understanding of C, you won't know if the bugs are due to lack of C experience, or lack of understanding of the corner case fault modes of the pattern you are exploring. \n\nDo learn managing linked lists. Single-linked, double-linker, stacks, queues.Learn how to utilize function pointers. Sort arrays. All the boring stuff. Your future self will thank you.\n\nC is not a big or difficult language. It's just that it is 50 years old and littered with footguns. \n\nOnce you master C, you will fall in love with either Java or Rust. \n\nGood luck!",
        parentId: null,
        postId: "1mkan90",
    },
    {
        id: "n7kak9w",
        body: "I think some days I will focus on C, the rest of the days I will be interested in embedded systems. I decided to do this after this post I opened. Everyone here has helped me. Thank you to everyone.",
        parentId: "n7huyk8",
        postId: "1mkan90",
    },
    {
        id: "n7nbtq0",
        body: "I would argue that those footguns are exactly why you shouldn't bother with C beyond a very basic level unless you want a job programming in C.\n\nI learned C sometime in the late 80's. I learned C++ in the late 90's. There are better languages now. I've shipped large embedded systems (embedded PC's) based on C#.\n\nIn my day job, I have to use C++ for embedded work because that's what we standardized on. In my hobby\/freelancing work, I try to use CircuitPython\/MicroPython as much as I can because I can be so much more productive.",
        parentId: "n7huyk8",
        postId: "1mkan90",
    },
    {
        id: "n7o9w0t",
        body: "I fully agree. I did my C in early 90s, and I've recently come to enjoy Rust. But, C is - unfortunately - Lingua Franca in the industry, like it or not. It's been around for 50 years, and won't go away very soon, even if there are things changing for the better in projects that dont build on previous projects' legacy code bases. It's not impossible to get an embedded job that's not involving C or C++. It's just not as common.",
        parentId: "n7nbtq0",
        postId: "1mkan90",
    },
    {
        id: "n7k9uz8",
        body: "Thank you for this detailed and long advice, mate. I just want to keep working, I don't want to confuse my head. Whatever I'm doing, just keep doing it :)",
        parentId: "n7j10rd",
        postId: "1mkan90",
    },
    {
        id: "n7hgn14",
        body: "I don’t know how to write a linked list in c either",
        parentId: null,
        postId: "1mkan90",
    },
    {
        id: "n7ilizg",
        body: "I do, but I've not needed to write one in years. About the only reason to use a linked list is for a very unoptimized `malloc()` implementation. That's not something needed a lot in embedded. And in regular desktop\/server code you've got a CPU with a cache and linked lists have terrible cache locality.\n\nI've written some trees though. Trees are pretty useful.",
        parentId: "n7hgn14",
        postId: "1mkan90",
    },
    {
        id: "n7hihui",
        body: "Is this a normal situation... Don't we need advanced C programming knowledge for embedded systems? Or does advanced mean something beyond writing linked lists?",
        parentId: "n7hgn14",
        postId: "1mkan90",
    },
    {
        id: "n7hmkel",
        body: "You need knowledge about how things work under the hood, you should also understand what a linked list and similar structures accomplish so you know what to use in which scenario. You don't need to know how to implement one from memory, you can always look it up and use an implementation that already exists.\nEdit: I never had to implement one after I passed Data Structures in uni",
        parentId: "n7hihui",
        postId: "1mkan90",
    },
    {
        id: "n7k1jif",
        body: "I read the most important data structures at least once, learnt them, wrote them by hand on paper. I know the logic more or less. So this is enough for me, at least for now.",
        parentId: "n7hmkel",
        postId: "1mkan90",
    },
    {
        id: "n7i1x8y",
        body: "Writing a linked list isnt really C knowledge. Sure, you can use C to write a linked list, but it would fall under knowledge of data structures. Data structures are important, but you can get by (for now at least) just focusing on the fundamentals.\n\nFor reference, my bachelor's was in computer science so I learned a TON of data structure stuff. Its definitely nice to have knowledge but I haven't found myself using most of it in the day to day.",
        parentId: "n7hihui",
        postId: "1mkan90",
    },
    {
        id: "n7k1kb8",
        body: "Thanks dude.",
        parentId: "n7i1x8y",
        postId: "1mkan90",
    },
    {
        id: "n7nckop",
        body: "If I find myself needing to write a linked list in C, it most likely means that I've chosen the wrong tool for the job.",
        parentId: "n7hihui",
        postId: "1mkan90",
    },
    {
        id: "n7nghut",
        body: "Hahahah. Its veird.",
        parentId: "n7nckop",
        postId: "1mkan90",
    },
    {
        id: "n7hrz65",
        body: "Nailing Leetcode tasks isn't a good sign of good programming, don't worry about it for now. I'm an embedded C developer with 6 years of experience and Leetcode baffles me sometimes. Take your time and you'll learn what you need to as you come across it. I saw another embedded developer in my team use a linked list once and all of the other developers (from more electronics engineering backgrounds didn't get it either)",
        parentId: null,
        postId: "1mkan90",
    },
    {
        id: "n7hxjw7",
        body: "&gt;&gt;If I want to learn C, how should I go about it? \n\nGet one or more C language books.  Also, search Amazon for most popular C language books.\n\n* https:\/\/en.wikipedia.org\/wiki\/C_(programming_language)#Further_reading\n\nUse online compiler to write\/test C code.  There are many other online compiler websites too.   A person should be able to do quite a lot of C\/C++ learning with these online compilers.\n\n* https:\/\/www.onlinegdb.com\/online_c_compiler\n\n* https:\/\/godbolt.org\/",
        parentId: null,
        postId: "1mkan90",
    },
    {
        id: "n7k2dqx",
        body: "Thanks dude.",
        parentId: "n7hxjw7",
        postId: "1mkan90",
    },
    {
        id: "n7ipbpe",
        body: "The only way to get good at writing C is to write a ton of it.",
        parentId: null,
        postId: "1mkan90",
    },
    {
        id: "n7k2bin",
        body: "Do I need to code for a specific purpose to do tonnes of projects? For example graphics programming or gui, socket programming etc.",
        parentId: "n7ipbpe",
        postId: "1mkan90",
    },
    {
        id: "n7plwdx",
        body: "No need for leetcode practice yet\n\nJust learn these about C:\n* Header files \n   * Header guards\n* MACROS\n   * define\n* Pointers\n   * How to point to RAM memory address\n* Functions \n   * Callee\n   * Caller\n   * static keyword\n   * Understanding pass by reference vs pass by value\n* static, const, volatile\n* Set\/clear bitwise\n   * Clear\n        * reg &amp;= ~(1&lt;&lt;bit_shift_val)\n   * Set\n        * reg |= (1&lt;&lt;bit_shift_val)\n* typedef struct and typedef enum\n\nRegarding STM32, you need to understand the data sheet. Online source is good but that’s only a guide. The truth is always on the data sheet depending on what STM32 device you have. Most of the time STM32 has their own example with their own written HAL functions.\n\nGood luck! I only have 2 years of professional experience in Embedded so I kinda remember still how it feels to be beginner",
        parentId: null,
        postId: "1mkan90",
    },
    {
        id: "n7weqk8",
        body: "Thank you, I am trying to learn the basics thoroughly. For now, pointers and bitwise topics are a bit difficult for me. Actually, learning is easy, but I need to see where I can use these in practice... Reading data sheets may be the most difficult part for me right now (especially since English is not my native language). But I hope everything will improve with time.",
        parentId: "n7plwdx",
        postId: "1mkan90",
    },
    {
        id: "n7hbuwf",
        body: "I’m did some C about 20 years ago. I couldn’t do a link list or anything with an algorithm, yet I’ve managed to write plenty of embedded code for fun and leaning.\n\nYour level of expertise is only an issue when you do more and more advanced stuff.\n\nDon’t let a lack of experience or knowledge hold you back.",
        parentId: null,
        postId: "1mkan90",
    },
    {
        id: "n7hh5x8",
        body: "Thanks dude. Then I should stick with stm32.",
        parentId: "n7hbuwf",
        postId: "1mkan90",
    },
    {
        id: "n7ihwxb",
        body: "The best way is to build projects that you want to build. Then look up the theory that more experienced people use. Sometimes it might feel like shooting a canon on a mosquito, but you will learn a lot.",
        parentId: null,
        postId: "1mkan90",
    },
    {
        id: "n7k2i8h",
        body: "Build something in the field of embedded systems or develop some projects using C? Or both?",
        parentId: "n7ihwxb",
        postId: "1mkan90",
    },
    {
        id: "n7j10rd",
        body: 'Very few programmers write successful data structures "off the top of their head." What they *do* get done is breaking it down into steps and organizing code to do *a* step, and then another, and break the steps into smaller steps.\n\nYour example of a linked list, for example, is actually poorly defined :-). Do you want Last-in-first-out, first-in-first-out, or maybe sorted? It\'s a simple, basic question, but it\'s also important because it actually tells you where an Insert operation works; it either sticks it in at the top of the list, or travels the whole list and sticks it on the end ... but, if you know you\'re doing that, your list keeps a reference to the "head" and. "Tail" items so you never really need to travel the list at all :-) and, of course, is there a limit on the list count?\n\nGood programmers don\'t know the answers as much as they know the questions. Imagine, as another example, you have an array of integers and you want to write functions to Sum those integers, or Average them. They seem like simple methods, until you run into too many large integers, and you hit an overflow error ;-) or you find out they\'re signed integers, or unsigned... and integers can be more problematic because they can be 8-, 16-, 32-, or 64-bits. And That depends on your compiler options, or your type declarations. How you handle those options, or deciding the option(s) you support, determines your code.\n\nI would rather work with someone that can ask questions than someone who knows all the answers. I find humble balanced programmers don\'t apply a solution to a problem that adds complexity; tthey lean towards simple solutions. I\'ve fought with methods that were literally many hundreds of lines long because the dev couldn\'t see *it\'s getting out of hand*. \n\nMost learning comes from "solve a problem." "Linked list" is not a problem, it\'s part of a solution. Learning STM or embedded programming starts with "can I blink the LED?" questions. Then, can I do more than just white? Every 5-seconds.... Then you advance to other IO and can you read the serial port, or write to it? From reading an input, then outputs... Read a sensor, write the value. As you solve problems, you learn questions, and how they change your code.\n\nKeep coding ;-) break stuff ;-) fixing what you broke can reinforce lessons.',
        parentId: null,
        postId: "1mkan90",
    },
    {
        id: "n7dstqn",
        body: 'There is quite some discussion regarding this topic. You can search the sub with terms like "beginning" etc\n\nhttps:\/\/www.reddit.com\/r\/embedded\/search\/?q=starting&amp;cId=2a7dc9c0-a45b-4399-9382-c35ad939fafe&amp;iId=547dee85-78e4-450a-a480-0044924c02d7',
        parentId: null,
        postId: "1mjub5q",
    },
    {
        id: "n7ch4dj",
        body: "The 64 nodes are reasonably close. A 25-50MHz clock might be fine. \n\nThe problem is that all 64 devices on one SPI bus would require a data rate of 15Mbytes\/s or 120Mbits\/s which is higher then the RP2350 can run. Most MPUs only have a handful of SPI buses (lets say 4) and so even splitting into a banks of 16 results in 30Mbits\/s which leaves very little engineering margin.",
        parentId: "n7cds84",
        postId: "1mjmoo8",
    },
    {
        id: "n7cm0v3",
        body: "Interesting problem. You don't mention if your 64 nodes live on one PCB or if they are each external and separate hardware. You also don't say the nature of the data (if its time series data points or if its something else - the nature of the data can open or limit your possibilities). You also don't mention how the data needs to be saved. Saving to SD card, or saving directly to a PC disk drive? Fixed hardware or are you allowed to change it? Budget? You kinda need to define the hard requirements as that will give you a better idea of what solutions are possible\/feasable.\n\nAssuming each node is separate hardware device and you want to minimize wiring, you can opt to try using CAN-FD (traditional CAN wont work). Run the bus toward the upper end of speed (4-5MHz, depending on your EMC requirements). If one CAN bus is not enough, then you can run multiple CAN busses in parallel to your data collection node(s). You would also get the benefit of adding\/removing nodes on the bus at runtime without downtime. Or another idea is to have multiple nodes that aggregate data and then those data collection nodes send that collected data to a master collector node using a faster interface such as SPI or QSPI (you essentially have a tree topology here). If the data is time series, then you can collect your data in parallel and sort the data by timestamp before saving to disk, for example.\n\nMicrocontrollers that can do this will be on higher end of automotive or industrial product lines. [Infineon XMC 7100 &amp; XMC 7200 series][1] will have enough SPI\/CAN &amp; Ethernet nodes, for example. I'm sure NXP and ST also have similar product lines.\n\nIf for some reason the above doesn't cut it, then the next best option will probably be custom CPU\/FPGA solution using Xilinx Zync 7000 series or similar.\n\nJust some ideas to ponder.\n\n[1]: https:\/\/www.infineon.com\/products\/microcontroller\/32bit-industrial-arm-cortex-m\/xmc7000-m7",
        parentId: null,
        postId: "1mjmoo8",
    },
    {
        id: "n7cpfvm",
        body: 'Thanks for the detailed response.\n\nThe nodes will live on "daughter cards" with board to board interconnects so clocks can be reasonably fast and lots of lanes is not a huge issue (I am more limited by the 11 leftover GPIOs on the node). The data is time ordered but each node is actually reading out 64 or so sensors. So the data looks like an array of 64 32bit numbers sent at 1kHz. The data needs to eventually end up on a hard drive but ideally it also gets streamed over ethernet, likely a x86 machine will do the write to disk from the ethernet stream. The only hardware that is fixed is the node design (so RP2350 and 11 leftover GPIOs) as the PIO is critical to the application, but the software and remaining GPIOs can be programmed as desired. This is a scientific application so cost is not a huge concern, power is the bigger driver. Each node draws 0.5W, the aggregator should not dominate the power consumption.\n\nI have toyed with the idea of having a "tree" topology as you described but I worry that operationally maintaining more firmware spread about the system will be a nightmare.\n\nI\'ll look into CAN-FD as I do not have much experience with it.',
        parentId: "n7cm0v3",
        postId: "1mjmoo8",
    },
    {
        id: "n7cpsb8",
        body: "CAN-FD is simply not fast enough.  Don't waste time down that rabbit hole.  You'd have a very hard time getting even two nodes worth of data on a single bus.",
        parentId: "n7cpfvm",
        postId: "1mjmoo8",
    },
    {
        id: "n7davh1",
        body: "Fpga into a usb bridge IC. Look at Opal Kelly hardware.",
        parentId: null,
        postId: "1mjmoo8",
    },
    {
        id: "n7k2r0s",
        body: "Okay, this is crazy. I was joking at first but then I was like.. maybe just crazy enough to work. So I dunno if it’s a good idea but hear me out - WiFi. Just switch to a 2W. You can get a lan going and even a simple consumer grade switch could handle switching 64 connections over an internal network. I get the appeal of going ham on a pseudo-SDIO-over-PIO bus but you are just reinventing the wheel. You know what already handles time division multiplexing? WiFi.",
        parentId: null,
        postId: "1mjmoo8",
    },
    {
        id: "n7abrww",
        body: "Looks very nice.  Well done.",
        parentId: null,
        postId: "1mjd8uj",
    },
    {
        id: "n7ad8lx",
        body: "I second this, may start using it on projects. You should get this onto platformio if you havent already",
        parentId: "n7abrww",
        postId: "1mjd8uj",
    },
    {
        id: "n7ah0lx",
        body: "Check out FreeRTOS Plus CLI",
        parentId: null,
        postId: "1mjd8uj",
    },
    {
        id: "n7alqs3",
        body: "Good point -- if you're already using FreeRTOS, that is probably the right default option.\n\nThat said, while I use FreeRTOS to make life not insane in using ESP drivers, I originally made this CLI to be for bare-metal systems. Also, correct me if I'm wrong, but I think FreeRTOS+CLI doesn't support context injection for functions, so you're stuck with globals or singletons to actually touch the outside world from inside your functions. That \\*works\\* but isn't my preference.",
        parentId: "n7ah0lx",
        postId: "1mjd8uj",
    },
    {
        id: "n81jcb4",
        body: "Here's a handy write up about doing exactly what you want\n\nPutting Code of Files into Special Section with the GNU Linker | MCU on Eclipse https:\/\/mcuoneclipse.com\/2014\/10\/06\/putting-code-of-files-into-special-section-with-the-gnu-linker\/",
        parentId: "n81iuyj",
        postId: "1mn0kbp",
    },
    {
        id: "n81k942",
        body: "I will look into this. Do you know if this is specific to the GCC compiler or if other toolchains like IAR and Keil have something similar?",
        parentId: "n81jcb4",
        postId: "1mn0kbp",
    },
    {
        id: "n7k9x66",
        body: "It seems to mee you have some analysis paralysis on what to do next. The best thing is to do something to discover what's really nagging you and find out what you actually want. Which means you dabble with several things for an hour until it isn't fun any more and then take a long walk.\n\nIf you want to see how other people program in C, you can join Exercism and do some C exercises. \n\nAfter completing exercises you check how others did solve it. Some will be smarter, other won't. But they won't be courses in algorithms. \n\nOr check the sources from popular embedded operating systems. Like RIOT-OS or FreeRTOS. \n\nTrying to make Embedded more fun than watching youtube, you could try some projects from https:\/\/randomnerdtutorials.com and discover what you like the most. \n\nIn my opinion the whole leetcode thing is overblown and mostly used for job interviews. In reality you only use a few of those things and in most cases a single solution works best. Only in very specific case do you need the esoteric ones. If you run into problems, you can always look them up to solve your problems. \n\nThis YT short by Alberta Tech says it best:\nhttps:\/\/www.youtube.com\/shorts\/0H0juLZ6SfI",
        parentId: "n7k2i8h",
        postId: "1mkan90",
    },
    {
        id: "n7kjv0h",
        body: "I thought Leetcode and similar sites are the best way to master a language. After all, you are solving questions, you need to master the features of the language while solving questions, etc. randomnerdtutorials site looks very nice, thank you. The Exercism site is also great, there are 84 different questions and I can easily see people's solutions. Why can't I study both embedded systems and C programming at the same time? I think I can do that.",
        parentId: "n7k9x66",
        postId: "1mkan90",
    },
    {
        id: "n7iwr9h",
        body: "Concentrate and learn the basics. One of these basics is pointers. This isn't only a C thing, it's a computer thing. Pointers are underneath almost everything. And C pointers are pretty much a direct implementation of the basic computer pointers.\n\nSome people think of C as little more than a wrapper over assembly code, which had more validity in the early days. If you don't mind unoptimized code, you really can hand-compile C into certain assembly languages quite easily. It maps very nicely into the PDP-11 assembly language.",
        parentId: null,
        postId: "1mkan90",
    },
    {
        id: "n7k2nh3",
        body: "I'm familiar with the concept of pointers, but I can't use them perfectly. Maybe I need to work on it more, practise more, thanks.",
        parentId: "n7iwr9h",
        postId: "1mkan90",
    },
    {
        id: "n7jq9qc",
        body: "first principle",
        parentId: null,
        postId: "1mkan90",
    },
    {
        id: "n7k2f27",
        body: "Yeah..",
        parentId: "n7jq9qc",
        postId: "1mkan90",
    },
    {
        id: "n7ldeae",
        body: "Don't pressure yourself. No one became proficient within days... it took time and patience. I too am a newbie learning these things and moving one step at a time. I was once overwhelmed since I felt like my progress was stagnant but I convinced myself that nothing happens with a snap of a finger. Currently, I'm taking an online course in C Programming and almost through with it. After completing it, I'll buy an STM32 and figure out what will happen next. In short, just take things slow(not like procrastinating or sth). Learning is a process that takes time.",
        parentId: null,
        postId: "1mkan90",
    },
    {
        id: "n7nhwf8",
        body: "Thanks dude. I hope we can make it.",
        parentId: "n7ldeae",
        postId: "1mkan90",
    },
    {
        id: "n7rb2ep",
        body: 'Knowing C and knowing DSA are two completely different things.\n\nTo learn straight "C", maybe stick with learning to turn LED-s on and off and read switches for a bit until that becomes comfortable.\n\nThen... spend some time learning how to write linked lists in C, there is plenty of help out there.\n\nOnce you have that... here\'s where it gets fun:\n\nIn your STM32 LED code...create a structure that contains "instructions" e.g. LED-ON, LED-OFF etc, then set that up as a list of instructions and run it by walking the list over and over until maybe a switch is pressed. Hey, you just wrote your own simple CPU!!!\n\nOnce the addiction starts...',
        parentId: null,
        postId: "1mkan90",
    },
    {
        id: "n7we5qv",
        body: "Thanks for your advices my friend.",
        parentId: "n7rb2ep",
        postId: "1mkan90",
    },
    {
        id: "n7xsh8o",
        body: "Always glad to help, it's a long and lonely learning curve at times, but ig you are truly motivated enough it's just another obstacle to overcome!",
        parentId: "n7we5qv",
        postId: "1mkan90",
    },
    {
        id: "n82wttd",
        body: "STM32's (or any Arm Cortex-M) is in my opinion a bit much for a beginner. I always recommend starting with Arduino, preferably the Arduino UNO, to figure out relation between thought, code, time and other real world phenomena using LEDs, motors etc. Then use the atmega328p (the MCU on the Arduino UNO) w\/o Arduino framework to learn how the most common peripherals (timer, ADC, UART etc) and things like interrupts work.\n\nThen I recommend learning how STM32's work or whatever your goal is.\n\nI think Arduino -&gt; atmega328p bare-metal is an unbeatable combo because of how easy Arduino is to use and how simple the atm328p architecture is but that doesn't mean you can't start with STM32's right away if you want to dive in on the deep end.\n\nYou can write an entire program for an atmega328p with a long list of assignment inside main if you want so C requirement is very low (I never had a dedicated C course). Then as you learn about repetition and conditionals you'll improve your program but not even that much is needed to start.",
        parentId: null,
        postId: "1mkan90",
    },
    {
        id: "n89cmw5",
        body: "Thanks, dude. To be honest, I used Arduino Uno once and did some small projects, so I think it's appropriate to start with STM now. I've never used atmega328p, but I'm not sure if I will use it or learn it.",
        parentId: "n82wttd",
        postId: "1mkan90",
    },
    {
        id: "n89pyc1",
        body: "It's not a bad choice! I use arduino and\/or stm32 for everything pretty much.\n\nWhen it comes to ST's documentation, their app notes and examples are awesome and they have them for just about everything. \n\nI just google \"stm32 app note &lt;topic&gt;\" and find direct links. The examples you can find in the FW repositories you download and on github. It really helps to see examples.",
        parentId: "n89cmw5",
        postId: "1mkan90",
    },
    {
        id: "n7l61th",
        body: "OP，I think novices don't need to touch the underlying at the beginning, let alone optimize the performance of algorithms, start with simple peripherals, learn to use ifelse to implement simple logical business, when you understand what you can do. At this point, you need to expand your horizons and think about why this phenomenon is happening, the work of the CPU, registers, etc. Once you have an idea of what you think, you can start thinking about using algorithms to optimize your project.",
        parentId: null,
        postId: "1mkan90",
    },
    {
        id: "n7ni696",
        body: "As I said in my other comments, my aim was not to optimise or write healthier code. I don't have much command of the C language at the moment, I thought about how to master it more and I thought of solving Leetcode questions :)",
        parentId: "n7l61th",
        postId: "1mkan90",
    },
    {
        id: "n7q3koo",
        body: 'I apologize for not making it clear from the beginning. From other comments, I sense that you are more interested in learning C language to make a big splash in the Linux field, but my advice is on how to learn MCU. Speaking of which, first, you need to understand that algorithms are a way of thinking rather than a language. You just want to use C language to concretize abstract thinking. Secondly, I think you should implement a few command-line programs. Although they may seem outdated now, they are the best way for beginners to practice. To quote a classic saying from our country, "A journey of a thousand miles begins with a single step." As long as you grasp the basic syntax of a language, you just need to keep practicing algorithm problems.',
        parentId: "n7ni696",
        postId: "1mkan90",
    },
    {
        id: "n7weeds",
        body: " Thanks for your detailed advices my friend. I hope I can make it this damn C lang and embedded systems :d",
        parentId: "n7q3koo",
        postId: "1mkan90",
    },
    {
        id: "n7lsqj7",
        body: "mark",
        parentId: null,
        postId: "1mkan90",
    },
    {
        id: "n839u78",
        body: 'If you want a "standardized data object and command model", the first choice is OPC UA.\n\nIf you need lightweight transmission, MQTT + Sparkplug B can be used as a communication protocol and message framework.\n\nThe combination of the two is used to define the data model and command interface with OPC UA in the design, and to encapsulate messages and manage states with Sparkplug B.\n\nDLMS\/COSEM is more in the field of smart metering, suitable for power and utilities, less suitable for general industrial equipment.\n\nBTW，I‘m Micky Fang，you can find me on Linkedin',
        parentId: null,
        postId: "1mk321q",
    },
    {
        id: "n83bcba",
        body: "Ok. Thanks so much for the information. I have suggested sparkplug. Will look at the opc ua for the data model",
        parentId: "n839u78",
        postId: "1mk321q",
    },
    {
        id: "n81omo8",
        body: "don't forgot to add another option: dry film photoresist + laser\/inkjet printed transparency as photomask. \n\n\\- some wet chemicals are required -- Na2CO3 for photoresist development, NaOH for stripping photoresist, HCl+H2O2 for copper etching. \n\n\\- check out dupont riston for dry film photoresist\n\n\\- photomask could potentially be replaced by UV DLP printer head\n\n\\- PCB rivets can be used to make vias \n\none comment about CNC method is that no FR4 stock is perfectly flat, and as a result you will have to deal with leveling with workarounds like [this](https:\/\/hackaday.com\/tag\/autoleveling\/). \n\nHonestly DIY PCB isn't worth the time if you just need the board. A week's wait for professionally made boards is really nothing compared to the hassle.\n\nIf long-term sustainability matters to you. I'd argue that designing for homelab methods often requires deviation from standard PCB design practices, and thus not sustainable if your long term goal is making professional PCBs.",
        parentId: null,
        postId: "1mmzjrc",
    },
    {
        id: "n859ock",
        body: "3m dry film with a screen used for resin 3d printer. (I use the actual printer). It has superb resolution and is faster\/has less defects than using prints.\n\nIf you have a print shop nearby you can also use proper film for masking.\n\nCnc for drilling\/cutting.\n\nUnfortunately there are no good options for vias.\n- Rivets are quite expensive and time consuming.\n- Chemicals are short lasting, expensive and nasty.\n- Soldering a thin tubes intended for crimping wire-ends is pleasant and fast but requires big vias but you can then solder the components through hole.\n- thin wire soldered both sides gives the smallest vias that a hobbyist can make, but takes more time than tubes. I’ve made a cutter\/bender to produce wire pieces, then you stick them through holes, solder one side on the whole board, flip it and solder the other. Hot air\/reflow unfriendly. You have to solder components first or you risk damaging vias, so can’t check continuity. ",
        parentId: "n81omo8",
        postId: "1mmzjrc",
    },
    {
        id: "n87k4vw",
        body: "thanks for sharing the resin printer method! have you incorporated pre-bake and post exposure bake on the photoresist before and after exposure? I used a laminator for this. with a microscope I was able to observe some benefit from these steps. Things like better adhesion and smoother edges. \n\nwant to add that bare copper is hard to solder. Adding liquid tin could marginally improve solderability, but this is nasty wet chemical +1.",
        parentId: "n859ock",
        postId: "1mmzjrc",
    },
    {
        id: "n8b2dv7",
        body: "I put the resist with laminator but no post bake. Just development and etching. \n\n&gt; want to add that bare copper is hard to solder\n\nI put the board into the etching solution for 10 seconds before resist application - removes oxides and gives a grippy surface. It solders well (with flux of course) for about a week or two. I have tried elecroless tin bath once and that was enough - after two weeks it was really hard to solder.\n\nIf I needed to tin the pads just a bit of wick on the iron tip was working reasonably well. ",
        parentId: "n87k4vw",
        postId: "1mmzjrc",
    },
    {
        id: "n822qv7",
        body: "This is pretty much the process the commercial manufacturers use. \n\nDealing with the printers and somewhat toxic chemicals is just so annoying that it’s more convenient to just order the boards premade.",
        parentId: "n81omo8",
        postId: "1mmzjrc",
    },
    {
        id: "n831gep",
        body: "You can switch to a less ugly etchant at home. I've always stuck to stuff that I can safely evaporate or dispose in the sink. Sodium persulfate works fine. NaOH is literal drain cleaner, and the acetone for stripping is usually gone before you even think about it.",
        parentId: "n822qv7",
        postId: "1mmzjrc",
    },
    {
        id: "n87rtq3",
        body: "Uhm, I thought it's not the etchant but the copper?\n\nBack when I was etching PCBs I used HCl for etching, which literally neutralizes to salt water, but since I can't get the copper back out it's still toxic waste.",
        parentId: "n831gep",
        postId: "1mmzjrc",
    },
    {
        id: "n88glke",
        body: "HCl doesn't dissolve the copper by itself, you need H2O2 in addition. The most commonly used etchant is ferric chloride. Both are a hassle because of the corrosiveness (and for ferric chloride, the staining).\n\nWith sodium persulfate, any superfluous etchant solution can be disposed of via the drain if sufficiently diluted.\n\nYou are right that the used etchant should be evaporated and the solid be collected and treated. However, people used to flush cupric sulfate down the drain on purpose to prevent tree root growth, despite being toxic to aquatic life, so make of that what you will.\n\nEither way the sodium sulfate requires heat and quite some time to even etch copper, so it's significantly less nasty than the other options.",
        parentId: "n87rtq3",
        postId: "1mmzjrc",
    },
    {
        id: "n8fxlop",
        body: "&gt; Either way the sodium sulfate requires heat and quite some time to even etch copper, so it's significantly less nasty than the other options.\n\nAnd quite forgiving due to slower etching. An aquarium heater and either a pump or areator is enough for etching traces up to 8 mils consistently. 5-6 mils is doable if you have good resist but requires rotating the pcb to etch all sides evenly (bubbles mix the solution well, but create over etching on the bottom and under etching on the top of traces). ",
        parentId: "n88glke",
        postId: "1mmzjrc",
    },
    {
        id: "n8fy2xx",
        body: "I've always done it in a hot water bath and with manual agitation. Obviously not an option if you have some quantity to etch.\n\nIt's slower, but that's simply due to it not being as corrosive.",
        parentId: "n8fxlop",
        postId: "1mmzjrc",
    },
    {
        id: "n859zh0",
        body: "Unless you’re plating the holes the chemicals are less harsh than bathroom cleaner. ",
        parentId: "n822qv7",
        postId: "1mmzjrc",
    },
    {
        id: "n81gwjw",
        body: "The biggest problem with at-home making of double-sided PCBs is that there is absolutely no way to do plated-through holes.\n\nWith that limitation, you have to compensate in one of a few different ways:\n\n1. Use metal inserts in all holes and vias and solder them.  \n2. If using a THT component, it has to be soldered well enough that the solder contacts the traces on the top and bottom, this may involve soldering the THT lead on both sides.  \n3. For vias, you may have to use a small wire and solder it on both sides.  \n4. Redesign the board to use copper on only one side, and make liberal use of 0 ohm resistors for jumpers, or solder wire jumpers on the board.\n\nI would say that any at-home PCB making process is only useful if you're doing single-sided copper.  If you have to have 2 layers, it simply isn't practical by any method.",
        parentId: null,
        postId: "1mmzjrc",
    },
    {
        id: "n7dpvqn",
        body: "This will be the best way OP. Just dont use hub chip to connect more hub chips (you will be limited by total bandwidth and will overall have to deal with more constraints), get a 1 to 16 hub chip if thats out there and then get any mpu that has 4 usbs ports. It sould be easy with the amount of data you have.\n\nKeep us posted.",
        parentId: "n7dktwt",
        postId: "1mjmoo8",
    },
    {
        id: "n7caii9",
        body: "MLVDS, ethercat, ethernet",
        parentId: null,
        postId: "1mjmoo8",
    },
    {
        id: "n7ccopo",
        body: "Sadly ethernet\/ethercat is a little too intensive to emulate in software without a dedicated peripheral. Or are you suggesting external SPI&lt;-&gt;Ethernet chips? I still worry about the complexity of adding ethernet into the mix but it may be doable.",
        parentId: "n7caii9",
        postId: "1mjmoo8",
    },
    {
        id: "n7cfsa5",
        body: "RP2350 can drive an Ethernet phy. There's also CAN. I was just giving you options on how you can have multiple devices on a single bus with a high ish data rate.",
        parentId: "n7ccopo",
        postId: "1mjmoo8",
    },
    {
        id: "n7d1p1p",
        body: "CAN is typically 1MHz.  It can get higher speeds using RS-485 as the transport.",
        parentId: "n7cfsa5",
        postId: "1mjmoo8",
    },
    {
        id: "n7fglei",
        body: "Can FD is 8, XL 20",
        parentId: "n7d1p1p",
        postId: "1mjmoo8",
    },
    {
        id: "n7fqv5g",
        body: "Still nowhere near the data rate needed by OP.",
        parentId: "n7fglei",
        postId: "1mjmoo8",
    },
    {
        id: "n7cymex",
        body: "Can the data from the nodes be compressed?",
        parentId: null,
        postId: "1mjmoo8",
    },
    {
        id: "n7dpjym",
        body: "How about ethernet? Wiznet do some inexpensive Ethernet+TCP\/IP in a chip solutions, which take SPI or parallel on the microcontroller side. These can go on your data collection units. Then the aggregator can be anything with an ethernet port, any number of ARM or RISC-V SoC boards.",
        parentId: null,
        postId: "1mjmoo8",
    },
    {
        id: "n7cds84",
        body: "How physically close are the 64 nodes ? Could you wire up a shared SPI bus with acceptable signal integrity ? You might even be able to run I2C fast enough, which uses fewer wires but is severely limiting on bus length.\n\nDid you leave one of the RP2350 SPI pin sets free ? Otherwise you can use a spare PIO to bitbang the SPI protocol. (There's an implementation in the Arduino BSP if you need a head start).\n\nThen just round-robin each node in turn. You will be able to run SPI sufficiently fast on RP2350 to get the data out, if the wiring length will permit it.\n\nIf you can get the data into a Linux SBC over SPI, you can get it out again using gigabit ethernet if required.",
        parentId: null,
        postId: "1mjmoo8",
    },
    {
        id: "n7cgrwk",
        body: "120Mhz is screaming fast for SPI, and completely impossible for I2C.  And that's without even considering overhead.",
        parentId: "n7cds84",
        postId: "1mjmoo8",
    },
    {
        id: "n82ywcf",
        body: "TX doesn’t need to be pulled up.",
        parentId: null,
        postId: "1mn7gdk",
    },
    {
        id: "n8303yx",
        body: "Ok if so when i observed the communication isolating a single device using saleae, i saw that the tx bits are being reflected at the rx frame too. what would the reason be ? Is that normal ? here d6 is tx and d7 rx. why is the tx bits reflected at the rx frame ?",
        parentId: "n82ywcf",
        postId: "1mn7gdk",
    },
    {
        id: "n8308yj",
        body: "This is rs485. If your running full duplex then whatever is sent will be echoed back. Half duplex won’t",
        parentId: "n8303yx",
        postId: "1mn7gdk",
    },
    {
        id: "n83153i",
        body: "I am doing bidirectional half duplex.",
        parentId: "n8308yj",
        postId: "1mn7gdk",
    },
    {
        id: "n831ase",
        body: "If your running half duplex then you shouldn’t be receiving what you’re sending. Do you have an image of your schematic?",
        parentId: "n83153i",
        postId: "1mn7gdk",
    },
    {
        id: "n831mj5",
        body: "Well what would be the possible reasons for this to happen ? I do not have the schematics.",
        parentId: "n831ase",
        postId: "1mn7gdk",
    },
    {
        id: "n831o9r",
        body: "The only way is it’s wired as full duplex",
        parentId: "n831mj5",
        postId: "1mn7gdk",
    },
    {
        id: "n83lgnu",
        body: "Its half Duplex. The only issue is that when using rs485 with multiple devices, it works fine for some time and then at some point some of the devices stops responding.",
        parentId: "n831o9r",
        postId: "1mn7gdk",
    },
    {
        id: "n8383ph",
        body: "Are you disabling the receiver on the 485 driver when transmitting?",
        parentId: "n831mj5",
        postId: "1mn7gdk",
    },
    {
        id: "n838e06",
        body: "No, I am using an auto direction control transceiver.",
        parentId: "n8383ph",
        postId: "1mn7gdk",
    },
    {
        id: "n83bcwf",
        body: "It looks like it has a receiver enable pin?",
        parentId: "n838e06",
        postId: "1mn7gdk",
    },
    {
        id: "n83iulz",
        body: "Yes but for auto direction control, the MCUs Tx Pin can be connected to the driver data input.",
        parentId: "n83bcwf",
        postId: "1mn7gdk",
    },
    {
        id: "n81iuyj",
        body: "Linker script..\n\n    .mysection :\n    {\n      my_file.o(.text)  \/* Place .text section of my_file.o into .mysection *\/\n    } &gt; MY_SPECIAL_REGION",
        parentId: null,
        postId: "1mn0kbp",
    },
    {
        id: "n81v10g",
        body: "Yeah, IAR can do it like this in the linker..\n\n    define region UTILITIES_region = mem:[ from 0x71000 to 0x71FFF ]; \/\/ Define the memory region\n\n    place in UTILITIES_region { readonly object my_file.o }; \/\/ Place all read-only content from my_file.o\n\nI'm sure Keil has something similar.  Probably the Scatter (.sct) file",
        parentId: "n81k942",
        postId: "1mn0kbp",
    },
    {
        id: "n81jyvp",
        body: "Thank you, was... Surprisingly simple.",
        parentId: "n81iuyj",
        postId: "1mn0kbp",
    },
    {
        id: "n805g0a",
        body: "One thing that comes to mind: Does the printer use 3.3V or 5V TTL?",
        parentId: null,
        postId: "1mmtshp",
    },
    {
        id: "n814s1h",
        body: "Oh man, I hadn't even considered that - good catch. The uno is 5V, and the cable I got is 3V3. Seems like as good a place to start as any, thanks for the heads up.",
        parentId: "n805g0a",
        postId: "1mmtshp",
    },
    {
        id: "n842m7m",
        body: "That's a really good observation. For the most part it wouldn't make sense to use an MCU for a traditional buck or boost Converter, the complexity and cost would be substantially higher. Where it does make sense is in more complex topologies, things like power factor correction applications and high power isolated topologies, where a cots solution might be hard to find.\n\nSelf promotion warning ahead\n\nThere is one topology that I'm trying to open source as it's surprisingly useful and powerful, especially in applications like fpgas where you have multiple series converters. There's no compensation loop so there's minimal burden on the MCU.\n\nhttps:\/\/github.com\/resonantlabs\/Intermediate-Bus-Converter",
        parentId: "n835sd1",
        postId: "1mmtmdy",
    },
    {
        id: "n7zjx1d",
        body: "are you trying to bare metal x86? because most OS and RTOS I known already have such functionality...\n\nother than that yes, idle task is one of the ways to calculate cpu utilization",
        parentId: null,
        postId: "1mmpf54",
    },
    {
        id: "n7zmfps",
        body: "&gt; are you trying to bare metal x86?\n\nThat would be quite an undertaking on any x86 cpu less than 30 years old...",
        parentId: "n7zjx1d",
        postId: "1mmpf54",
    },
    {
        id: "n7zxoc7",
        body: 'Not wanting to downplay it, but "bare" doesn\'t mean you need to build your own mainboard and BIOS around a modern CPU. Your fancy Ryzen 13 can still boot FreeDOS.',
        parentId: "n7zmfps",
        postId: "1mmpf54",
    },
    {
        id: "n800k3j",
        body: 'Bare metal _does_ mean not using an OS (that is the very definition of "bare metal") and that includes not using FreeDOS. So you get to enjoy things like writing a boot sector, your own filesystem code, figuring out memory, handling protected mode switching etc etc.\n\nThere\'s a reason nobody except BIOS \/ OS \/ low level diagnostic tool devs have written actual bare metal software for x86s since the days of 486 (or more realistically since 286).',
        parentId: "n7zxoc7",
        postId: "1mmpf54",
    },
    {
        id: "n81uj1n",
        body: "I'm sure they have something similar, but I've never done this with anything other than GCC, so I can't say for sure.",
        parentId: "n81k942",
        postId: "1mn0kbp",
    },
    {
        id: "n7zname",
        body: '\/ scheduler.c uint64\\_t idle\\_ticks = 0; uint64\\_t busy\\_ticks = 0; void scheduler\\_tick(void) { process\\_t \\*next = pick\\_next\\_process(); if (next == NULL) { idle\\_ticks++; asm volatile("hlt"); } else { busy\\_ticks++; switch\\_to(next); } } You mean I should implement something like this, without making an idle process?',
        parentId: "n7zjx1d",
        postId: "1mmpf54",
    },
    {
        id: "n7ztbd9",
        body: "Yes.  \nThe OS requiring an always-runnable idle process is wasteful and a poor design choice IMHO.",
        parentId: "n7zname",
        postId: "1mmpf54",
    },
    {
        id: "n7zpsl8",
        body: "I don't know x86 assembly or how interrupts work there, but assuming you can safely call halt inside your scheduler tick this should work",
        parentId: "n7zname",
        postId: "1mmpf54",
    },
    {
        id: "n89gz8u",
        body: "Nitpick: depending on where \/ how scheduler\\_tick is called, I would make the \\_ticks variables volatile. ;-) \n\nIn fact, (also reading up on the details of 'hlt' instruction) depending on hów that function is called, I'm not entirely sure executing it here is very smart... It effectively stops execution of code 'until a next interrupt', presumably meaning that after that it continues executing the function. This in and on itself is 'fine' (effectively the function exits), but íff the OS running the tasks also has background processes for maintenance of the (RT)OS, those will have some trouble executing.",
        parentId: "n7zname",
        postId: "1mmpf54",
    },
    {
        id: "n7zwlfl",
        body: 'Instead of timing "idle" times, you can also time each process\'s runtime (runtime += t\\_switched\\_out - t\\_switched\\_in). You can calculate load\/usage from that, plus you get the CPU load of each process.',
        parentId: null,
        postId: "1mmpf54",
    },
    {
        id: "n7z33g0",
        body: 'You can write a struct definition in a propriety language, and then have it compiled into .h and .py files automatically. There are about 100 of these tools, the most widely deployed is arguably Protobuf by Google. \n\nSome tools in this field offer "zero copy", which might be helpful in the embedded environment to save some cycles. Cap\'n Proto and FlatBuffers are such examples. \n\n[https:\/\/protobuf.dev\/](https:\/\/protobuf.dev\/)\n\n[https:\/\/capnproto.org\/](https:\/\/capnproto.org\/)',
        parentId: null,
        postId: "1mmortn",
    },
    {
        id: "n7zosf9",
        body: "Thats what I need. Thanks",
        parentId: "n7z33g0",
        postId: "1mmortn",
    },
    {
        id: "n7zxeeg",
        body: "NanoPB is a popular ProtoBuf library for embedded devices. Written in C. No allocation by default.",
        parentId: "n7zosf9",
        postId: "1mmortn",
    },
    {
        id: "n82vq2z",
        body: "This was closer to what I needed. The problem is that, from my point of view, it’s not good for microcontrollers. It cannot create uint8_t; everything is created as uint32_t, which is a waste of space.",
        parentId: "n7zxeeg",
        postId: "1mmortn",
    },
    {
        id: "n87dmzf",
        body: "This isn't true, there is an options file to change the uint32\\_t to any other smaller type. I am using it now to turn into int16\\_t. The documentation isn't clear but it is there.\n\nIn the options file:\n\n`&lt;variable-name&gt; int_size:&lt;size&gt;`\n\nwhere &lt;size&gt; can be IS\\_8, IS\\_16 etc. The generated .c file will have int8, int16 respectively\n\n[https:\/\/github.com\/nanopb\/nanopb\/blob\/master\/generator\/proto\/nanopb.proto](https:\/\/github.com\/nanopb\/nanopb\/blob\/master\/generator\/proto\/nanopb.proto)",
        parentId: "n82vq2z",
        postId: "1mmortn",
    },
    {
        id: "n82wbyh",
        body: "Thats true, but I’ve found on most middle sized 32 bit MCUs it’s not really an issue. But you’ll need to access it for your use case.",
        parentId: "n82vq2z",
        postId: "1mmortn",
    },
    {
        id: "n805c7c",
        body: "I love LCM types - but yes. You basically need an intermediate description language, and code-gen the C and the Python from the same source.",
        parentId: "n7z33g0",
        postId: "1mmortn",
    },
    {
        id: "n7zm57n",
        body: "CrazyCrazyCanuk answer is probably the best approach but you can also write a Python script that parses your .h file and extracts the structs and enums",
        parentId: null,
        postId: "1mmortn",
    },
    {
        id: "n82vy2j",
        body: "In the end, I tested many different solutions, but I had to create a custom Python script to read my .h file, just as you suggested, because none of the existing solutions were as perfect as I wanted. And when working with microcontrollers, space reduction is a\n\nmust.",
        parentId: "n7zm57n",
        postId: "1mmortn",
    },
    {
        id: "n7zoxwc",
        body: "Yes, but I prefer not to create something new. Thanks for your help",
        parentId: "n7zm57n",
        postId: "1mmortn",
    },
    {
        id: "n82nvt2",
        body: "The [cstruct library](https:\/\/pypi.org\/project\/cstruct\/) seems to do what you want. I use it to generate a header for my firmware binaries. For messaging I think protobuf like the other commenter says might be a better fit.",
        parentId: null,
        postId: "1mmortn",
    },
    {
        id: "n82usmn",
        body: "Protobuf is what you are looking for. From a single source of truth, you can generate for different languages",
        parentId: null,
        postId: "1mmortn",
    },
    {
        id: "n82w2ql",
        body: "Protobuf can not generate C language",
        parentId: "n82usmn",
        postId: "1mmortn",
    },
    {
        id: "n82wejz",
        body: "Yes it can, the protoc compiler supports it",
        parentId: "n82w2ql",
        postId: "1mmortn",
    },
    {
        id: "n7zza8f",
        body: "You could take a look at ASN.1. Its used extensively in the telecom world and was designed to solve your exact problem.",
        parentId: null,
        postId: "1mmortn",
    },
    {
        id: "n80yjlk",
        body: "Your concerns around the out-of-sync are misguided.  The issue is rarely that the C and Python code bases get out of sync, that's easy to solve either with tools (as mentioned by other replies) or just by updating both at the same time. The far more common issue is release control, that the MCU is running a different version to the Python and you get out of sync that way.\n\nThere's a bunch of ways of solving it, just forcing updates, good design to retain backwards compatibility, version fields etc. All the solutions have drawbacks though, it's a significantly more complex issue than source synchronization.",
        parentId: null,
        postId: "1mmortn",
    },
    {
        id: "n85x8rp",
        body: "Yea. It's an incredibly basic tool. You use any llm library to load a document into a vector database which can then be queried using the same library mentioned above. It's literally like 5 lines of code",
        parentId: null,
        postId: "1mnmpw6",
    },
    {
        id: "n85y5ag",
        body: "I’ve used the feature in co pilot where you can upload PDFs of ref manuals but my personal experience of it was quite mixed when attempting generate peripheral init functions for the STM32 using register access",
        parentId: "n85x8rp",
        postId: "1mnmpw6",
    },
    {
        id: "n8760em",
        body: "IME I can't even get the newest, best models to go from register description in the abstract to an actual functional implementation. It goes from bad to worse if the registers depend on each other in any way.\n\nI've only found them useful in this application for narrowing down where in the thousands of datasheet pages I can find what I'm looking for.",
        parentId: "n85y5ag",
        postId: "1mnmpw6",
    },
    {
        id: "n88aw2y",
        body: "That was my experience too, specifically using the STMG4 series of boards when attempting to use co pilot by feeding in the 2000 page reference manual😂\n\nIf there was a tool that could generate register level boiler plate from the reference manual would you find it useful?",
        parentId: "n8760em",
        postId: "1mnmpw6",
    },
    {
        id: "n88ehgl",
        body: "Ah.. so you want to invent the ST HAL? Could make some tasks easier. Maybe we should ask ST.",
        parentId: "n88aw2y",
        postId: "1mnmpw6",
    },
    {
        id: "n88hj9x",
        body: "I was more thinking in terms of LL api\/register level for where niche examples for that particular micro don’t exist. For example using the HRTIM with the DMA for the stm32g474 to create a varying duty cycle based on a look up table",
        parentId: "n88ehgl",
        postId: "1mnmpw6",
    },
    {
        id: "n85bdz5",
        body: "At home double-side pcb making can make sense. If you don’t have many vias you can have a pcb ready in about 2-3 hours. So you can actually make two iterations of a working prototype over the weekend. \n\nAs I have some disposable money but little time I just order pcbs. But as a student there was so much I could do with just a few dollars worth of materials it was beautiful. ",
        parentId: "n81gwjw",
        postId: "1mmzjrc",
    },
    {
        id: "n81h3bk",
        body: "There's no good way.  Order them.\n\nBetween drilling, plating or soldering vias, etching or milling, and the fact that **none** of the DIY solutions handle fine pitch very well or have solder mask, it's just not practical.\n\nBe happy that it's not 20 years ago where a few shitty two layer boards cost a minimum of a few hundred bucks and took 2-3 weeks.  And 4 layer or more for hobby use?  Forget about it.",
        parentId: null,
        postId: "1mmzjrc",
    },
    {
        id: "n85bytv",
        body: "You can do solder mask the same way you etch them. \n\nHave a laser jet that can print on transparent film. And that’s all ",
        parentId: "n81h3bk",
        postId: "1mmzjrc",
    },
    {
        id: "n82ehix",
        body: "Do it like we have done them 20yrs ago.\n\nPhotoresist board, single or dual side, no plated holes and high speed drills for holes.\n\nOr laser toner transfert, very limited resolution.\n\nI have done both ways, I don't want to touch this anymore (ferric chloride stains...).",
        parentId: null,
        postId: "1mmzjrc",
    },
    {
        id: "n83pvp5",
        body: "How limited? I used to do tqfp144 just fine with a laminator. Not that I would recommend that.. Vias are the worst. Hard to make them reliable enough.",
        parentId: "n82ehix",
        postId: "1mmzjrc",
    },
    {
        id: "n87so5f",
        body: "0.635 was the smallest I could do with my printer (LaserJet 6L).",
        parentId: "n83pvp5",
        postId: "1mmzjrc",
    },
    {
        id: "n865td2",
        body: "This is going to sound kinda dickish but the solution here is to spend the time at design. If you're making multiple iterations of a proto in a weekend it's because you're not doing the work required to get better. Once you read the datasheet, do the simulations, think through the various scenarios and complete a thorough design review, even just with yourself,  you should be getting mostly \"right the first time\" boards back. If you're doing work that's so simple you can etch, populate and test multiple versions in a weekend you can absolutely design them right instead. If you're doing dense FPGA boards with multiple high speed interfaces you will certainly make a mistake or two but you also can't make them or test them faster than JLPCB. \n\nHardware isn't software, it takes a different mindset to be good at it. You need to be willing to sit down and plan and anticipate issues in your head rather than hitting Build\/Run and see what's broken.",
        parentId: null,
        postId: "1mmzjrc",
    },
    {
        id: "n86ypzr",
        body: "Not as dickish as it might initially seem, in a perfect environment with spec. sheets matching both parts and circuitry you are correct, knowledge and simulation is a better solution. For that a onetime board order is appropriate. If on the other hand if you are trying to establish interconnections between a number of unknown variables within a prototype a single board might not allow for the number of different changes or iterations in design available by one single assembly.\n\nThe best approach should be determined by the reasoning behind making the project in the first place.",
        parentId: "n865td2",
        postId: "1mmzjrc",
    },
    {
        id: "n875m27",
        body: "I stand by my original statement, any board that can be spun and tested in a weekend isn't complex enough to need it. There are certainly undiscovered bugs that don't show up in the errata buried deep in STMicros or possibly some faint non linear high speed behavior in some ADC collecting pixel data but none of that is the kind of thing you can even identify in a weekend much less fix and re build. I've been doing this a fair long time and I can count the number of board killing errors that have truly been from bad datasheets on fingers and toes. The far larger quantity have been from mine and my coworkers failures to thoroughly read those specs and understand what we're doing. \n\nAt the end of the day OP is a grown person spending their own money so if they want to etch boards for hobby projects they should. I'm mostly speaking to the people who are getting into this for a living. You shouldn't be making easy to fix mistakes, you should be catching those in the design process not the prototype process",
        parentId: "n86ypzr",
        postId: "1mmzjrc",
    },
    {
        id: "n87t7e4",
        body: "Well stated, I am not disagreeing with you and I agree with your pretense that commercial boards should be debugged before any production.\n\n As someone that has been professionally reviewing board designs for manufacturability since the advent of multi-layer PCB’s, I have seen firsthand the different changes in the ways that chips and board interconnections have changed in process and design. Most of these changes come from prototype iterations, tests and changes. As example the ways ultrasonic vibration has changed the way hole wall plating is done, and vacuum via fill has replace solder filled holes, and how flip chip wire bonding has made component pin pitch footprints smaller and smaller, all these were first prototyped from homemade equipment. Some of the boards for chip testing equipment that I have recently reviewed are at the limit of the equipment that tests them, with current micro assembly flip chip’s having more and more interconnects, new products will need to be made to keep up with the technology. If a new processes produces a better result than it is worth the investigation.\n\n If someone is interested in how to make prototypes and wants to produce something different for a quick demonstration, they can order a board and wait for the standard manufacturing processes with tariff to determine the delivery and final cost, or possibly make it themselves. I’m an optimist that thinks a new set of eyes on an old procedure is a good thing even if it’s part of the learning curve.",
        parentId: "n875m27",
        postId: "1mmzjrc",
    },
    {
        id: "n8btc48",
        body: "I agree with you that prototyping and iteration before ordering a PCB makes sense and should work most of the time. However, assume there is a way for hobbyists like me to send PCB design test ideas and iterate faster, just like 3d printing. Assume I want to test an RC plan with a board that someone did, I can just take the design, print it in a couple of nights, and have fun assembly print a PCB, then fly it. Some other random hero makes a v2 of the board, and I can print it in no time. I came up with v3? No problem, I can test it out. \n\n\n\nI'm trying to make the test cycles faster (in the air). I'm not an electric engineer, so I'll make a lot of mistakes like in 3D printing, CNCs, and laser cutting. That's why I wanted a cheap way. It looks like what I want doesn't exist yet, but people are coming closer to doing it. Multiple ways to make traces. And found this for vias [https:\/\/youtu.be\/0vtCyUHz1Mo?si=wQQzQ-nNyeypDppV](https:\/\/youtu.be\/0vtCyUHz1Mo?si=wQQzQ-nNyeypDppV)\n\n\n\nIt is still a looong way to go, but I'm interested in trying those methods, and maybe I can add something one day",
        parentId: "n865td2",
        postId: "1mmzjrc",
    },
    {
        id: "n81q0qj",
        body: "I found plastic stencils worked OK for solder masking\n\nCnc milling didn’t work well for me, copper tore out .\n\nLaser printer method was bad. \n\nI tried painting boards and laser ablating off the paint but too much paint was left behind. \n\nNever attempted resin resist but that seems the most promising. ",
        parentId: null,
        postId: "1mmzjrc",
    },
    {
        id: "n820bg0",
        body: "The least expensive &amp; easiest way to make usable SMD boards is pretty much exactly how professional board makers do it, but scaled down. Chemical etching &amp; plating.",
        parentId: null,
        postId: "1mmzjrc",
    },
    {
        id: "n83f8fc",
        body: "I haven´t tried yet, but some are suggesting that the new low-cost (20-60w) fibre laser engravers can remove thin copper layers.  Resolution isn´t bad, and it's vector engraving rather than just pixels.  Doesn't solve the via problem, and only for 2 layer boards.",
        parentId: null,
        postId: "1mmzjrc",
    },
    {
        id: "n851ia4",
        body: "Did a project some time back to use low cost 3D printer (Creality ender 3 under $300) and attached silver ink ball point pen (circuit scribe conductive ink pen about $9 amazon) to make PCB’s, not a metel printer.\n\n[https:\/\/www.thingiverse.com\/thing:6309641](https:\/\/www.thingiverse.com\/thing:6309641)\n\nmaking multilayers was just a matter of making the printed non-conductive layers thin enough so that the ink pen could stair-step up and down from one layer to the next. The process involved making an STL file of the board outline with angled holes for via locations. The trick to getting it to work had to do with getting a ball point pen to write on plastic. The Cura script program I used took the layout Gerber file and translated it into spiral patterns that were inserted between the printers G-code layers. I did not have to solder any of the parts because I could put the components directly on the wet conductive ink.\n\nThe traces needed to be wider then copper to make up for the difference in resistance, but for making several iterations of my low voltage prototypes it seemed to work just fine. one roll of PETG should last depending on the complexity of the board, and each pen provided about 11 hours of write time, so initial cost is higher than ordering one PCB, but if you are making several different boards with minor changes the saving add up quickly,.",
        parentId: null,
        postId: "1mmzjrc",
    },
    {
        id: "n876phj",
        body: "I've tried many different options over more than 30 years. I've got a $4000 desktop CNC milling machine that I justified buying because I was going to use it for quick-turn prototypes. I've probably made fewer than a dozen boards with it. Leveling and calibrating it to the degree required to isolation-route fine traces is a non-trivial task, and tool wear still needs careful monitoring once you've got that dialed in.\n\nFabricating double-sided boards at home just sucks. You've got to get the layer registration right and vias are a pain. 20 years ago, when getting cheap PCBs fabricated involved emailing Gerber files to Bulgaria and waiting 4-6 weeks it was easier to justify the effort. Today, there are very few times when I'm going to attempt to do that in house.\n\nThe high-quality, super-easy, low-cost PCB suppliers out there today mean that it's just not worth the trouble. That $500 you're willing to spend will buy a lot of boards with a 1-week turnaround. When prototyping speed is an issue, plan ahead and make breakout boards for your common stuff.\n\nThese days the only PCBs I'll make myself are large and very simple boards. A prime example would be the slip ring contacts I made for powering the LEDs on [this thing](https:\/\/youtu.be\/hLVJLGRYo2I). Those boards are just concentric rings, no components at all, and I did the CNC programming by hand.\n\nI've done embedded development independently for over 20 years now and I will not buy any more equipment with messy chemicals, supplies with limited shelf lives, or high maintenance requirements if I can possibly avoid it. I've got a $6000 UV flatbed printer that has probably saved me $1000 in enclosure printing costs over the last few years and it needs attention constantly - I've got to replace a head on it this weekend and replace the ink delivery tubing again.\n\nUnless you have a specific, frequent, and ongoing need for quick-turn, meh-quality prototypes, just don't. Find a PCB house that fits your budget and time needs and learn to plan your workflow around that lead time.",
        parentId: null,
        postId: "1mmzjrc",
    },
    {
        id: "n81beem",
        body: "Pcbway is like 5 bucks for a 2 layer and you get in a week.",
        parentId: null,
        postId: "1mmzjrc",
    },
    {
        id: "n81ejts",
        body: "Thanks for the reply. When you add shipping and stencil, it's 30-60 USD per 5. I'm looking for a method that is faster and cheaper in the long run (or at least costs the same).",
        parentId: "n81beem",
        postId: "1mmzjrc",
    },
    {
        id: "n81hk52",
        body: "Issue with fabricating your own boards is \n\n- that they don't have a solder mask which makes stenciling  impossible \n- you have to fill vias manually which would be very time consuming\n- no silkscreen \n- lots of manual circuit tests for shorts and open\n\nIt just seems like a lot of work for minimal gain. I can understand if you're designing antennas or planar transformers.\n\nI wanted to add that I appreciate your ingenuity and looking  outside of the box. This is what truly makes a good engineer.",
        parentId: "n81ejts",
        postId: "1mmzjrc",
    },
    {
        id: "n85ubi9",
        body: "There is no at home method that is faster or cheaper.\n\n\n Once you factor in your time, consumables, cutting pcb to size, drilling all the different sized holes, dealing with pcb drill dust,  buying pcb drill bits, soldering in loads of vias, messing around making a special layout to minimise vias or use jumpers to avoid going to 4 layer design, struggling with soldering because you've got unplaced copper pads with no solder resist, fixing assembly errors because  you've got no silkscreen on the board etc etc...  \n\n\nIt can be more immediate if you really want fast turnaround. But it's not quicker. I ordered some boards from PCBWay last week, it took less than 15 mins start to finish. Now I work on some other project while I wait for them to arrive. Making them myself would have taken a whole day realistically. ",
        parentId: "n81ejts",
        postId: "1mmzjrc",
    },
    {
        id: "n81g10k",
        body: "Are these diy boards for prototyping or production ??\n\nMost of your suggestions leverage additive construction. Getting started with any of these will cost more then $500. Unless you are looking to make small PCBs. Less then 150mm x 150mm.\n\nCNC is removing copper from the copper clad boards. Chemical etching also removes copper. These would be the easiest to get started and are very accurate if your willing to do the work to make them work.\n\nI am not clear what stencil you area talking about for CNC.\n\nGood Luck",
        parentId: null,
        postId: "1mmzjrc",
    },
    {
        id: "n82amnk",
        body: "Laser Cutter",
        parentId: null,
        postId: "1mmzjrc",
    },
    {
        id: "n83izdu",
        body: "JLPCB is my PCB god!",
        parentId: null,
        postId: "1mmzjrc",
    },
    {
        id: "n83k846",
        body: "Usually good to be able to spell your god's name.",
        parentId: "n83izdu",
        postId: "1mmzjrc",
    },
    {
        id: "n83kmyx",
        body: "JLCPCB - sorry for that......",
        parentId: "n83k846",
        postId: "1mmzjrc",
    },
    {
        id: "n83p082",
        body: "get a laminator and a laser printer. you can't beat that in price, quality and convenience. Not every laminator is good enough, FGK-120 is known to work well.\n\nvias and through holes are still going to be a pain.",
        parentId: null,
        postId: "1mmzjrc",
    },
    {
        id: "n8iicch",
        body: "If you can wait for a week or so, it's a no brainer to get them made from jlcpcb\/pcbway. You're never going to get that kind of quality for that price at home (&lt;20$ for 5 boards).",
        parentId: null,
        postId: "1mmzjrc",
    },
    {
        id: "n80ietr",
        body: "did u probe initial conditions before the first lvgl draws on the lines leading to display to see if theres differences between startup vs reset\n\nidk what teh code looks like but maybe its failing on some init after reset. hard to say without looking at what the codes actually doing.\n\nmaybe just try a UI re-init after some period of time depending on RESET REASON as a quick &amp; dirty debug if all failure cases on initial calls are all passing.",
        parentId: null,
        postId: "1mmvltd",
    },
    {
        id: "n8061g7",
        body: "Everything at [mu.microchip.com](http:\/\/mu.microchip.com)",
        parentId: null,
        postId: "1mmtmdy",
    },
    {
        id: "n80d9vh",
        body: "Thanks for posting this",
        parentId: "n8061g7",
        postId: "1mmtmdy",
    },
    {
        id: "n89px4f",
        body: "How come I've never heard of this before. Amazing, thanks!",
        parentId: "n8061g7",
        postId: "1mmtmdy",
    },
    {
        id: "iue2306",
        body: "Add jumpers to disconnect them from the Circuit when you want to reprogram.",
        parentId: null,
        postId: "yhhnqw",
    },
    {
        id: "iuemqk5",
        body: "I see. Jumper connections will work just fine. Are there any particular types of switches that I could use to break the connection on and one swoop? Dip switches come to mind, once that join all the pins with one bar",
        parentId: "iue2306",
        postId: "yhhnqw",
    },
    {
        id: "iuf2io7",
        body: "If you're making a PCB then you might want to use jumpers: a couple of 2-pin headers with shunts.",
        parentId: "iuemqk5",
        postId: "yhhnqw",
    },
    {
        id: "iwqp7gm",
        body: "Alternatively, use inline resistors to your application interface\/IO filters to have a hands off approach. I use 1K inline resistors to a UART with the debug header being on the PIC side of the resistors. No fiddling with jumpers and I have either UART or DEBUG\/PROG whenever I need them.\n\nSame method works for IO, though I would bump up the resistance if the application allows. Haven't tested with analog (such as trim pots for timer adjustment) but I expect that should work as well though you may have to adjust sampling time.",
        parentId: "iuf2io7",
        postId: "yhhnqw",
    },
    {
        id: "iuedax5",
        body: "As long as you don’t have low impedance or high capacitance connected to those pins you can still use them for programming. Same applies to MCLR pin.",
        parentId: null,
        postId: "yhhnqw",
    },
    {
        id: "j5tc23h",
        body: "I tend to use the programmer pins as things which are outputs from the PIC that won't hurt anything if they flail about during programming (IE, use for indicator LEDs, or logic outputs for thing that don't matter when the board is being programmed). Otherwise I'll just make sure there's a resistor in series if the pin MUST be an input something as low as 470R will work with a pickit4.",
        parentId: null,
        postId: "yhhnqw",
    },
    {
        id: "jlyh2m0",
        body: "This is the best solution.",
        parentId: "j5tc23h",
        postId: "yhhnqw",
    },
    {
        id: "iueqkwq",
        body: "I try not to use them, if you do you cannot in circuit debug.",
        parentId: null,
        postId: "yhhnqw",
    },
    {
        id: "n83mwt5",
        body: "God bless you my guy!",
        parentId: "n8061g7",
        postId: "1mmtmdy",
    },
    {
        id: "n80iymi",
        body: "Zephyr RTOS is show up more and more in job descriptions. Biggest change I’ve seen over the last five years.",
        parentId: null,
        postId: "1mmtmdy",
    },
    {
        id: "n83cgcp",
        body: "Yes this one. 2-3 years ago FreeRTOS seemed to be enough. Even I have noticed increase in Zypher and VxWorks mentions in JD.",
        parentId: "n80iymi",
        postId: "1mmtmdy",
    },
    {
        id: "n83j8bo",
        body: "Currently interning in an embedded systems role. We just tried zephyr the other day and love it compared to freeRTOS",
        parentId: "n83cgcp",
        postId: "1mmtmdy",
    },
    {
        id: "n83mpy3",
        body: "Can you share your experience? It has been on my todo for years now, but haven't got time lately",
        parentId: "n83j8bo",
        postId: "1mmtmdy",
    },
    {
        id: "n83o6f8",
        body: "I really like it so far. I used it to get Ethernet running on a Same54 xplained pro board (We were having an abundance of issues with microchips example code) and we were able to get it working within minutes. Hal code is already built for you and pin assignments are already done (for supported boards). You just specify which board you are building for when you build your project. You can also apparently simulate hardware if you don’t have any on you, or if you want to do something like a pipeline (I haven’t tried this yet, but saw it in the docs). Again I’m just an intern and have only been messing with it for a week, but it seems to be worth it. It doesn’t take long to set up at all either.",
        parentId: "n83mpy3",
        postId: "1mmtmdy",
    },
    {
        id: "n81vlwk",
        body: "One big one that I've been investigating is asynchronous programming. Specifically coroutines in C++ but other languages have their methods of doing it. It's an extremely powerful tool for getting the most out of your CPU.",
        parentId: null,
        postId: "1mmtmdy",
    },
    {
        id: "n83gosn",
        body: "Cooperative multitasking with state machines isn't new. I would like to employ C++20 coroutines for some use cases but, honestly, they seem overly complicated. I fear I would replace boiler plate that I completely understand and can debug with boiler plate in a black box library that I don't and\/or can't.",
        parentId: "n81vlwk",
        postId: "1mmtmdy",
    },
    {
        id: "n83ltdd",
        body: "Absolutely not new. But many languages are adding built in support for such things which allows you to build more portable code. Which is something I'm highly interested in. Zig recently added support for async support C++ coros are quite complicated, but luckily we got std::generator and std::task in for C++26.  The benefit I've found is that it allows developers using my libraries and drivers to pick and choose what scheme they want to follow. Thread pool? You bet! Event loop! Easily done. Cooperative multitasking, easy. Basically I \"should\" can easily swap which scheme I have using the coro scheme from C++ and require no code rewrite for the libraries. I simply have to schedule them the way I'd like. Sounds and is complicated when done from scratch. Once you have the coro system up, with the flexibility you'd prefer, you can do some great things. When I've gotten my code in a decent place I plan to share it here.",
        parentId: "n83gosn",
        postId: "1mmtmdy",
    },
    {
        id: "n80vroa",
        body: "As a Power Electronics \/ Embedded engineer, I have a hate\/love relationship with digital control of switching power supply regulation and compensation loops.\n\n[https:\/\/www.st.com\/resource\/en\/application\\_note\/an5497-buck-current-mode-with-the-bg474edpow1-discovery-kit-stmicroelectronics.pdf](https:\/\/www.st.com\/resource\/en\/application_note\/an5497-buck-current-mode-with-the-bg474edpow1-discovery-kit-stmicroelectronics.pdf)",
        parentId: null,
        postId: "1mmtmdy",
    },
    {
        id: "n835sd1",
        body: "What makes implementing dc converter with an mcu appealing? There are countless ic that do the job just fine.",
        parentId: "n80vroa",
        postId: "1mmtmdy",
    },
    {
        id: "n848bb9",
        body: "About the PFC part, I found two Infineon ic that can control totem-pole PFC code-less:\n\n1. IMC102TF048XUMA1\n2. IMD112T6F040XUMA1\n\nThere is also Onsemi NCP1681, which is for fully bridgeless PFC.\n\nAlso thanks for your link. I've taken a look, and from first impression the IBC topology looks similar to 4-switch buck-boost converter? I have no experience with IBC so I have no comment on its efficacy, but as a pcb hobbyist I think spending time to write firmware for a voltage converter just makes no sense, especially since for many board power conversion isn't the main purpose. I'd definitely consider it worthwhile to design a bespoke solution if power conversion is THE product though, i.e solar inverter",
        parentId: "n842m7m",
        postId: "1mmtmdy",
    },
    {
        id: "n82bzz0",
        body: "As far as skills to learn, communication protocols like I2C, SPI, QSPI, CAN, USART, AXI so your system can talk to peripherals, in particular talk to sensors of different kinds.",
        parentId: null,
        postId: "1mmtmdy",
    },
    {
        id: "n806bvb",
        body: "SIMD.  Vector Processing.  Offloading to ASICs.",
        parentId: null,
        postId: "1mmtmdy",
    },
    {
        id: "n872esr",
        body: "Embedded Rust, specifically with Embassy Embed and Tokio.",
        parentId: null,
        postId: "1mmtmdy",
    },
    {
        id: "n836ruo",
        body: "On the programming language side, C++ may be relevant to you in the short term, and Rust in the long term (but maybe not, depends on the evolution of your sector, the market, regulations, if something shinier appears in the next few years, ...)\n\n* https:\/\/doc.rust-lang.org\/book\/\n* https:\/\/docs.rust-embedded.org\/book\/",
        parentId: null,
        postId: "1mmtmdy",
    },
    {
        id: "n802un6",
        body: "DSP, Cryptography, CV and ML\n\npick one and learn fundamentals, then continue learning that topic alongside the C\/Cpp.\n\nAlso you will require HW knowledge to optimize your model\/sw\/idea.",
        parentId: null,
        postId: "1mmtmdy",
    },
    {
        id: "n80zfvo",
        body: "There’s literally thousands of topics.\n\nAsk yourself What do you want to be doing in 5 years? What parts of your job do you like? Which ones you don’t enjoy?",
        parentId: null,
        postId: "1mmtmdy",
    },
    {
        id: "n832k9b",
        body: 'Central Compute vs. Edge Processing is a big topic right now.\n\nWith powerful SoCs and cheap high-speed interfaces you can integrate more and more at a central SoC in the system and keep the "Edges" more minimal.\n\nIn the end it is a lot about cost. Why spend money on 5 expensive mixed-signal MCUs, when you can directly stream e.g. sensor data to 1 SoC. As MCUs are often a big cost adder to an Embedded System, the 1-SoC system can be cheaper (generally speaking, might differ from project to project)',
        parentId: null,
        postId: "1mmtmdy",
    },
    {
        id: "n81ywt4",
        body: "edgeML stuff is slowly taking off. TrustZone is getting used more and more for more secure embedded projects. I've been playing with some chips with RF harvesting in ultra low power environments. I still think mesh has a long way to go. \n\n  \nNot necessarily new stuff, but I think that even though some of the \"humanoid\" robots coming aren't likely to generate serious value soon, AI is starting a new wave of companies trying to make specialized robots. So likely a lot more embedded work in robotics soon. This is where I think edgeML and classical controls theory will be super useful.",
        parentId: null,
        postId: "1mmtmdy",
    },
    {
        id: "n82tppy",
        body: "For me it’s been edge computing and tiny ML",
        parentId: null,
        postId: "1mmtmdy",
    },
    {
        id: "n83slbd",
        body: "RemindMe! 100 Hours",
        parentId: null,
        postId: "1mmtmdy",
    },
    {
        id: "n83xgcz",
        body: "If embedded as an hobby, what knowledge should be enough? \n\nCurrently i know only C, Finite State Machine, Cooperative Scheduler and i have no clue about FreeRTOS, in order to sovled for my pet project, im looked forward to new concepts like Active Object Pattern, Event-Driven Programing from Quantum Leaps, but somehow i think this is too overwhelm.\n\nAny suggestions for me please?",
        parentId: null,
        postId: "1mmtmdy",
    },
    {
        id: "n85nji3",
        body: "Tech stack - Rust, Zephyr. Field of expertise - crybersecurity and in my humble opinion reproducible developement (eg. via Nix)",
        parentId: null,
        postId: "1mmtmdy",
    },
    {
        id: "n827ozi",
        body: "RemindMe! Tomorrow",
        score: -1,
        parentId: null,
        postId: "1mmtmdy",
    },
    {
        id: "n7zb9u7",
        body: "My idle task always toggles an unused pin.  Allows me to measure idle time with an oscilloscope anytime I want.",
        parentId: null,
        postId: "1mmpf54",
    },
    {
        id: "n88g80s",
        body: "Have you checked to see what voltages you're getting with the pins on and off? An oscilloscope would be best to see what's happening while it's running, but you can do a sanity check with a voltmeter.",
        parentId: null,
        postId: "1mnyoq5",
    },
    {
        id: "n88j6kd",
        body: "Yup so this helped a lot, turns out my electronics wasn't the issue... my step delay wasn't high enough so it was sending pules wayyyyy to fast but it's fixed now in code.\n\nThanks a lot!!!",
        parentId: "n88g80s",
        postId: "1mnyoq5",
    },
    {
        id: "n88ubb9",
        body: "Update to this too, I can run it off 3.3v power and logic so all my issues was just timing.",
        parentId: "n88j6kd",
        postId: "1mnyoq5",
    },
    {
        id: "n88klng",
        body: "Why do 5v and 12v have capacitors in series? Shouldn't 5v go directly to the VDD pin and C1 be connected to 5V and GND? Same with 12v, VMOT and C2.",
        parentId: null,
        postId: "1mnyoq5",
    },
    {
        id: "n88naxi",
        body: "Yeah no 100%, that was a mess up on my part with the schematic because I was rushing. I do the decoupling capacitors going to ground not inline",
        parentId: "n88klng",
        postId: "1mnyoq5",
    },
    {
        id: "n88n2ya",
        body: "You need to look at the data sheets to determine the guaranteed input high level for the 5 volt part. Usually a CMOS 3.3V output will be sufficient to meet 5 volt guaranteed high input but there are exceptions and you might need to use a converter. You don't need to use open drain and in fact if you are using open drain with a pull up to five volts you could damage your 3.3 volt part. The outputs probably won't swing more than 3.3 volts because of input protection. Just drive it directly with push pull 3.3.",
        parentId: null,
        postId: "1mnyoq5",
    },
    {
        id: "n8ab5jj",
        body: "There are voltage translator ICs, just connect it on not sides and it should do the job for you. This is the most reliable way. There is also the possibility to use MOSFET level translator which is very popular with Arduino or just to pull up the pins but that may not be very reliable at high speed.",
        parentId: null,
        postId: "1mnyoq5",
    },
    {
        id: "n88gcfw",
        body: "[tutorial for your chip](https:\/\/www.makerguides.com\/a4988-stepper-motor-driver-arduino-tutorial\/)\n\n&gt; The EN (enable) pin can be left disconnected, it is pulled low by default. When this pin is set high the driver is disabled.\n\nIts a ~Enable line, so you don't need to bring it to 5V",
        parentId: null,
        postId: "1mnyoq5",
    },
    {
        id: "n88gj91",
        body: "I have tried it with leaving enable floating as well.",
        parentId: "n88gcfw",
        postId: "1mnyoq5",
    },
    {
        id: "n88gpc6",
        body: "Have you tried setting it to ground?",
        parentId: "n88gj91",
        postId: "1mnyoq5",
    },
    {
        id: "n88hxfi",
        body: "Yes I have, I can get it to work manually by just touching the step and direction pins to 5v i just can't get it to work with my MCU.",
        parentId: "n88gpc6",
        postId: "1mnyoq5",
    },
    {
        id: "n88gcxc",
        body: "Sounds like it’s wrong.",
        parentId: null,
        postId: "1mnyoq5",
    },
    {
        id: "n8b4pdn",
        body: "A4988 can be directly powered with 3.3V on the VDD side - no need to level translate anything, but RESET definitely needs to be pulled up to VDD for anything to work reliably. Do not connect it together with SLEEP, just pull them both high or connect to the MCU to drive them with a pair of outputs. MSx are fine to be floating as they have internal pulldowns.",
        parentId: null,
        postId: "1mnyoq5",
    },
    {
        id: "n88gvde",
        body: "needs same power supply as logic. Your using 5v so 5v is expected change vdd to 3.3v. Pretty sure it is this, tell me if it worked.",
        parentId: null,
        postId: "1mnyoq5",
    },
    {
        id: "n88hqta",
        body: "I tried using 3.3v on vdd as well but when i was manually setting things high and low to set the current limits it would only work with 5v.",
        parentId: "n88gvde",
        postId: "1mnyoq5",
    },
    {
        id: "n88iri6",
        body: "You need to tie the step and direction pins to 5V through a series resistor each, and use the open-drain pins to pull it low to send the pulses.",
        parentId: "n88hqta",
        postId: "1mnyoq5",
    },
    {
        id: "n88hyeu",
        body: "It because stepp and dir pin need 3.3v aswell",
        parentId: "n88hqta",
        postId: "1mnyoq5",
    },
    {
        id: "n8b6vrp",
        body: "You could also track stuff without GPS. Just log the SSIDs and make a map out of it. Just an idea I had.",
        parentId: "n8a88mg",
        postId: "1mnrma7",
    },
    {
        id: "n8fxelr",
        body: "I made a bluetooth version of this (just the software not hardware), was going to move on to the wifi version but never got round to it. \n\nI never really finished it though because getting it to work as i intended would require basically scanning everywhere which obviously isnt practical, unless you create some user-driven positive feedback loop to grow the network e.g make users scan -&gt; more users -&gt; more scans -&gt; better maps\n\nBut i think offline networks like this are going to become very sort after.",
        parentId: "n8b6vrp",
        postId: "1mnrma7",
    },
    {
        id: "n8ai67p",
        body: "could be vulnerability testing",
        parentId: "n8a88mg",
        postId: "1mnrma7",
    },
    {
        id: "n87p76x",
        body: "Haven't looked at the code yet; did PlatformIO also have a limitation on the number of SSIDs returned? ESP-IDF did last time I looked.",
        parentId: null,
        postId: "1mnrma7",
    },
    {
        id: "n88nj93",
        body: "What limit does ESP-IDF have? It's memory limited, but I've pulled over 100 records. I think it's really limited by the antenna and noise floor more than anything.\n \nEdit: I think it's limited to a UINT16, so 65k... Again, I think the limit is \"just\" memory, and you normally put wifi on internal for performance.\nhttps:\/\/docs.espressif.com\/projects\/esp-idf\/en\/stable\/esp32\/api-reference\/network\/esp_wifi.html#_CPPv428esp_wifi_scan_get_ap_recordsP8uint16_tP16wifi_ap_record_t",
        parentId: "n87p76x",
        postId: "1mnrma7",
    },
    {
        id: "n8dvzrf",
        body: "How does sqlite work without an operating system?",
        parentId: null,
        postId: "1mnrma7",
    },
    {
        id: "n8e7neh",
        body: "Depending on what features you plan on using, [it doesn't really need much](https:\/\/www.sqlite.org\/custombuild.html).",
        parentId: "n8dvzrf",
        postId: "1mnrma7",
    },
    {
        id: "n8e45a2",
        body: "Why do you initialize I2S?  Is that leftover code from something else?",
        parentId: null,
        postId: "1mnrma7",
    },
    {
        id: "n8fmrqz",
        body: "I guess that’s for the micro SD card module on the ESP32-S3. Should check if we actually need it.",
        parentId: "n8e45a2",
        postId: "1mnrma7",
    },
    {
        id: "n845vaq",
        body: "[https:\/\/en.wikipedia.org\/wiki\/USB\\_human\\_interface\\_device\\_class](https:\/\/en.wikipedia.org\/wiki\/USB_human_interface_device_class)",
        parentId: null,
        postId: "1mneah6",
    },
    {
        id: "n846yn2",
        body: 'The PC keyboard (going back to the original IBM PC) has never had a fully simplex communication protocol.  There has always been a way to "turn around" the interface to send commands to the keyboard for controlling things like the status LEDs.  However, that interface was not "full duplex" since data could only be sent in one direction at a time.\n\nModern USB keyboards are actually the same in that conventional USB (prior to SuperSpeed) only has a single data line (it\'s a differential pair but both wires are used at the same time to transmit the same data), and the interface can be used in either direction.  That is, like the old-school PC keyboard interface, USB is "duplex" but not "full duplex".\n\nSome people like to think of keyboard interfaces as fully simplex because data mostly only goes in one direction, and the use of data in the other direction is for ancillary purposes not strictly related to the keyboard\'s function.  Indeed you don\'t HAVE to ever send data to a conventional PC keyboard (USB is more complicated), but in practice all PC environments do and always have.',
        parentId: null,
        postId: "1mneah6",
    },
    {
        id: "n807jp8",
        body: "Can you not read or what is going on?\n\nAnd as long as you are targeting adequate hardware from the pre-PnP era, it is no different than programming for any modern microcontroller or microprocessor. On modern post-PnP architectures, you'll at least get some help from the BIOS by virtue of emulating basic PnP devices as if they were native - for example USB keyboard and mouse supporting USB boot protocol as PS\/2 devices.",
        score: -1,
        parentId: "n800k3j",
        postId: "1mmpf54",
    },
    {
        id: "n7zjzzt",
        body: "Heyyo! So look for the UART, the boot mode pin and the reset pin. \n\nTo get an ESP32 into flashing mode you gotta assert the boot mode pin right as you assert the reset pin. There might be a dance in there too. Then it goes into flashing mode.\n\nNext part you connect the UART pins to a ftdi connector. Then if you have the esp-idf or even arduino with esp32 support you can call flash and it should be able to. \n\nThere’s circuits that wire up the ctr and rts UART flow control pins on your ftdi to the rst boot mode pins to trigger them the right way. Maybe make the circuit externally and wire it up? That way you have automatic flashing.  Google esp32 flow control reset circuit.\n\nAlso be prepared that depending on who made or designed that chip, it could be locked down. Most manufacturers of products like to lock down their chips so people can’t flash code on them as it’s a semi liability(might be able to access our cloud keys for example). So you might not even be able to use the esp32 at all",
        parentId: null,
        postId: "1mmnbep",
    },
    {
        id: "n7yxxlk",
        body: "Since its ESP32, why dont you check with ESPIDF the official esp toolkit, if its software related issue that might fix it.",
        parentId: null,
        postId: "1mmnbep",
    },
    {
        id: "n81sdo1",
        body: "Looking at the pictures there appears to be a button labeled B in the top RH corner, this may be a boot mode button that would allow you to flash it, you could try holding this button down while applying power and seeing if it appears in the Arduino IDE. \n\nIf you do get it to appear make sure you enable USB CDC in the Arduino IDE before flashing anything otherwise you will have a bit of an adventure recovering depending how it is designed.",
        parentId: null,
        postId: "1mmnbep",
    },
    {
        id: "n7xzuey",
        body: "https:\/\/www.freertos.org\/Documentation\/01-FreeRTOS-quick-start\/01-Beginners-guide\/01-RTOS-fundamentals\n\nhttps:\/\/www.freertos.org\/Documentation\/02-Kernel\/07-Books-and-manual\/01-RTOS_book\n\nThe best resource for FreeRTOS fundementals.",
        parentId: null,
        postId: "1mmiy3h",
    },
    {
        id: "n7y0wny",
        body: "Additionally, before create a post like yours please check old posts about FreeRTOS. Probably the others created a post like this and shared the same links and books , too:)",
        parentId: "n7xzuey",
        postId: "1mmiy3h",
    },
    {
        id: "n84i53i",
        body: "I sure will",
        parentId: "n7y0wny",
        postId: "1mmiy3h",
    },
    {
        id: "n7zeht2",
        body: "This!  I literally just read through their book a year ago while getting started. Its online and free on their website. Give that a try! Remember to read it couple of times. It took me 3 try to get good understanding of it. Best of luck!",
        parentId: "n7xzuey",
        postId: "1mmiy3h",
    },
    {
        id: "n83ci52",
        body: "Best are the official documents and its source codes",
        parentId: "n7zeht2",
        postId: "1mmiy3h",
    },
    {
        id: "n84i3tj",
        body: "Thanks a lot, mate!",
        parentId: "n7xzuey",
        postId: "1mmiy3h",
    },
    {
        id: "n7y2pl0",
        body: "https:\/\/youtube.com\/playlist?list=PLEBQazB0HUyQ4hAPU1cJED6t3DU0h34bz\nrecently found this series by Digi-Key, easy to digest too",
        parentId: null,
        postId: "1mmiy3h",
    },
    {
        id: "n84iepn",
        body: "Yeah, I found this too. But he's coding in Arduino IDE. Doesn't really make any difference but I'd prefer doing it in STM32CubeIDE",
        parentId: "n7y2pl0",
        postId: "1mmiy3h",
    },
    {
        id: "n7ytg1f",
        body: "Came across this but didn't watch it fully but agree.  Need to learn what an RTOS and the theory before just jumping in.",
        parentId: "n7y2pl0",
        postId: "1mmiy3h",
    },
    {
        id: "n7xzob6",
        body: "If you enjoy reading, freertos has free book online",
        parentId: null,
        postId: "1mmiy3h",
    },
    {
        id: "n7y09oh",
        body: "I think, All developers must enjoy reading :)",
        parentId: "n7xzob6",
        postId: "1mmiy3h",
    },
    {
        id: "n84ii5q",
        body: "Sure. I'll check it out",
        parentId: "n7xzob6",
        postId: "1mmiy3h",
    },
    {
        id: "n7z455n",
        body: "Go to the ST channel on youtube and find MOOC about freertos. It's free and the best I've found so far",
        parentId: null,
        postId: "1mmiy3h",
    },
    {
        id: "n84l3x5",
        body: "Will check that out. But i presume that they've implement CMSIS library. I don't want to do that. I want pure FreeRTOS",
        parentId: "n7z455n",
        postId: "1mmiy3h",
    },
    {
        id: "n7yaa45",
        body: "What's your guys opinion on threadx? I find it much easier to implement, has more features and is easier to compile as compared to FreeRtos.",
        parentId: null,
        postId: "1mmiy3h",
    },
    {
        id: "n7yu3rv",
        body: "1) personally prefer it over freeRTOS because of the certification (ASIL D), ecosystem (NetXDuo, USBX, FileX etc.) and api naming\n2) ThreadX has a queue element size limitation which bit me in the ass today. Each queue element can only be as long as 16 words (you can modify the source code to get over this + logically makes sense as to why, but it's a limitation nonetheless) - also, I like having explicitly binary semaphores, which from my understanding ThreadX doesn't have, so onus is on the developer to ensure the binary aspect of it\n3) not too sure about its longevity now, seems like development is pretty slow\/stagnant since the Azure\/Eclipse transition. Honestly not sure if that's normal, or worrying, but found it noteworthy at the least. Also now that it is FOSS, I wonder if they'll still be aiming for ASIL D conformance and certification",
        parentId: "n7yaa45",
        postId: "1mmiy3h",
    },
    {
        id: "n7zafm2",
        body: "What kind of project are you working on?",
        parentId: "n7yu3rv",
        postId: "1mmiy3h",
    },
    {
        id: "n823mz2",
        body: "Currently working on an ev charge controller, so that's where the ASIL expectation comes in. There's networking involved in the project, so the NetXDuo integration comes in handy. Was trying to set up RX and TX buffers for ethernet packets yesterday when I realised the queue limitation and had to work around it",
        parentId: "n7zafm2",
        postId: "1mmiy3h",
    },
    {
        id: "n7ygize",
        body: "Udemy has a good course if you prefer video tutorials",
        parentId: null,
        postId: "1mmiy3h",
    },
    {
        id: "n7zrcaw",
        body: 'Look at the "Modern Embedded Systems Programming Course" by Miro Samek on YouTube. The IDE and tools he uses are a bit dated but the OS concepts are gold.',
        parentId: null,
        postId: "1mmiy3h",
    },
    {
        id: "n84lju1",
        body: "Will check that out :)",
        parentId: "n7zrcaw",
        postId: "1mmiy3h",
    },
    {
        id: "n811t4v",
        body: "It is not dedicated but on my Youtube Getting Started channel there are at least 3-4 videos diving into FreeRTOS.\n\n[https:\/\/youtu.be\/3Kevk3l6vPs](https:\/\/youtu.be\/3Kevk3l6vPs)   \n[https:\/\/youtu.be\/zY\\_I6GZffos](https:\/\/youtu.be\/zY_I6GZffos)   \n[https:\/\/youtu.be\/i9j63SeN1H8](https:\/\/youtu.be\/i9j63SeN1H8) \n\nThey should cover the basics including how to gather statistics.",
        parentId: null,
        postId: "1mmiy3h",
    },
    {
        id: "n84ltxf",
        body: "That's great",
        parentId: "n811t4v",
        postId: "1mmiy3h",
    },
    {
        id: "n81rg1b",
        body: "The best documentation on FreeRTOS I've found is ST Micro's own YouTube Playlist:\n\n[https:\/\/www.youtube.com\/playlist?list=PLnMKNibPkDnExrAsDpjjF1PsvtoAIBquX](https:\/\/www.youtube.com\/playlist?list=PLnMKNibPkDnExrAsDpjjF1PsvtoAIBquX)",
        parentId: null,
        postId: "1mmiy3h",
    },
    {
        id: "n89141r",
        body: "The freertos demo code is quite useful. Start with flashing a led using a couple of tasks, then try a few different intertask communications, then play around with task priorities.",
        parentId: null,
        postId: "1mmiy3h",
    },
    {
        id: "n7yzado",
        body: "Checkout Fastbit academy in Udemy, its great for starting on Embedded, there is a course on Freertos with SMT32 there,  its paid😅 course, but its worth it if you can purchase it under 500rs.",
        parentId: null,
        postId: "1mmiy3h",
    },
    {
        id: "n84lfjr",
        body: "Yeah that's where i began my embedded journey. They're great. But have they used the STM32CubeIDE's GUI in that course or proper bare metal coding like initial courses?",
        parentId: "n7yzado",
        postId: "1mmiy3h",
    },
    {
        id: "n7xgl0i",
        body: "I am a complete beginner. Infact I havent started yet. I reaceantly had to get involved due to some work. And I am hooked now. I took esp32 instead of nrf51822 for ble for work. I am not offering anything jsut saying I am on the same path. Hihi!",
        parentId: null,
        postId: "1mm4h6a",
    },
    {
        id: "n86rfcq",
        body: "I find nordic to be very nice for learning on.\n\nAnd CLion is free for personal use, so go with that for your IDE.\n\nAs far as your target application, I would just stick with micropython. Once you dip your toes into embedded C, you are exposed to linker scripts and make systems (CMake), and it just gets really nasty.",
        parentId: null,
        postId: "1mm4h6a",
    },
    {
        id: "n7vtlk9",
        body: "Use vs code\n\nIt really depends on what you want to do. But I think Nordic can have a steeper learning curve, especially if you're going to use zephyr",
        parentId: null,
        postId: "1mm4h6a",
    },
    {
        id: "n8f1ftt",
        body: "Using the ground?",
        parentId: "n891tms",
        postId: "1mo0pbn",
    },
    {
        id: "n88svdq",
        body: "The firmware in the other MCUs may be bad, or you are not waiting for each reply to complete before polling the next device. Use an oscilloscope to look at the data on the RS-485 bus.",
        parentId: null,
        postId: "1mo0pbn",
    },
    {
        id: "n7vpjpq",
        body: "I just want you to know that programming a microcontroller and designing a PCB are really quite different skills.\n\nBut anyways, pick whatever development board you think looks least toxic if you melt it, and write the code in powerpoint or minecraft signs or whatever, it really doesn't matter much in the end. The only thing that matters is that you do something and get the practice.",
        parentId: null,
        postId: "1mm4h6a",
    },
    {
        id: "n7vwws7",
        body: "The stm32 ide is the simplest way to go. But I prefer vscode",
        parentId: null,
        postId: "1mm4h6a",
    },
    {
        id: "n89fvir",
        body: "Both are actually pretty good, once you get the hang of it. \n\nVS Code is pretty popular by people that (rightfully) dislike (at least the earlier versions of) Arduino IDE. It's lightweight, versatile and an active community around it keeps it up to date and supplied with tooling and compilers for new chips. \n\nSTM32CubeIDE is indispensable when you're working with custom boards where you are fully in control of (and responsible for) pin-assignments marking which pin does what. The IDE has a GUI interface where you can basically wire the (first selected) chip, marking what pin is an input \/ output for what (labels, which are named in generated code) and even what protocols are supposed to be initialised \/ supported on them. \n\nGenerated code is typically something that gets sh\\*\\* on, but honestly, the CubeIDE generator does a relatively good job on it and puts it all in a directory séparate from your own code. HAL libraries are.... Something to get used to and have varying levels of quality, but ultimately either save time, or gives some insight in what is at least suppósed to happen.",
        parentId: "n7vwws7",
        postId: "1mm4h6a",
    },
    {
        id: "n8aocal",
        body: "But you don't need the IDE to generate the boiler plate with cubeMX. You just export as cmake or w\/e instead of exporting to the stmIDE.",
        parentId: "n89fvir",
        postId: "1mm4h6a",
    },
    {
        id: "n8gpvyr",
        body: "True, (I haven't really been following that generator) but wasn't CubeMX sunsetted in favour of CubeIDE?",
        parentId: "n8aocal",
        postId: "1mm4h6a",
    },
    {
        id: "n7wdiqq",
        body: "For what you want to do, the development board really doesn't matter. The simpler, the better. Just make sure it has enough RAM for the display buffer. A Cortex-M0+\/M23 would be fine. Also keep in mind that you'll have to design the PCB, so you want to avoid any unnecessary complexity. If you have enough I\/O with a 32-pin package, don't use an MCU with 64 pins.\n\nNow, for learning in the long run (outside of the scope of your prototype), a development board with a Cortex-M4 is great because it has tons of peripherals while remaining relatively beginner-friendly. I like the STM32F407 (or one of its many clones) because it even has Ethernet, so there's a lot to learn with it.\n\nFor the IDE, it's up to you. Try several, starting with the vendor's IDE, and choose the one you prefer. You don't even need an IDE, though using one makes certain things easier - autocomplete, source navigation, debugging.",
        parentId: null,
        postId: "1mm4h6a",
    },
    {
        id: "n7wiz1j",
        body: "I would go with Nordic personally, although both will work OK.  nRF 52 series is mature and works great, but there is a newer 54L series out now, you might consider it for a new design.",
        parentId: null,
        postId: "1mm4h6a",
    },
    {
        id: "n89hvdg",
        body: "I have the following setup:  \n\\- github actions on source code repos. These actions are triggered on release or pre release events (e.g when i create a tagged release on stable(release event), alpha or dev branches(pre-release event). Tip: make reusable workflows that can be shared with all the individual source code repos. saves you time. check this out [https:\/\/github.com\/mrbeam\/mrb-workflows\/blob\/main\/.github\/workflows\/update-beamos-tags.yml](https:\/\/github.com\/mrbeam\/mrb-workflows\/blob\/main\/.github\/workflows\/update-beamos-tags.yml)\n\nWhat does the gh action do?  \n\\- it updates a .inc file on a custom recipe layer that is included on all source code recipes.\n\nyou can check if the gh actions use case can be extended to SHA",
        parentId: null,
        postId: "1mo422d",
    },
    {
        id: "n89i3na",
        body: "In my case, i consider two repos.  \n\\* source code repo for the packages  \n\\* top level repo - which contains all the default layers and custom layer for my use case",
        parentId: "n89hvdg",
        postId: "1mo422d",
    },
    {
        id: "n89nu3h",
        body: "Thanks that’s along the lines of what I was thinking of. I do wonder if there is a canonical way of doing this still since it is a common use case.",
        parentId: "n89i3na",
        postId: "1mo422d",
    },
    {
        id: "n8aampc",
        body: 'This is heavily customized and works for my use case(\\~3 years now). There should be something that i am unaware of as well about a canonical way to do things. Nevertheless, below is a sample that you could use.\n\n**Custom recipe:**\n\npackage\\_1.bb :\n\n    SUMMARY = "Custom package name"\n    HOMEPAGE = "https:\/\/github.com\/path\/to\/your\/source_code_repo"\n    AUTHOR = "John Doe &lt;<EMAIL>&gt;"\n    LICENSE = "CLOSED"\n    \n    include source-code-git-tags.inc\n    \n    BRANCHNAME="${@bb.utils.contains(\'IMAGE_TYPE\', \'prod\', "stable", "alpha", d)}"\n    OPTIONS="${@bb.utils.contains(\'USE_TAGS\', \'true\', "nobranch=1;tag=${TAG_${PN}}", "branch=${BRANCHNAME}", d)}"\n    SRC_URI = "git:\/\/**************\/path\/to\/your\/source_code_repo.git;protocol=ssh;${OPTIONS}"\n    SRCREV = "${@bb.utils.contains(\'USE_TAGS\', \'true\', "", "refs\/heads\/${BRANCHNAME}", d)}"\n    PV = "0.1+git${SRCPV}"\n    \n    S = "${WORKDIR}\/git"\n    \n    RDEPENDS:${PN} += " \\\n        dep1 \\\n        dep2 \\\n        dep3 \\\n    "\n    \n    inherit python3-dir python3native setuptools3\n\n**source-code-git-tags.inc:**\n\n    TAG_package_1=     "v2.1.2"\n    TAG_package_2=     "v1.0.1"\n    TAG_package_3=     "v2.4.0"\n\n`bb.utils.contains` helps me to switch based on certain flags that i set during build time which includes:\n\n* IMAGE\\_TYPE\n   * prod - which always creates an image from the stable branch of source code repo\n   * dev - which by default creates an image from alpha branch of the source code repo, can be changed to a custom branch as well.\n* USE\\_TAGS - where i can switch between using tags or the head of the branch I  want\n   * true\n   * false\n\nThe above flags are set in a gh action for the top level repo where creating a tagged release or a cron job set to create nightly build is set up.\n\nLet me know if you need some help with any specifics. You can dm me, im glad to help.',
        parentId: "n89nu3h",
        postId: "1mo422d",
    },
    {
        id: "n8aj8u5",
        body: 'For #2\n\n`SRCREV = "${AUTOREV}"`\n\nFor #3 use mono repo instead of submodules.\n\n  \nOr use CI\/CD to automate what you already do',
        parentId: null,
        postId: "1mo422d",
    },
    {
        id: "n89dq0z",
        body: "No, I dont",
        parentId: null,
        postId: "1mo38pz",
    },
    {
        id: "n8as6gy",
        body: "Yes.",
        parentId: null,
        postId: "1mo38pz",
    },
    {
        id: "n8cn3xp",
        body: "Original I was looking for a shield which supports : UART, CAN, a lot of ADC and DAC but I did not finde something. So I thought maybe we have to do our own ?",
        parentId: "n8as6gy",
        postId: "1mo38pz",
    },
    {
        id: "n88yo1d",
        body: "The description of issue is too poor. The cause or causes may be several.\n\nOne problem may be hardware. Do you have a well-loaded RS485 bus? What is the RS485 voltage level when no node is loading the bus?\n\nAnother problem is some weird bug in the firmware of MCU. First of all, try to isolate the problem with a single slave node. If the problem happens in this case, it may be much simpler to find the bug.",
        parentId: null,
        postId: "1mo0pbn",
    },
    {
        id: "n891tms",
        body: "when no node is loading, at non inverting the voltage is in range of 2.06 and the inverting between 1.9 and 2.",
        parentId: "n88yo1d",
        postId: "1mo0pbn",
    },
    {
        id: "n8927dn",
        body: "I am using pyserial with timeout. I believe that will wait for reply from the board to be completed ?",
        parentId: "n88svdq",
        postId: "1mo0pbn",
    },
    {
        id: "n88tr9l",
        body: "RS485 is not a communication protocol. So what are you using? It is likely in a faulty state.",
        parentId: null,
        postId: "1mo0pbn",
    },
    {
        id: "n891xeu",
        body: "i trying to do uart with multiple devices. So used a rs485 half duplex",
        parentId: "n88tr9l",
        postId: "1mo0pbn",
    },
    {
        id: "n8bn0mc",
        body: "2 most frequent issues :\n- Noises\n- Memory \n\n1st is upon your schematic design, which requires hardware debugging.\n\n2nd is how you use C ? Fat MCU make ppl loosely handle bad Arduino code that leak memory\/stack elsewhere randomly. \n\nCrashed randomly when I tested sketch on Pico\/Arduino Libraries but worked flawlessly &amp; stable on PIC\/C code in production.",
        parentId: null,
        postId: "1mo0pbn",
    },
    {
        id: "n8d3q2d",
        body: "Do the test with only 1 slave, debug if necessary while communication is happening",
        parentId: null,
        postId: "1mo0pbn",
    },
    {
        id: "n88iyhn",
        body: "https:\/\/github.com\/STMicroelectronics\/STM32CubeG4\/blob\/master\/Projects\/NUCLEO-G474RE\/Examples\/HRTIM\/HRTIM_Basic_ArbitraryWaveform \n\nLooks close.",
        parentId: "n88hj9x",
        postId: "1mnmpw6",
    },
    {
        id: "n88jutv",
        body: "This was the example I had to adapt to get working with the dma, the process was quite tedious and required me to go through documentation\/datasheets - I’m curious if this workflow can be automated in some way?",
        parentId: "n88iyhn",
        postId: "1mnmpw6",
    },
    {
        id: "n88k188",
        body: "No. That's Embedded Life.\n\nEven if you use LLMs: you must 100% understand what you are doing. Otherwise you will not be able to debug issues - even if you use these tools to create code.\n\nIt's not web development where just the UI looks bad. In our case maybe a super expensive machine will burn down.",
        parentId: "n88jutv",
        postId: "1mnmpw6",
    },
    {
        id: "n88efyl",
        body: 'I tried it seveal times and it failed hard. Means: It made 80% of the boiler code looked perfectly nice - 20% was looking very nice but completely broken and it took ages to debug. Never again.\n\nBut it works quite nice for "asking" datasheets for specific data. Even more if they are in Chinese.',
        parentId: null,
        postId: "1mnmpw6",
    },
    {
        id: "n84exm9",
        body: "You'll never regret getting more scope than you need.  Get four channels too.  I've never really truly needed four channels, but I've wished for three channels plenty of times, when I only had my still working two-channel Rigol ds1102c.",
        parentId: null,
        postId: "1mnewjd",
    },
    {
        id: "n84dyqv",
        body: "Protocol decoding in the scopes is a pain, get a cheap logic analyzer, it will be much more useful for general work.\n\nSDS1102 is really good as a second scope, since it has no fan and silent. Its performance is also fine, but its single trigger rearm is infuriating. I would go with Siglent as an only scope.",
        parentId: null,
        postId: "1mnewjd",
    },
    {
        id: "n87sczx",
        body: 'Take a look at the SDS804X HD oscilloscope.  As far as I can tell, it outperforms the SDS1104X-E in every way except bandwidth, which can be "fixed" by searching EEVblog.\n\nMore info here: https:\/\/www.reddit.com\/r\/AskElectronics\/comments\/1igkg77\/siglent_sds1104xe_or_sds814x_hd_scope\/',
        parentId: null,
        postId: "1mnewjd",
    },
    {
        id: "n88v9qh",
        body: "thanks for the tip, I will check on it. As I see it's more or less for the same price.",
        parentId: "n87sczx",
        postId: "1mnewjd",
    },
    {
        id: "n84dxvy",
        body: "Get a logic analyzer if you also want to focus and decode communication  protocols (UART, SPI, I2C etc).\nP.S. Since I am not aware of the scopes you provided has the capability to decode communication. The scope I use requires a license to decode communication.",
        parentId: null,
        postId: "1mnewjd",
    },
    {
        id: "n85rs97",
        body: "Someone said this a while ago in this subreddit but I second this - I rarely actually have the need for a logic analyzer since decoding serial protocols is also easily doable with a scope for “smaller-scaled” instances but when I do need it, boy does it come in handy",
        parentId: "n84dxvy",
        postId: "1mnewjd",
    },
    {
        id: "n860x1x",
        body: "Agree with you.",
        parentId: "n85rs97",
        postId: "1mnewjd",
    },
    {
        id: "n88j7na",
        body: "The Cypress FX2 work up to 24MHz sampling rate. Great for a lot of tasks.\n\nIf you need more and are on budget: DSLogic.\n\nSaleae are nice. But not their price tag.",
        parentId: "n84dxvy",
        postId: "1mnewjd",
    },
    {
        id: "n8b0idw",
        body: "I honestly don't know if I would be able to get a project to a working state without saleae logic pro. When dealing with multiple communication interfaces and debugging real time application execution it is (at least for me) the greatest tool ever developed.",
        parentId: "n88j7na",
        postId: "1mnewjd",
    },
    {
        id: "n8646zz",
        body: "If you want to have the cheapest but functional setup,\n\n1) Scope- DreamSource Lab \n2) Fnirsi Type C Power supply (switching supply, has higher noise)\n3) Uni-T \/Habotest multimeter\n\nIf you want to do it professionally, don't buy any of the above buy the ones that others recommended.",
        parentId: null,
        postId: "1mnewjd",
    },
    {
        id: "n89j92c",
        body: "For the scope, the Hanmatek DOS1102 is the same as the OWON SDS1102 but a little bit cheaper. It's a decent entry-level scope, no problem. If you want something better, go for RIGOL.\n\nThe KKMOON\/FeelTech FY6900 signal generator is fine too.\n\nFor the multimeter, the OWON XDM1041 has an unbeatable price\/performance ratio as a bench DMM. Otherwise, ANENG makes cheap but decent multimeters. The AN8008 is quite cheap, fits in a pocket, and does all you need. Maybe buy both.\n\nAs a logic analyser, I recommend the Alientek DL16. It's the cheapest I could find with triggers - which you don't get with the even cheaper Saleae clones.",
        parentId: null,
        postId: "1mnewjd",
    },
    {
        id: "n8cfgbq",
        body: "thanks for the info!\n\n  \nI was also thinking about Hanmatek DOS1102, but some users complain about software problems, and short lifespan - however, it's also much cheaper than a siglent. Still haven't decided to go with a cheap, or already buy the siglent.\n\n  \nOWON XDM1041 seems a very good DMM, very good tip, thank you!\n\nI have a logic analyzer, which is a saleae clone, but I am not satisfied with it, it always have a problem detecting device, so I will move on Alientek, it seems much better.",
        parentId: "n89j92c",
        postId: "1mnewjd",
    },
    {
        id: "n89xatk",
        body: "First off decide what you actually need - requirements are different low-noise analogue Vs RF work Vs embedded \/ digital.\n\nAlso there's a ton of gear snobbery out there, people pretending you can't possibly live without a $200 Fluke meter when a $5 clone is good enough for 98% of measurements \/ diagnosis where you just need to check if 3.3v is getting to the right place or not.\n\nIt sounds like you're doing embedded development which means mostly digital - for that you care less about super high-end stuff and are mostly watching\/diagnosing digital signals that are either on or off. I have a 4ch Rigol MSO on my desk that was cheap, it can't touch the Tektronix for low noise but the fact it is 4ch and can decode I2C or SPI or work as a logic analyser is way more useful 99% of the time.\n\nLikewise I have a few $10 DMM's scattered around the lab for quick checks, if we need really accurate \/ calibrated measurements then yes the good Fluke meter comes out but that's rare.\n\nI'd also say there's some great cheap open source stuff out there - BusPirate and open logic analyser ( [Dangerousprototypes.com](http:\/\/Dangerousprototypes.com) ) are very useful.\n\nI have a cheap programmable signal generator on Dave Jones' recommendation, I forget what brand but clones are all over the net, it's good enough and cost like $40.\n\nFor power supplies all I'd say is cheap switch-mode ones can be noisy and anything too cheap could have some nasty traits that expensive ones avoid - accidentally outputting weird voltages when switching or adjusting, power glitches, lack of protection \/ bad earthing - just go into it with your eyes open and don't put too much trust in them. I've known people kill very expensive circuits by plugging in a malfunctioning bench PSU.",
        parentId: null,
        postId: "1mnewjd",
    },
    {
        id: "n8cgmkl",
        body: "thank for the advices!\n\n  \nI am not looking for very high end tools, price-value matters more. Later I will keep upgrading the stuff, when my knowledge gets deeper.",
        parentId: "n89xatk",
        postId: "1mnewjd",
    },
    {
        id: "n8fo7pa",
        body: "Worth saying if you're at uni the uni labs should have a few good tools if you really need them for a particular measurement or task.",
        parentId: "n8cgmkl",
        postId: "1mnewjd",
    },
    {
        id: "n8bm095",
        body: "Funny that you can get Siglent SDS804\/814X for both osc + decoding ..",
        parentId: null,
        postId: "1mnewjd",
    },
    {
        id: "n8buqu7",
        body: "For learning I'd get a second hand Digilent AD 2\/3. It should be fairly cheap, you can use it as a scope and a signal generator and it comes with one of the better softwares I've seen. I also use them with the python API to do some automated testing. At work I have a few expensive scopes, but for what I need at home it's more than enough.\n\n(Not and ad though, just a happy customer. I'm sure there are other similar USB logic analysers\/scopes, that are just as good)",
        parentId: null,
        postId: "1mnewjd",
    },
    {
        id: "n8dh9bm",
        body: 'In 5 or 10 years from now, you will have wished you spent more on one or more of these items.\n\nThe first item that everyone should buy is a Digital Multimeter.  For handheld, a "UNI-T UT61E+" is a reasonable choice.  If you have deep pockets, then a "Siglent SDM3065X" 6.5 digit benchtop is a great choice.\n\n* https:\/\/old.reddit.com\/r\/PrintedCircuitBoard\/wiki\/tools#wiki_multimeter\n\nFor lower noise, it\'s better to choose a linear-type power supply.  Cheap switching power supplies are great for big loads, but cheap switchers often suck for low-noise projects, such as analog \/ audio \/ RF projects.\n\n* https:\/\/old.reddit.com\/r\/PrintedCircuitBoard\/wiki\/tools#wiki_dc_lab_power_supply\n\nThe Feeltech FY6900 is about as cheap as you should go.  The UNI-T UTG1000X &amp; UTG2000X families are much better choices, but cost significantly more too.\n\n* https:\/\/old.reddit.com\/r\/PrintedCircuitBoard\/wiki\/tools#wiki_waveform_generator\n\nIt\'s probably best to get a 12-bit digital oscilloscope.  In the long term, you will kick yourself in the future if you buy a cheap-ass digital scope that lacks features.  You can\'t go wrong with a Rigol DHO800 or DHO900 families.  The DHO900S (the "S" models) have a builtin waveform generator, but doesn\'t have as many features as the UNI-T UTG1000X &amp; UTG2000X families.\n\n* https:\/\/old.reddit.com\/r\/PrintedCircuitBoard\/wiki\/tools#wiki_oscilloscope',
        parentId: null,
        postId: "1mnewjd",
    },
    {
        id: "n8eyqt5",
        body: "Thank you!\n\nThe UTG waveform generator recommendation is really a better choice.\n\nI will Check on these.",
        parentId: "n8dh9bm",
        postId: "1mnewjd",
    },
    {
        id: "n84bv6i",
        body: "For the oscilloscope, Siglent. Multimeter, check brymen brand.",
        parentId: null,
        postId: "1mnewjd",
    },
    {
        id: "n84hvoo",
        body: "As an owner of BM869 I would argue that brymen is overkill for a beginner. OP should get DMM under 50USD and invest rest into decent power supply and scope",
        parentId: "n84bv6i",
        postId: "1mnewjd",
    },
    {
        id: "n85vk0u",
        body: "Getting a Brymen DMM is the cheap solution, as they are only a couple of hundred bucks,  those less than $50 toys are pure junk.",
        parentId: "n84hvoo",
        postId: "1mnewjd",
    },
    {
        id: "n862mag",
        body: "$50 unit-t or even $20 aneng is enough to cover 99% of embedded developer DMM needs and for the remaining 1% you will either way need a tabletop or some specialised equipment.  \nI do like my brymen but tbh its more of a novelty item",
        parentId: "n85vk0u",
        postId: "1mnewjd",
    },
    {
        id: "n88j50w",
        body: "Also Brymen gang here.\n\nBuy once cry one. Their multimeters will last a lifetime.",
        parentId: "n84bv6i",
        postId: "1mnewjd",
    },
    {
        id: "n7k8xvx",
        body: "How are you using reset? You should probably just leave them high, if not pulled up.\n\n\nAnd you're sure the chip selects aren't overlapping?",
        parentId: "n7jbvnr",
        postId: "1mkjmsl",
    },
    {
        id: "n7jdsi8",
        body: "That’s a pretty long SPI run for a pretty small board. I don’t really know, but if you can get two simultaneous interfaces working but not three, my thoughts go to a) signal coupling or b) power. \n\nFor coupling, you may have tried this, but do you get better results if you separate the cable runs? Or if you use the SPI that are further apart? You could even try holding some foil between the runs to get some shielding… \n\nFor power, that’s a tougher sell — would be interesting to see Vdd and Gnd on a scope. Maybe more bulk capacitance could help a bit, but I’d want someone else to comment on that.",
        parentId: null,
        postId: "1mkjmsl",
    },
    {
        id: "n7jedge",
        body: "I don’t think power is an issue, that I’ve noticed anyway. I’m probably better off getting shielded wires?",
        parentId: "n7jdsi8",
        postId: "1mkjmsl",
    },
    {
        id: "n7jm4ep",
        body: "My guess would be there is too much capacitance in the shared SPI lines (SCK, MISO, MOSI).\n\n\nDoes the problem occur once you physically connect more of the SPI wires?\nAnd is the SPI being driven directly from the microcontroller's GPIO?\n\n\nYou could potentially fix this by adding a buffer IC that can drive more current into the lines.\n\n\nRegardless, you should definitely check the signal integrity with an oscilloscope, if you can get access to one.",
        parentId: null,
        postId: "1mkjmsl",
    },
    {
        id: "n7jnou8",
        body: "I thought I had another pic in the post. The shared lines are bussed on the perf board, would it help if I could bus them as far upstream as possible?\n\nhttps:\/\/preview.redd.it\/wyua0m0w3qhf1.jpeg?width=3024&amp;format=pjpg&amp;auto=webp&amp;s=c371b68d5ff856cedaf10747095e2d880111e9bf\n\nMosi, miso, clk are blue, green, and white.",
        parentId: "n7jm4ep",
        postId: "1mkjmsl",
    },
    {
        id: "n7jsyab",
        body: "Yes actually. In each wire bundle, you have a signal wire (SCK, for instance) running next to a ground wire for a relatively long distance. This adds capacitance, and it can be a lot when you multiply it by 5.\n\nRunning one set of cables all the way to the end, and leaving the 5-way split as short possible, will help.\n\nAgain, does the problem occur once you physically connect more of the SPI wires? And is the SPI being driven directly from the microcontroller's GPIO?\n\nAre you able to check the signals with a oscilloscope? That will tell all.",
        parentId: "n7jnou8",
        postId: "1mkjmsl",
    },
    {
        id: "n7jtbt0",
        body: "Don’t have an Oscope. Yes the problems occur as more spi wires are added to the buss. The the mcu does a check at the beginning of the program to get the codes I mentioned, then it cycles thru the readers one by one.",
        parentId: "n7jsyab",
        postId: "1mkjmsl",
    },
    {
        id: "n7jtfi9",
        body: "Here is a pic of the busses in the bottom of the board.\n\nhttps:\/\/preview.redd.it\/9cmyfcbybqhf1.jpeg?width=3024&amp;format=pjpg&amp;auto=webp&amp;s=939022110081ee498d5733a8fe4804787a91ebfe\n\nI thought it was in the post.",
        parentId: "n7jsyab",
        postId: "1mkjmsl",
    },
    {
        id: "n7jd2rg",
        body: "Your photo is worthless. Do you have a pdf schematic ??",
        parentId: null,
        postId: "1mkjmsl",
    },
    {
        id: "n7joy3t",
        body: "I don’t have schematic but I didn’t realize that a different photo wasn’t included. Here you can see the rails that connect the spi lines. Blue is mosi, green is miso, white is clk.\n\nhttps:\/\/preview.redd.it\/q9n3590o5qhf1.jpeg?width=3024&amp;format=pjpg&amp;auto=webp&amp;s=c9476da92d25347d501f086d4d0bba0e38a4d0d6",
        parentId: "n7jd2rg",
        postId: "1mkjmsl",
    },
    {
        id: "n7jrdkg",
        body: "How do you know you wired it up correctly ?? It doesn't work, does it.\n\nGood Luck",
        parentId: "n7joy3t",
        postId: "1mkjmsl",
    },
    {
        id: "n7jswnq",
        body: "The wiring is correct. Lol I’ve been on this about 6 months and have kinda hit a wall. But like I said I can def get at least two working at a time.",
        parentId: "n7jrdkg",
        postId: "1mkjmsl",
    },
    {
        id: "n7k1bqt",
        body: "What is the baud rate? You might want to go slower and slower, possibly bit banging SPI.",
        parentId: null,
        postId: "1mkjmsl",
    },
    {
        id: "n7m7cxe",
        body: "Quick sanity check: reattach the peripherals with the shortest wire you can manage and see if it works at all.\n\n\nWhen you go back to the longer wires make sure the termination at the peripheral uses the correct resistance.",
        parentId: null,
        postId: "1mkjmsl",
    },
    {
        id: "n84dfvk",
        body: "Fun fact - if you plug an RS-232 serial port GPS mouse into older Windows machines it thinks it's a serial mouse because both just spit out a stream of data without being asked, and windows does its best to interpret the data as something it knows.\n\nAside from turning the capslock light on\/off 99% of the time (older) keyboards are only transmitting serial data.\n\nUSB ones you've got the whole enumeration &amp; polling &amp; USB stack going on which is way more complicated.",
        parentId: "n846yn2",
        postId: "1mneah6",
    },
    {
        id: "n84saz7",
        body: "I remember back when 'gaming keyboards' were first becoming a market segment in the aughts, there was a brief period where a few companies were pushing PS\/2 and serial again because it's interrupt driven and ~~theoretically~~ demonstrably* faster than USB.",
        parentId: "n84dfvk",
        postId: "1mneah6",
    },
    {
        id: "n85tfhu",
        body: 'It mostly came from the tech press regurgitating marketing bullshit without actually *understanding* the technology.\n\nFor example, PS\/2 can operate no faster than 16.7Kbps, with each data byte taking 11 bits, and with a mouse update taking 3 data bytes. That means a mouse update takes *at least* 1.8 millisecond to transmit from start to finish.\n\nMeanwhile, USB is polled, but even the most basic version can be polled once every millisecond, with the data transfer only taking a few microseconds. This means a USB mouse can be polled and respond *twice* during a single PS\/2 transmission!\n\nThe whole interrupt stuff is also woefully outdated. Sure, it worked like that 40 years ago, but you do **not** want to interrupt a modern CPU for something as trivial as a moving mouse. Imagine interrupting your game to generate some mouse report, which is just going to sit in a buffer waiting for your game to finish calculating the current frame and ask for updates for the *next* frame - the additional context switching is only going to reduce your FPS and increase your latency!\n\nBesides, I highly doubt it even has the hardware for a true interrupt. It\'s far more likely to be implemented like a mailbox, with a kernel worker thread looking for a pending "Hey, this device has new data" flag from the PS\/2 controller every X microseconds. You know, basically *exactly* the same as USB...',
        parentId: "n84saz7",
        postId: "1mneah6",
    },
    {
        id: "n85yqd6",
        body: "2 things:\n\n\\-x86 CPU architecture still has dedicated hardware interrupt channel(s) in the PIC for mice and keyboard, which is why PS\/2 slots appear on motherboards to this day. As far as I'm aware, they're still hooked directly into the CPU and not running through a logical USB hub on the chipset. Most laptops, actually, still use PS\/2 if I'm not mistaken for touchpads and keyboards because they're lower energy.  \n\\- It CAN be polled a 1000Hz but most USB mice live in the 1-200 hz range, meaning we're looking at up to 10ms before it's even brought into the HAL. Meanwhile, Windows allows developers to bolt other ISRs to the PS\/2 hardware interrupts on a high level, meaning you can have logic in your game fire microseconds after the button is pressed. This would require queuing up the input as normal or triggering some really challenging asynchronous state logic that could screw up your game state, and I agree most developers would just drop the incoming input into some mailbox or queue rather than deal with the potential headache.\n\nI don't disagree that it's splitting hairs at the time scales we're dealing with, and the actual bottleneck with most of this stuff is probably server tick rate anyway. Even without that, saving 5 MS of input lag equates to MAYBE a single frame of delay, and that's if your GPU is screaming. I didn't say I agreed with it or thought it was a good idea, they're just not wrong that PS\/2, when implemented properly, is objectively faster than USB.",
        parentId: "n85tfhu",
        postId: "1mneah6",
    },
    {
        id: "n84v61m",
        body: "Some dude a while back proved one of the old 8-bit Macs was faster from keypress to effect on-screen than a screaming fast modern gaming PC because there was so much less hardware &amp; software in the way.",
        parentId: "n84saz7",
        postId: "1mneah6",
    },
    {
        id: "n84yh8w",
        body: "not surprised at all, honestly.",
        parentId: "n84v61m",
        postId: "1mneah6",
    },
    {
        id: "n8994d9",
        body: "Measuring latency, we've found that modern systems will have around 40-60ms latency from key\/joystick button press to something happening on the screen. A vintage system like a Sinclair Spectrum will have 20ms latency worst case.",
        parentId: "n84v61m",
        postId: "1mneah6",
    },
    {
        id: "n8471f5",
        body: "Look there is no rule or law that would mandate for keyboards to be simplex. Engineers will use whatever technology seems to be the best. You came up with two examples that require duplex. In fact, I would guess that most modern keyboards use duplex",
        parentId: null,
        postId: "1mneah6",
    },
    {
        id: "n84re3o",
        body: "Simplex != 1-way.\n\nSimplex just means 1-way at a time.\n\nUSB is a request\/response protocol. The host sends a request to the device. The device sends a response back. Lather, rinse, repeat.\n\nAnd even with PS\/2, the host can send data back out to the keyboard, which is how the mode lock lights work. Just because you stroke the caps-lock key, it does not necessarily follow that the PS\/2 keyboard has to turn on the caps-lock LED. The host tells the keyboard that it's now interpretting keystrokes in upper-case by telling it to turn its caps-lock LED on. But, even so, the keyboard's still just sending keystrokes. It knows nothing of how the host is interpretting them.\n\nIf there are PS\/2 keyboards with RGB, I'm not sure how that would be handled by the PS\/2 protocol.",
        parentId: null,
        postId: "1mneah6",
    },
    {
        id: "n85pse2",
        body: "Simplex is truly one way only. Half duplex means both directions but only in one direction at a time.",
        parentId: "n84re3o",
        postId: "1mneah6",
    },
    {
        id: "n86n7ra",
        body: "I stand corrected.",
        parentId: "n85pse2",
        postId: "1mneah6",
    },
    {
        id: "n7zsqyl",
        body: "I think your bias tee L\/C values are good.\n\n\\-\n\nThe SN74LVC1G132DCKR will likely not have enough current to source for your active antenna.\n\nDo you know what active antenna you are using? (or a general idea?) Usually they are more up to 50 mA max, outside of the 24mA max of the 1G132 at 3V (from Recommended Operating Conditions section), and the voltage drops a lot when used to source a lot of current like you have it.\n\nIf you look at the V\\_OH output section of the datasheet, sourcing 24 mA of current at 3V, you can see the output voltage drops to 2.3 V. This is not so good for some active antennas.\n\nInstead...\n\nGet the HW design document for the LIV3R here, and look at their Active Antenna design page in section 6.2:\n\n[https:\/\/www.st.com\/resource\/en\/user\\_manual\/um2231-teseoliv3-gnss-module--hardware-manual-stmicroelectronics.pdf](https:\/\/www.st.com\/resource\/en\/user_manual\/um2231-teseoliv3-gnss-module--hardware-manual-stmicroelectronics.pdf)\n\nThey recommend either a DCX123JU (NPN\/PNP switch) or TPS22943 (short-circuit limited to 40 mA.) Personally I like going with more of an integrated device such as the TPS22943, as it is easier to use.\n\nMake sure to follow other recommendations in that guide, power supply filtering, power management, etc.\n\n\\-\n\nAlso, compare with some other GPS HW integration guides, this one has an active antenna design page with some component values, [https:\/\/content.u-blox.com\/sites\/default\/files\/NEO-8Q-NEO-M8-FW3\\_HIM\\_UBX-15029985.pdf](https:\/\/content.u-blox.com\/sites\/default\/files\/NEO-8Q-NEO-M8-FW3_HIM_UBX-15029985.pdf)",
        parentId: null,
        postId: "1mlarsq",
    },
    {
        id: "n7phvn9",
        body: "I would start with adding individual GND wires for each SPI signal, then twisting each of those together as a pair. \n\nIf that doesn’t work you’ll need to decrease the clock speed, and if you can’t do that you’ll want to add some LVDS drivers and also use twisted pairs with that configuration and it’s pretty much guaranteed to work.",
        parentId: null,
        postId: "1mkjmsl",
    },
    {
        id: "n7pi5ab",
        body: "However, you’ll probably want to look at the lines somehow, the cheapest way to do that would be with a cheap logic analyzer like this: [https:\/\/a.co\/d\/2qXNTtx](https:\/\/a.co\/d\/2qXNTtx)",
        parentId: "n7phvn9",
        postId: "1mkjmsl",
    },
    {
        id: "n81ahov",
        body: "Get an oscilloscope and stop guessing.",
        parentId: null,
        postId: "1mkjmsl",
    },
    {
        id: "n79nhrh",
        body: "For a safer DIY neck fan, use a protected 18650 battery with a USB-C charging board, a switch, and a 5V blower fan; your component list is correct.",
        parentId: null,
        postId: "1mjaumr",
    },
    {
        id: "n79pod9",
        body: '&gt; I assume the 18650 is "safer"\n\nSeems like lots of people are pulling assumptions out of there ass this morning.\n\nAnyway, the charging board (TP4056) is available with protection circuit, which you should use with an unprotected 18650 cell.  Or you could use a simple TP4056 board (which is a little smaller) and an unprotected 18650 cell or LiIon pouch battery (pouch batteries usually have the protection built in).  Both types of cell are safe when handled properly (don\'t puncture, crush, dent, drop, open, or burn the cell).',
        parentId: null,
        postId: "1mjaumr",
    },
    {
        id: "n79ry2c",
        body: 'As I mentioned, my experience with pouches and LiPo\/Li-Ion has been with airsoft where they did tend to get physically mishandled and erupt, explode, incinerate, etc.\n\n..as well as the errant "I was repairing an old cellphone and poked the funny balloon" posts that go viral.',
        parentId: "n79pod9",
        postId: "1mjaumr",
    },
    {
        id: "n7gbrgz",
        body: "I'd assume those are more prone to damage if not physically protected but be fine otherwise. I guess it's down to your design choice. Just chimed in to say that there are boards with TP4056\/IP5306 charging AND 5V boost built in, so you can get one of those instead of 2 seperate circuits.",
        parentId: "n79ry2c",
        postId: "1mjaumr",
    },
    {
        id: "n7gdbrs",
        body: "I'm unfamiliar with the board designations and features, I'd assume 5V boost just would spin the fan faster in this use case?",
        parentId: "n7gbrgz",
        postId: "1mjaumr",
    },
    {
        id: "n7gh6tw",
        body: "Well a 5V fan would need.. 5V. And the battery outputs 3.7 - 4.2V so you'd need to boost it to run it at its rated spec.\n\nSo one board to charge battery. Another to boost voltage.\nTP4056 is like the standard chip+board to charge, IP5306 is something i came across and I think it's fairly new? \n\nAnyways you can find tp4056+ step up booster combo so you can use one board instead of 2",
        parentId: "n7gdbrs",
        postId: "1mjaumr",
    },
    {
        id: "n7gkat3",
        body: "More info: Apparently the common board doesn't have [battery protection](http:\/\/www.youtube.com\/watch?v=7StJZc0lr9w)\n\nBut there seems to be [another](http:\/\/makerbazar.in\/products\/3-5v-to-5v-9v-lithium-battery-charge-discharge-adjustable-module?variant=47006430429424) that claims to have protection but over discharge is not mentioned. You'd have to do your research (just buy one really) or resort to 2 seperate boards",
        parentId: "n7gdbrs",
        postId: "1mjaumr",
    },
    {
        id: "n8ae4a7",
        body: "The reason that a 9V battery is smaller than four AAs is that it contains much less motor-spinning energy. These toys are actually pretty well engineered for the price. You can’t improve the performance with the wrong battery for the job.",
        parentId: null,
        postId: "1mo4hml",
    },
    {
        id: "n89hy8u",
        body: "No because of 2 Reasons\n\n1.  4*1.5V batteries will output between 4-6V and a 9V battery will output too much.\n\n2. and more importantly: a 9V Battery can only output a pretty low current making it unusable in an rc car. \n\nGet 2S Lipo with a buck converter set to 6V and a multimeter to set it up. Also a 2s lipo charger would be needed",
        parentId: null,
        postId: "1mo4hml",
    },
    {
        id: "n8dsiu3",
        body: "You can’t directly hook up a 9 V battery, your original motor and board are designed for about 4-6 V.\nIf you connect 9 V directly, the motor might run too hot and the circuit could burn out.\nIf you want to use a 9 V source, put a small DC-DC buck converter, set to 5 V, between the battery and the car’s circuit.",
        parentId: null,
        postId: "1mo4hml",
    },
    {
        id: "n89ingf",
        body: "4 1.5v batteries means 6v - if you plug 9v into it, you might burn it out. \n\nAssuming you just want to run the motor and not play with the circuit board, you can stick a resistor in series to drop the volatge - eg if the motor needs 6v and uses 200millamps you could stick 15 ohm resistor in and not blow anything up",
        parentId: null,
        postId: "1mo4hml",
    },
    {
        id: "n8bboyv",
        body: "It is unlikely a 9V battery can deliver 200mA.\n\nThe short circuit current from an alkaline 9V is about 350mA\n\nA steady draw of 30mA (or less) will give a reasonable service life.",
        parentId: "n89ingf",
        postId: "1mo4hml",
    },
    {
        id: "n8fy351",
        body: "I've recorded 800 mA from an 9v energizer before -  not my finest hour since i blew the crap out of the circuit",
        parentId: "n8bboyv",
        postId: "1mo4hml",
    },
    {
        id: "n89k5jj",
        body: "👍 \n\nThere is a fault in thinking the 9-volt battery is an upgrade, because it can't produce the amperage needed.\n\n...\n..\n.",
        parentId: null,
        postId: "1mo4hml",
    },
    {
        id: "n89596y",
        body: "With the limited info, I can only give general advice.\n\nIf this happened after a drop, there is most likely a broken wire, connection or the speaker itself.\n\nTroubleshooting includes opening up the headphones and probing to find the culprit. Search for the model, and you will find disassembly instructions.",
        parentId: null,
        postId: "1mo1df3",
    },
    {
        id: "n8awp6l",
        body: "Wore most likely broken at the solder joint. This will be an easy fix if you can get to the speaker part without destroying the headphones. If you do manage to get them open and get to the problem any cheap soldering iron will do and you may be able to just touch the wire to the speaker with the iron to solder them back together. \n\nIf you want to do a better job then purchasing some solder will also allow you to put more solder on the joint.  Watch some videos but it is really easy to solder these types of things.",
        parentId: null,
        postId: "1mo1df3",
    },
    {
        id: "n8dnavo",
        body: "You don't even know what's wrong. How good are you reverse engineering skills?\n\n... Yeah that's what I thought. \n\nBuy new headphones.",
        parentId: null,
        postId: "1mo1df3",
    },
    {
        id: "n87q1p2",
        body: "Spdt switch.. if you're near a city an electronics store should have tons, keep the button for the new switch.  Otherwise eBay is where I'd go.",
        parentId: null,
        postId: "1mnv8ga",
    },
    {
        id: "n7wozpw",
        body: "Id probably get a cheap solar garden light and gut that to put in the traffic light",
        parentId: null,
        postId: "1mm96gb",
    },
    {
        id: "n80a8gg",
        body: "Depends whether the lights all go on at once or have some other pattern that you want to retain",
        parentId: "n7wozpw",
        postId: "1mm96gb",
    },
    {
        id: "n7w31m1",
        body: "I actually don't think you have an amperage issue. Just the smaller battery won't last too long. My guess is your problem with the small battery is it won't last very long.",
        parentId: null,
        postId: "1mm96gb",
    },
    {
        id: "n7wrhlu",
        body: "You could measure the power draw and calculate how large battery capacity you need to sustain the light through the dark period. For instance, if the light draws 1A and the period without sunlight is 12 hours, you need a battery capacity of 12Ah to sustain light. Likewise, your solar panel needs to be capable of charging the battery enough during the sunlight period. This gets complicated as sunlight varies through the year, but it's a fun (?) exercise to try and predict the needs.",
        parentId: null,
        postId: "1mm96gb",
    },
    {
        id: "n7z0az1",
        body: "I'll give that a try, luckily it's pretty low stakes if it doesn't last all night since it's just a decoration.",
        parentId: "n7wrhlu",
        postId: "1mm96gb",
    },
    {
        id: "n7z0wvd",
        body: "Fair enough. All you really need is for it to charge a tad more than it drains, that way it'll be self sufficient.. Feel free to shout out if you need some help with the calculations and\/or measurements 😊",
        parentId: "n7z0az1",
        postId: "1mm96gb",
    },
    {
        id: "n86j4xs",
        body: "I have a circuit designed for driving a bunch of these in a multiplexed arrangement, but the sheer number of pins and the orientation makes them a pain to work with on a breadboard. Fortunately, I have some large-ish protoboards arriving soon.",
        score: -1,
        parentId: null,
        postId: "1mnplmj",
    },
    {
        id: "n7lrjua",
        body: "That’s awesome.\n\nWhere did you get the 2 sticks from. I’ve not seen the before. Link?",
        parentId: null,
        postId: "1mkki84",
    },
    {
        id: "n7je48o",
        body: "You keep posting intriguing builds, a photo and basics. \n\nIt’d be nice to share details.  Video of it working.  Photos during the build.  Internals.  Etc.",
        parentId: null,
        postId: "1mkk822",
    },
    {
        id: "n7jk3yt",
        body: "Put pressure sensors under your mattress",
        parentId: null,
        postId: "1mkk822",
    },
    {
        id: "n7k1t42",
        body: "How has humanity gone on without one of these. I'd like to see the device eject you out of the bed.",
        parentId: null,
        postId: "1mkk822",
    },
    {
        id: "n7v9ibo",
        body: "[https:\/\/youtu.be\/EVzn1pl4nlo](https:\/\/youtu.be\/EVzn1pl4nlo)",
        parentId: "n7k1t42",
        postId: "1mkk822",
    },
    {
        id: "n7jphb1",
        body: "Interesting. I wonder if one can use an mmwave presence sensor (LD2410) too. Set it to keep checking every 15 - 30 seconds and the alarm keeps getting louder each time. Lol.",
        parentId: null,
        postId: "1mkk822",
    },
    {
        id: "n7jfh9s",
        body: "Why can’t it detect when u get out of bed",
        parentId: null,
        postId: "1mkk822",
    },
    {
        id: "n7k7bun",
        body: "is that you sneakysquid",
        parentId: null,
        postId: "1mkk822",
    },
    {
        id: "n7kbamv",
        body: "Wowwww",
        parentId: null,
        postId: "1mkk822",
    },
    {
        id: "n7kc2tj",
        body: "My only question: where do you get the case?",
        parentId: null,
        postId: "1mkk822",
    },
    {
        id: "n7mujnk",
        body: "They said they 3D printed it.",
        parentId: "n7kc2tj",
        postId: "1mkk822",
    },
    {
        id: "n7kpgp3",
        body: "Congratulations!",
        parentId: null,
        postId: "1mkk822",
    },
    {
        id: "n7kzego",
        body: "This is diabolical. I want one to help deal with my ADHD brain.",
        parentId: null,
        postId: "1mkk822",
    },
    {
        id: "n7lbj3s",
        body: "Wouldn't work for me. I'd just throw it across the room and break it. I need a bed that vibrates and\/or uses a hydraulic lift to dump me outta bed. 🫡",
        parentId: null,
        postId: "1mkk822",
    },
    {
        id: "n7moh2o",
        body: "I guess I'm napping on the couch now!",
        parentId: null,
        postId: "1mkk822",
    },
    {
        id: "n7nywkd",
        body: "I have the perfect tool to silence it thanks to the pink panther for inspiring me!😂🤣😂🤣\n\n\n[Pink Panther vs Alarm Clock](https:\/\/youtu.be\/Y3FEZJz55T0?feature=shared)",
        parentId: null,
        postId: "1mkk822",
    },
    {
        id: "n7ozq17",
        body: "Like one of those alarms clocks that rolls around on the floor",
        parentId: null,
        postId: "1mkk822",
    },
    {
        id: "n81jqz9",
        body: "Sinister",
        parentId: null,
        postId: "1mkk822",
    },
    {
        id: "n7jy1xn",
        body: "I see this being something people gove as a gift and get used one time, and then either thrown in the teash or stored out in the garage until its sold at a yard sale for fifty cents because the guy couldnt just pay you the dollar you wanted. And you would have gladly given it to the guy to get the half hour of your life he wasted with his lowball offers and him walking away only to come back again 5 minutes later with a slightly higher offer. Which of course you counter offer a price somewhere in the middle and he walks away again, pretending to look at your other items for sale. Like fuck man just take the damn thing for free. What so you mean ithat wouldnt be right? The dollar was too expensive, but free is too cheap. Fine give me 50 cents or im smashing it with the sledgehammer you said you'd be interested in if it was a 4 pounder instead of the 8 pound hammer that it is.... \nyeah your alarm clock is primed to bring more annoyance into peoples lives that they never knew they needed.",
        score: -4,
        parentId: null,
        postId: "1mkk822",
    },
    {
        id: "n7k1woq",
        body: "Amazing word salad. Wtf",
        parentId: "n7jy1xn",
        postId: "1mkk822",
    },
    {
        id: "n7mu86f",
        body: "So, I'm getting the impression that you're not a fan of their alarm clock.",
        parentId: "n7jy1xn",
        postId: "1mkk822",
    },
    {
        id: "n897kxb",
        body: "\/r\/spam is leaking.",
        parentId: null,
        postId: "1mn8fou",
    },
    {
        id: "n7yqxvu",
        body: "Love this, I’d like to make this a project for my students to build.",
        parentId: null,
        postId: "1mmm19m",
    },
    {
        id: "n7zf4nq",
        body: "How accurate are those shtcx sensors for humidity?  I tried some dht22 sensors and the were 15% to 20% higher than actual.",
        parentId: null,
        postId: "1mmm19m",
    },
    {
        id: "n7zlp8f",
        body: "Hard to say as I don't have a good reference.\n\nI have tried many different sensors, I like the i2c based ones from a wiring and programming pov.  I do have a dht22 on my v1 unit.",
        parentId: "n7zf4nq",
        postId: "1mmm19m",
    },
    {
        id: "n8c2sb9",
        body: "Wtf am I looking at. \nI can't wrap my mind around your shady wiring. Please fix it, or make a schematic, because it's a mess.\n\nYour wiring is most certainly the culprit, because everything feels wrong in this post.\nAnd please, red is +, black is gnd.\nAnd no subwoofer should ever be wired in serie with anything.",
        parentId: null,
        postId: "1mogr0y",
    },
    {
        id: "n8c5z7l",
        body: "The wiring isn’t clear, however if by coil OP means a crossover coil, and he’s such is wired in series.  Subs can and often are wired in series.  Common is four subs, two 4ohm in series for 8ohms - then each pair at 8ohms in parallel for final impedance of 4ohms.  All assuming a mono amp (or bridged to mono) \n\nOP, try removing the coil and wire straight from the amp to the sub.  If it’s a sine wave there is no need.  If it plays then sort from there.",
        parentId: "n8c2sb9",
        postId: "1mogr0y",
    },
    {
        id: "n8c4mq8",
        body: " Well first i would start by connecting a PC or something with an actual audio output and then going on from there",
        parentId: null,
        postId: "1mogr0y",
    },
    {
        id: "n8bgigj",
        body: "12MP is more than 4K, I'm assuming it's near 5K.\n\nYou want to record 5-8 5K resolution videos at the same time, even 1 5K ain't happening on a pi lol. You need some 11th gen intel 2+tflops graphics or newer PC with QSV and absolutely no idea what cameras you're using so if they are usb it better be usb 3.0 and some pcie card with multiple usb 3.0 ports. If they are HDMI cameras you'll need a lot of capture cards.",
        parentId: null,
        postId: "1mobocg",
    },
    {
        id: "n8brt5g",
        body: "Oh ok lol. \nAnd there's no way to buffer one-two mimutes on the camera? \n\n\nThanks!",
        parentId: "n8bgigj",
        postId: "1mobocg",
    },
    {
        id: "n8amnwi",
        body: "I would definitely look into that mixer and see if it is grounded or not. Not just for audio but for safety reasons.\n\nHave you tried another mic into the mixer? Does your electret mic need phantom power?",
        parentId: null,
        postId: "1mo9wmy",
    },
    {
        id: "n8aowr1",
        body: "so.. i do not believe the mixer is grounded as it has a Japanese wall plug (type A) and then it's conected to a 220v to 110v converter which is not connected to the ground pin on the wall outlet. \n\ni have tried to use it on another mixer but that one's power supply also only has a 2 prong connector (type C) and does not connect to the ground pin (we use a type H in my country)\n\nand yes it does require phantom power\n\ni don't own any other condenser or electret mics so i cant test another mic with it but when i use a dynamic microphone there is no noise",
        parentId: "n8amnwi",
        postId: "1mo9wmy",
    },
    {
        id: "n8fa1qs",
        body: "Looks like the circuit does not provide the mic capsule with the bias voltage.",
        parentId: null,
        postId: "1mo9wmy",
    },
    {
        id: "n8cg7a1",
        body: "Yes, the P terminals are for charging\/discharging",
        parentId: null,
        postId: "1moivuf",
    },
    {
        id: "n8chjlr",
        body: "Ty!",
        parentId: "n8cg7a1",
        postId: "1moivuf",
    },
    {
        id: "n8f6q62",
        body: "Make sure to add some kind of insulator between the BMS and your cells. Just additional protection for vibration.",
        parentId: null,
        postId: "1moivuf",
    },
    {
        id: "n89ikrs",
        body: "[Audio Power Amplifier Design Handbook - Doug Self](https:\/\/nick.desmith.net\/Data\/Books%20and%20Manuals\/Self%20-%20Audio%20Power%20Amp%20Design%20Handbook%204th%20Edn.pdf)\n\nI’d highly recommend his other books, too.\n\nIf you want make speaker drivers, check Joppe Peelen and Polymate3D on YouTube.\n\nIf you want to make speaker enclosures, check  KirbyMeetsAudio also on YT.",
        parentId: null,
        postId: "1mnu5ms",
    },
    {
        id: "n8gxt02",
        body: "Thank you for your contribution, greetings!!!",
        parentId: "n89ikrs",
        postId: "1mnu5ms",
    },
    {
        id: "n87gvhy",
        body: 'El autor es , "newton C. Bragas".',
        parentId: null,
        postId: "1mnu5ms",
    },
    {
        id: "n8d96jd",
        body: "For starters remove unneeded components, better layout maby go double sided. There is smaller ics that require less support components and smaller caps and inductors because of a higher switching frequency \n\nIf you must use this ic, read the datasheet and see if you can find design reccomendations\n\nHigher switching frequencies do cause more heat btw, and also more EMI issues (usually)\n\nAlso you can use 0402 resistors for the feedback circuit instead of the ones on the board in the pic",
        parentId: null,
        postId: "1moj4h5",
    },
    {
        id: "n8emd4c",
        body: "Does your ESP32 DEV board really handle 12 volts? Mine don't. You could use a voltage regulator or buck converter to drop the 12V down to 5V. Be sure to input the 5 volts to the Vin pin so the on-board regulator can power the ESP with the 3.3 volts it needs.\n\nAlso, the output level of the ESP32 GPIO pins is 3.3v. Your relay module may operate with that logic level, you'll have to test it. If it doesn't, you can use a level converter.\n\nAnd there must be a ground connection on those relay modules somewhere to reference the logic input levels.",
        parentId: null,
        postId: "1moiewo",
    },
    {
        id: "n8f668s",
        body: "Seem like you only run the pump in 1 direction. To minimize noise into the 12 input of your dev board, you can:  \n\\+Add Schottky diode across each pump terminal. This is call fly-back diode. Even your motor is 2A continous, do you do not need 2A continuous diode. 1A or 0.5A would do, as the diode will see a brief 2A current that die down very quickly\n\n\\+Add bulk capacitor at your 12V input. Standard would be around 100uF or 47uF electrolytic type + 10uF or less of ceramic. Some people would recommend pi filter (capacitor -&gt; inductor -&gt; capacitor) but I don't think you need this much filter.\n\n\\+Use an LDO (low dropout regulator) to provide power to your MCU. So you can buck from 12V to 5V, then 5V LDO to 3.3V. LDO is pretty good at filtering out input noise.\n\nYou should be more than rock solid with the power rail. If you are worry about the motor wire is making your Wifi stop working, you can twist the motor wire together too.",
        parentId: null,
        postId: "1moiewo",
    },
    {
        id: "n7e9vfu",
        body: "(I had no idea what Lorex system was until today. Everything below is what I have just found, so please excuse there may exist incorrect info)\n\nAssumed it's a Lorex system, as per this manual.\n\n[https:\/\/www.lorextechnology.com\/downloads\/discontinued-b4-2010\/SY5S1021C-A-english.pdf.R1.pdf](https:\/\/www.lorextechnology.com\/downloads\/discontinued-b4-2010\/SY5S1021C-A-english.pdf.R1.pdf)\n\nIf so, then the 6PIN DIN should be Lorex-compatible one. I found its pin layout too. See p3 of this:\n\n[https:\/\/web.archive.org\/web\/20120227062434\/http:\/\/lorexstore.lorextechnology.com\/uploads\/CABLES\\_GUIDE\\_EN\\_R2.pdf](https:\/\/web.archive.org\/web\/20120227062434\/http:\/\/lorexstore.lorextechnology.com\/uploads\/CABLES_GUIDE_EN_R2.pdf)\n\nBut I saw some comments like those DIN layouts may vary over different models. So be careful. I'd test the +12V first with a multimeter for peace of mind.\n\nThere seem to be breakout cables available in the market, which could be another option for you. [https:\/\/www.svideo.com\/6pinbreakout.html](https:\/\/www.svideo.com\/6pinbreakout.html)\n\nAs per the camera spec from the manual I shared above, \"320 H X 240 V 2:1 interlace Horizontal 400 TV Lines 1.0Vp-p\/75 Ohm\", it feels promising to do what you mentioned. Maybe it's even more fun to connect an NTSC camera or another classic console to the port 2 so that you can switch on-the-fly..\n\nr\/Crt , r\/cctv are also worth visiting I think.\n\nGood luck, and have fun!",
        parentId: null,
        postId: "1mjtnxq",
    },
    {
        id: "n89s6yh",
        body: "[WireViz](https:\/\/github.com\/wireviz\/WireViz) is a free software you can use for wiring diagram and wiring harnesses.",
        parentId: null,
        postId: "1mo42zc",
    },
    {
        id: "n8fm0sd",
        body: "Thanks! ",
        parentId: "n89s6yh",
        postId: "1mo42zc",
    },
    {
        id: "n8ac2yi",
        body: "KiCad is pretty good.",
        parentId: null,
        postId: "1mo42zc",
    },
    {
        id: "n8accqh",
        body: "And if you ever want to do pcbs you already know how to do the first step.",
        parentId: "n8ac2yi",
        postId: "1mo42zc",
    },
    {
        id: "n8abqzb",
        body: "I use pencil and paper. The classic green Tops engineering pad with grid on the back. I have a really big eraser.",
        parentId: null,
        postId: "1mo42zc",
    },
    {
        id: "n8ep4ox",
        body: "I use [Express\\_SCH](https:\/\/expresssch.apponic.com\/).",
        parentId: null,
        postId: "1mo42zc",
    },
    {
        id: "n8fm1uq",
        body: "Thanks ",
        parentId: "n8ep4ox",
        postId: "1mo42zc",
    },
    {
        id: "n89fn5z",
        body: "I would also like to know this. I was using PCBs though and ended up printing grids of holes to just draw on. Must be a better way",
        parentId: null,
        postId: "1mo42zc",
    },
    {
        id: "n88k8ep",
        body: "Order DIP parts.",
        parentId: null,
        postId: "1mnswqh",
    },
    {
        id: "n894cpe",
        body: "You wrote the answer. At digikey\/mouser, look for 'sop14 breakout'.",
        parentId: null,
        postId: "1mnswqh",
    },
    {
        id: "n89n0hi",
        body: "The thing that breaks the pins out is, not too surprisingly, often called a breakout board!",
        parentId: null,
        postId: "1mnswqh",
    },
    {
        id: "n8cawhx",
        body: "https:\/\/www.digikey.com\/en\/products\/detail\/chip-quik-inc\/PA0003\/5014719 PA0003 Chip Quik Inc. | Prototyping, Fabrication Products | DigiKey",
        parentId: null,
        postId: "1mnswqh",
    },
    {
        id: "n8hz438",
        body: "What kind of motorcycle uses that?",
        parentId: null,
        postId: "1mns4aj",
    },
    {
        id: "n861av4",
        body: "Not the best subreddit but NPN fuzz face is pretty common.",
        parentId: null,
        postId: "1mnn1q9",
    },
    {
        id: "n86p8nm",
        body: "Germaniums ftw",
        parentId: null,
        postId: "1mnn1q9",
    },
    {
        id: "hyau5bx",
        body: "I have seen this work OK for programming, but the only way to know for sure it to try it. Worst case you might have to add the LEDs (or resistors) after programming.",
        parentId: null,
        postId: "t0jrvd",
    },
    {
        id: "n7wrs8f",
        body: "Easy way, buy medium size solar garden lamp, tear down it and connect wires to C type batteries. \n\nMedium size lamps, works with a 3.7 volts battery, if you have electronics experience, you can add some resistor to protect leds lights.\n\nHard way, buy 5 volts solar panel, tp4056 charging module, 18650 li-ion battery, some 220 and 330 ohm resistor, battery holder, some diodes (1n4001), switch button, if you want dimme lights, some photo resistor and put in al together.",
        parentId: null,
        postId: "1mm96gb",
    },
    {
        id: "n7w3ayr",
        body: "That's actually a great idea. Just make sure you use (the right kind of) rechargeable batteries, so the same kind as the solar light.\n\n\nThe size doesn't really matter. It only determines how much charge they can hold. You can even replace the C-cells with AA-cells and some padding if you want. \n\n\nYou're right that a C-cell could also provide a bit more current, but for this application that doesn't matter.",
        parentId: null,
        postId: "1mm96gb",
    },
    {
        id: "n8cg8d6",
        body: "No.  I was thinking that myself.  I saw some videos that a Pakistani was making.  He used his own circuits home made and turn it into a LED1 with varying colors.  Similar to this traffic lights.  Because he’s the engineer.  Do you consider yourself the engineer?  I figure I don’t get paid, so I don’t undertake this kinds of projects.  Just simple breadboard projects and stuffs is enough for me.",
        parentId: null,
        postId: "1mm96gb",
    },
    {
        id: "n8917ve",
        body: "There are little adapter boards for various SMD footprints.  EBay, aliexpress, amazon, Akihabara",
        parentId: null,
        postId: "1mnswqh",
    },
    {
        id: "n8crgm8",
        body: 'Search "PCB SOP14 DIP". SOP14 is supereasy to solder even with a crappy soldering iron.',
        parentId: null,
        postId: "1mnswqh",
    },
    {
        id: "n8j29fm",
        body: "Escooter throttle are usually analog hall effect sensors. They have a 3 wire +- and signal. They usually operate at 4.2 volt but 5 volt should work. You can use pretty much as replacement for a potentiometer.",
        parentId: null,
        postId: "1mns4aj",
    },
    {
        id: "n871xm9",
        body: "Here's a breakdown.  1\/2 thru the video, he actually gets it open.\n\nAt first I was thinking it's a conditioner and isolator, but maybe not.\n\nhttps:\/\/www.youtube.com\/watch?v=YMV9aQI5xCA\n\nI picked up a large Home Theater power supply thing and it isolates all the power supplies so that you don't get noise in your home theater system. It has some remote control outlets and delay outlets on it.\n\nI think there's logic to turn one on before the other and other features.\n\nNot sure what a person would use it for.  Maybe outside light timer or turn something on remotely.\n\nIf it isolates (I don't think it does, that would be a larger transformer) then you can have conditioned, isolated power for whatever reason, like audio equipment.",
        parentId: null,
        postId: "1mnqkbv",
    },
    {
        id: "n86pv3l",
        body: "These are very bright when you run them continuously, but the assumption is you will multiplex them.  They will then be quite a bit dimmer due to reduced the duty cycle.",
        parentId: null,
        postId: "1mnplmj",
    },
    {
        id: "n86z4xq",
        body: "Yep. I think 16:1 is totally do-able for these, without having to overdrive them at all.",
        parentId: "n86pv3l",
        postId: "1mnplmj",
    },
    {
        id: "n893xqp",
        body: "Something so simple yet so satisfying. I still remember the first time I ever made a light glow and I felt like I had cracked the code of the universe.",
        parentId: null,
        postId: "1mnplmj",
    },
    {
        id: "n88j4aj",
        body: "🇬🇧",
        parentId: null,
        postId: "1mnplmj",
    },
    {
        id: "n87ocy9",
        body: "Get a resistor array. It comes in a DIP package.",
        parentId: null,
        postId: "1mnplmj",
    },
    {
        id: "n87w5dn",
        body: "I don't know why I didn't think of that in the initial design, but yes, definitely gonna need those for the next version. ",
        parentId: "n87ocy9",
        postId: "1mnplmj",
    },
    {
        id: "n8akyzo",
        body: "Ahh this brings back memories of my youth when 40+ years ago in PHYS 340--electronics, I was wiring up now primitive seven segment LEDs!",
        parentId: null,
        postId: "1mnplmj",
    },
    {
        id: "n8cfwsc",
        body: "17-segment.\n\nThe radix counts too.\n\nUsually, these would be housed behind a layer of smoked plastic, so the circuit board and what-not can't be seen, so their intense brightness is actually deliberate, so they look normal brightness through the smoked plastic.",
        parentId: null,
        postId: "1mnplmj",
    },
    {
        id: "n8cjl32",
        body: "These are traditionally called 16-segment displays, just like the more common digit-only ones are referred to as 7-segment displays. \n\nThat extra LED is in fact a bit of an annoyance, since driving all of these LEDs needs 17 outputs, which isn't a common configuration to find in either current source or current sink chips. Even dedicated LED driver chips usually max out at 16 outputs, which leaves the decimal points as something you have to hack in, somehow.",
        parentId: "n8cfwsc",
        postId: "1mnplmj",
    },
    {
        id: "n8h2a2v",
        body: "17-segment display system I just had to modify code for, there's a global current sink pin that controls over-all brightness. The individual segments are just microcontroller pins mapped to circuit traces. 16, 17, pins. They're just fed through a bank of binary shift registers that whatever power driver circuitry they need. An individual row of characters is strobed left to right, so I don't need a dedicated driver pin going to each individual character.",
        parentId: "n8cjl32",
        postId: "1mnplmj",
    },
    {
        id: "n8i7evp",
        body: "This is ultimately going to be driven by shift register outputs. I'll need 3 8-bit shift registers to get 17 segment drive signals and 7 common signals. \n\nThe commons will go through a transistor to ground (these are common-cathode displays), and the segments can, I think, be driven directly through the SR outputs. It will still need a current limiting resistor per-segment. I looked into one resistor per display, and some letters would be noticeably dimmer than others in that configuration.",
        parentId: "n8h2a2v",
        postId: "1mnplmj",
    },
    {
        id: "n8i9bu2",
        body: "Oh yeah. You have no guarantees in the manufacturing process that one segment's not gonna come out slightly brighter or slightly dimmer than the rest. I would always bank on needing to use discrete current limitting resistors per segment to even out the intensities of all segments.\n\nAnd the base of that common transistor goes to a micro PWM output, right? That's how you have global software dimming.",
        parentId: "n8i7evp",
        postId: "1mnplmj",
    },
    {
        id: "n88h226",
        body: "This is my kind of clusterf*ck",
        parentId: null,
        postId: "1mnplmj",
    },
    {
        id: "n88qjnq",
        body: "max7219 is the answer",
        parentId: null,
        postId: "1mnplmj",
    },
    {
        id: "n8akelp",
        body: "If there are two isolated commons.\n\nSingle common cathode can be  driven by a 16K33.\nI have a display of 8x14 segment bubble display, on a 16K33.\nAdafruit have a library that does such a display. So far, it is a solution looking for a problem.",
        parentId: "n88qjnq",
        postId: "1mnplmj",
    },
    {
        id: "n8b9u92",
        body: "Yes, 16k33 is probably the best off the shelf solution for this. It's definitely the simplest for a small number of digits. There are some things about the design that aren't great for some of my intended applications, but it's more than adequate for most of them. And a massive savings in parts and annoyance to wire up. I should give it a try, at least.",
        parentId: "n8akelp",
        postId: "1mnplmj",
    },
    {
        id: "n8c6dsf",
        body: "*Rule Britania intensifies*",
        parentId: null,
        postId: "1mnplmj",
    },
    {
        id: "n8df1gv",
        body: "I recently got a radio with a 14 segment display, paying attention for the first time to the fact that &gt;7 segment displays exist, and I'm in love. This 16 seg is gorgeous.",
        parentId: null,
        postId: "1mnplmj",
    },
    {
        id: "n8dgj7j",
        body: 'I really do love how they look, and am justifying using them in a small project as "practice" for using them in my worst idea ever, which will ultimately need about 1000 of them. ',
        parentId: "n8df1gv",
        postId: "1mnplmj",
    },
    {
        id: "n8f7xrd",
        body: "One thing called part datasheet will give you all the details needed for a safe and long usage.",
        parentId: null,
        postId: "1mnplmj",
    },
    {
        id: "iwqqmvx",
        body: "If your device can remap the pins, try using them for the UART by adding 1K inline resistors to the USB-UART chip and directly connect the PIC pins to the debug header. The resistors allow the debug to take priority when needed and you have a UART when it's not needed.",
        parentId: "iueqkwq",
        postId: "yhhnqw",
    },
    {
        id: "iwstghy",
        body: "Are you talking about Microchip Pics ?",
        parentId: "iwqqmvx",
        postId: "yhhnqw",
    },
    {
        id: "iwt5ai1",
        body: "Yep, Microchip PIC.",
        parentId: "iwstghy",
        postId: "yhhnqw",
    },
    {
        id: "iwz2ey8",
        body: "I use 18F4525 mostly. If I am not wrong pins are fixed.",
        parentId: "iwt5ai1",
        postId: "yhhnqw",
    },
    {
        id: "ix8mr6e",
        body: "&gt;18F4525\n\nLooks like they are fixed there. I've been doing a lot of work with newer Q type parts, especially the 18F25Q10. Been spoiled with the peripheral pin select letting me remap most pins.",
        parentId: "iwz2ey8",
        postId: "yhhnqw",
    },
    {
        id: "ixa4wj3",
        body: "&gt;Cool feature. I admit. But no stock !",
        parentId: "ix8mr6e",
        postId: "yhhnqw",
    },
    {
        id: "iueuyij",
        body: "look for page 53 on the datasheet.",
        parentId: null,
        postId: "yhhnqw",
    },
    {
        id: "hxd2uge",
        body: "[deleted]",
        parentId: null,
        postId: "suz88h",
    },
    {
        id: "hxdox89",
        body: "That did the trick.  Thank you!",
        parentId: "hxd2uge",
        postId: "suz88h",
    },
    {
        id: "ixv2ny1",
        body: 'A "C" compiler called "CC5X" is command line and is one of my favorites easy to use and has been around since the 1990\'s.',
        parentId: null,
        postId: "suz88h",
    },
    {
        id: "i4g7gib",
        body: "Note there is also an IPE that is installed with MPLAB X that is a programmer only environment.",
        parentId: null,
        postId: "suz88h",
    },
    {
        id: "hw6d60n",
        body: "Simplest non data way would be just a wire between them.\nBest way would be to leave them both open collector and use a pull up and either one could pull it low to send the other a timing pulse.\n\nBut looking at the data you have 2 USARTs available.\nUsing those to create a half duplex connection would be fairly trivial.\nConnect Rx and tx together on both chips, then between them with one wire. You would need to disable the transmitter driver when not required so though it’s not strictly necessary add a pull up to maintain the idle state when neither is transmitting.",
        parentId: null,
        postId: "snwodg",
    },
    {
        id: "hyauild",
        body: "Note that you will also need a 2nd wire for a ground reference.",
        parentId: null,
        postId: "snwodg",
    },
    {
        id: "icb7jw6",
        body: "1-wire works great long distance. Check out maxims AN148.",
        parentId: null,
        postId: "snwodg",
    },
];
