const { topics } = require("./topics");
const { redditPosts } = require("./reddit-posts");
const { comments: redditComments } = require("./reddit-comments");
const fs = require("fs");
const path = require("path");

require("dotenv").config();

// Parse command line arguments
const args = process.argv.slice(2);
const topicOnly = args.includes("--topic-only") || args.includes("-t");
const help = args.includes("--help") || args.includes("-h");
const concurrencyIndex = args.findIndex(
    (arg) => arg === "-c" || arg === "--concurrency",
);
const concurrency =
    concurrencyIndex !== -1 && args[concurrencyIndex + 1]
        ? parseInt(args[concurrencyIndex + 1])
        : 1;

// Auth credentials - read from .env file
const USERNAME = process.env.ADMIN_EMAIL;
const PASSWORD = process.env.ADMIN_PASS;

if (help) {
    console.log(`
Usage: npx ts-node fetch-baseline.ts [options]

Options:
  -t, --topic-only      Only output content and topics (minimal output)
  -c, --concurrency <number>           Number of concurrent requests (default: 1)
  -h, --help            Show this help message

Examples:
  npx ts-node baseline-test/fetch-baseline.ts                    # Full analysis, 1 concurrent
  npx ts-node baseline-test/fetch-baseline.ts --topic-only       # Only content and topics, 1 concurrent
  npx ts-node baseline-test/fetch-baseline.ts -c 3               # Full analysis, 3 concurrent
  npx ts-node baseline-test/fetch-baseline.ts -t -c 2            # Topic only, 2 concurrent
`);
    process.exit(0);
}

// Function to check if JWT token is expired
function isTokenExpired(token: string): boolean {
    try {
        const payload = JSON.parse(
            Buffer.from(token.split(".")[1], "base64").toString(),
        );
        const currentTime = Math.floor(Date.now() / 1000);
        const expirationTime = payload.exp;
        return currentTime >= expirationTime;
    } catch (error) {
        console.log("🔍 Debug: Could not parse token payload:", error.message);
        return false;
    }
}

// Function to get JWT token
async function getAuthToken() {
    try {
        console.log("🔐 Authenticating...");
        const response = await fetch("http://localhost:3000/api/login", {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify({
                username: USERNAME, // Note: uses 'username' field, not 'email'
                password: PASSWORD,
            }),
        });

        if (!response.ok) {
            throw new Error(
                `Login failed: ${response.status} ${response.statusText}`,
            );
        }

        // Extract the auth cookie from the response
        const setCookieHeader = response.headers.get("set-cookie");

        if (!setCookieHeader) {
            throw new Error("No auth cookie received");
        }

        // Parse the auth cookie to get the token
        const authCookie = setCookieHeader
            .split(",")
            .find((cookie) => cookie.trim().startsWith("auth="));

        if (!authCookie) {
            throw new Error("Auth cookie not found in response");
        }

        const token = authCookie.split(";")[0].replace("auth=", "");
        console.log("✅ Authentication successful");
        return token;
    } catch (error) {
        console.error("❌ Authentication failed:", error.message);
        console.log(
            "💡 Make sure your API server is running and credentials are correct",
        );
        console.log(
            "💡 You can set AUTH_USERNAME and AUTH_PASSWORD environment variables",
        );
        process.exit(1);
    }
}

async function main() {
    // Get authentication token first
    let authToken = await getAuthToken();

    const results = [];
    const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
    const outputDir = path.join(__dirname, "results");

    // Create output directory if it doesn't exist
    if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
    }

    const filename = `analysis-${topicOnly ? "topic" : "full"}-${timestamp}.json`;
    const filepath = path.join(outputDir, filename);

    console.log(
        `Processing ${redditPosts.length} posts with ${concurrency} concurrent requests`,
    );
    console.log(`Results will be saved to: ${filepath}`);
    console.log("---");

    // Process posts in chunks based on concurrency
    for (let i = 0; i < redditPosts.length; i += concurrency) {
        const chunk = redditPosts.slice(i, i + concurrency);
        const chunkPromises = chunk.map(async (post) => {
            // Find comments for this post
            const postComments = redditComments.filter(
                (comment) => comment.postId === post.id,
            );

            console.log(`Analyzing: "${post.title}"`);
            console.log(
                `Found ${postComments.length} comments for post ${post.id}`,
            );

            try {
                const requestBody = {
                    id: post.id,
                    title: post.title,
                    text: post.text,
                    comments: postComments,
                    topics: topics,
                };

                // Check if token is expired
                if (isTokenExpired(authToken)) {
                    console.log("⚠️  Token expired, re-authenticating...");
                    const newToken = await getAuthToken();
                    if (newToken) {
                        authToken = newToken;
                        console.log("✅ Re-authenticated successfully");
                    } else {
                        throw new Error("Failed to re-authenticate");
                    }
                }

                const response = await fetch(
                    "http://localhost:3000/api/analysis",
                    {
                        method: "POST",
                        headers: {
                            "Content-Type": "application/json",
                            Cookie: `auth=${authToken}`,
                        },
                        body: JSON.stringify(requestBody),
                    },
                );

                if (!response.ok) {
                    throw new Error(
                        `HTTP ${response.status}: ${response.statusText}`,
                    );
                }

                const result = await response.json();

                if (topicOnly) {
                    console.log(
                        `✓ Topic analysis completed for "${post.title}"`,
                    );
                    console.log(`  Topics: ${result.topics || "None"}`);

                    // For topic-only mode, return simplified structure
                    return {
                        id: post.id,
                        input: result.input,
                        topics: result.analysis?.topics || "",
                    };
                } else {
                    console.log(
                        `✓ Full analysis completed for "${post.title}"`,
                    );
                    console.log(
                        `  Sentiment: ${result.sentiment?.score || "N/A"}`,
                    );
                    console.log(
                        `  Relevance: ${result.relevance || "N/A"}/100`,
                    );

                    // For full mode, return complete structure
                    return {
                        id: post.id,
                        title: post.title,
                        timestamp: new Date().toISOString(),
                        success: true,
                        result: result,
                    };
                }
            } catch (error) {
                console.error(
                    `✗ Error analyzing "${post.title}": ${error.message}`,
                );

                if (topicOnly) {
                    // For topic-only mode, return simplified error structure
                    return {
                        id: post.id,
                        input: `# ${post.title}\n\n${post.text}${postComments.length > 0 ? "\n\n## Comments\n\n" + postComments.map((c) => `* ${c.body}`).join("\n\n") : ""}`,
                        topics: "",
                        error: error.message,
                    };
                } else {
                    // For full mode, return complete error structure
                    return {
                        id: post.id,
                        title: post.title,
                        success: false,
                        error: error.message,
                    };
                }
            }
        });

        // Wait for current chunk to complete
        const chunkResults = await Promise.all(chunkPromises);
        results.push(...chunkResults);

        // Write results to file after each chunk
        const output = {
            metadata: {
                timestamp: new Date().toISOString(),
                mode: topicOnly ? "topic" : "full",
                concurrency: concurrency,
                totalPosts: redditPosts.length,
                processedPosts: results.length,
                successfulAnalyses: topicOnly
                    ? results.filter((r) => !r.error).length
                    : results.filter((r) => r.success).length,
                failedAnalyses: topicOnly
                    ? results.filter((r) => r.error).length
                    : results.filter((r) => !r.success).length,
            },
            results: results,
        };

        fs.writeFileSync(filepath, JSON.stringify(output, null, 2));
        console.log(
            `Chunk completed. Total processed: ${results.length}/${redditPosts.length}`,
        );
        console.log(`Progress saved to: ${filepath}`);
        console.log("---");
    }

    // Final summary
    console.log("\n=== FINAL SUMMARY ===");
    console.log(`Total posts processed: ${results.length}`);
    if (topicOnly) {
        console.log(
            `Successful analyses: ${results.filter((r) => !r.error).length}`,
        );
        console.log(
            `Failed analyses: ${results.filter((r) => r.error).length}`,
        );
    } else {
        console.log(
            `Successful analyses: ${results.filter((r) => r.success).length}`,
        );
        console.log(
            `Failed analyses: ${results.filter((r) => !r.success).length}`,
        );
    }
    console.log(`Results saved to: ${filepath}`);
}

main().catch(console.error);
