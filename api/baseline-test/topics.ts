export const topics = [
    {
        name: "Semiconductor Industry",
        description: "The semiconductor industry as a whole",
    },
    {
        name: "32-Bit Microcontrollers",
        description: "32-Bit Microcontrollers",
    },
    {
        name: "Infineon",
        description: "Infineon, the semiconductor company",
    },
    {
        name: "Microchip Technology Inc",
        description:
            "the company, this is not the hardware generally (microchips, etc)",
    },
    {
        name: "Keeloq & Security Devices",
        description: "Keeloq & Security Devices",
    },
    {
        name: "Bluetooth",
        description: "Bluetooth",
    },
    {
        name: "802.15.4/ZigBee",
        description: "802.15.4/ZigBee",
    },
    {
        name: "802.11/Wi-Fi",
        description: "802.11/Wi-Fi",
    },
    {
        name: "Internet of Things-Wireless",
        description: "Internet of Things-Wireless",
    },
    {
        name: "Functional Safety",
        description: "Functional Safety",
    },
    {
        name: "FPGA",
        description: "FPGA",
    },
    {
        name: "dsPIC33<PERSON>",
        description: "dsPIC33F",
    },
    {
        name: "dsPIC30<PERSON>",
        description: "dsPIC30<PERSON>",
    },
    {
        name: "dsPIC® DSCs",
        description: "dsPIC® DSCs",
    },
    {
        name: "Programmers (MPLAB PM3, PICSTART Plus, PICkit 2, PICkit 3)",
        description:
            "Programmers (MPLAB PM3, PICSTART Plus, PICkit 2, PICkit 3)",
    },
    {
        name: "MPLAB® Code Configurator",
        description: "MPLAB® Code Configurator",
    },
    {
        name: "MPLAB Xpress",
        description: "MPLAB Xpress",
    },
    {
        name: "MPLAB XC8",
        description: "MPLAB XC8",
    },
    {
        name: "MPLAB XC32",
        description: "MPLAB XC32",
    },
    {
        name: "MPLAB XC16",
        description: "MPLAB XC16",
    },
    {
        name: "MPLAB XC-DSC",
        description: "MPLAB XC-DSC",
    },
    {
        name: "MPLAB X IDE",
        description: "MPLAB X IDE",
    },
    {
        name: "MPLAB Snap",
        description: "MPLAB Snap",
    },
    {
        name: "MPLAB Simulator",
        description: "MPLAB Simulator",
    },
    {
        name: "MPLAB REAL ICE",
        description: "MPLAB REAL ICE",
    },
    {
        name: "MPLAB PICkit Basic",
        description: "MPLAB PICkit Basic",
    },
    {
        name: "MPLAB PICkit 5 Debugger",
        description: "MPLAB PICkit 5 Debugger",
    },
    {
        name: "MPLAB PICkit 4 Debugger",
        description: "MPLAB PICkit 4 Debugger",
    },
    {
        name: "MPLAB ICE 4 Debugger",
        description: "MPLAB ICE 4 Debugger",
    },
    {
        name: "MPLAB ICD4 Debugger",
        description: "MPLAB ICD4 Debugger",
    },
    {
        name: "MPLAB ICD In Circuit Debuggers",
        description: "MPLAB ICD In Circuit Debuggers",
    },
    {
        name: "MPLAB ICD 5 Debugger",
        description: "MPLAB ICD 5 Debugger",
    },
    {
        name: "MPLAB Harmony",
        description: "MPLAB Harmony",
    },
    {
        name: "MPLAB C32 Compiler",
        description: "MPLAB C32 Compiler",
    },
    {
        name: "MPLAB C30 Compiler, ASM30, Link30 forum",
        description: "MPLAB C30 Compiler, ASM30, Link30 forum",
    },
    {
        name: "MPLAB C18 Compiler",
        description: "MPLAB C18 Compiler",
    },
    {
        name: "MPLAB 8 IDE",
        description: "MPLAB 8 IDE",
    },
    {
        name: "MPASM, MPLINK, MPLIB (assembler, linker, librarian)",
        description: "MPASM, MPLINK, MPLIB (assembler, linker, librarian)",
    },
    {
        name: "motorBench™ Development Suite",
        description: "motorBench™ Development Suite",
    },
    {
        name: "Microstick Development Board for dsPIC33F & PIC24H",
        description: "Microstick Development Board for dsPIC33F & PIC24H",
    },
    {
        name: "Microchip Graphics Suite for Linux",
        description: "Microchip Graphics Suite for Linux",
    },
    {
        name: "Microchip Graphics Suite for Harmony",
        description: "Microchip Graphics Suite for Harmony",
    },
    {
        name: "MCAF",
        description: "MCAF",
    },
    {
        name: "MATLAB",
        description: "MATLAB",
    },
    {
        name: "Linux, Open Source Projects and Other Topics",
        description: "Linux, Open Source Projects and Other Topics",
    },
    {
        name: "HI-TECH C Compilers",
        description: "HI-TECH C Compilers",
    },
    {
        name: "Evaluation and Demonstration Boards",
        description: "Evaluation and Demonstration Boards",
    },
    {
        name: "Emulators (MPLAB ICE 2000, MPLAB ICE 4000)",
        description: "Emulators (MPLAB ICE 2000, MPLAB ICE 4000)",
    },
    {
        name: "Curiosity",
        description: "Curiosity",
    },
    {
        name: "CML",
        description: "CML",
    },
    {
        name: "Arriba IDE for PIC MCUs",
        description: "Arriba IDE for PIC MCUs",
    },
    {
        name: "Development Tools",
        description: "Development Tools",
    },
    {
        name: "Serial Real-Time Clocks (RTCC’s)",
        description: "Serial Real-Time Clocks (RTCC’s)",
    },
    {
        name: "Clock Solutions",
        description: "Clock Solutions",
    },
    {
        name: "Clock and Timing",
        description: "Clock and Timing",
    },
    {
        name: "Capacitive Touch Solutions",
        description: "Capacitive Touch Solutions",
    },
    {
        name: "MCP250xx devices",
        description: "MCP250xx devices",
    },
    {
        name: "Analog and Interface Devices",
        description: "Analog and Interface Devices",
    },
    {
        name: "PIC Microcontrollers (PIC10F, PIC12F, PIC16F, PIC18F)",
        description: "PIC Microcontrollers (PIC10F, PIC12F, PIC16F, PIC18F)",
    },
    {
        name: "Peripherals / Core Independent Peripherals -- Waveform Control (CCP/ECCP, PWM, PSMC, COG, CWG, NCO, DSM)",
        description:
            "Peripherals / Core Independent Peripherals -- Waveform Control (CCP/ECCP, PWM, PSMC, COG, CWG, NCO, DSM)",
    },
    {
        name: "Peripherals / Core Independent Peripherals -- User Interface (mTouch, HCVD, LCD)",
        description:
            "Peripherals / Core Independent Peripherals -- User Interface (mTouch, HCVD, LCD)",
    },
    {
        name: "Peripherals / Core Independent Peripherals -- Timing and Measurements (AngTMR, HLT, PSMC, PWM, NCO, SMT, RTCC, TEMP)",
        description:
            "Peripherals / Core Independent Peripherals -- Timing and Measurements (AngTMR, HLT, PSMC, PWM, NCO, SMT, RTCC, TEMP)",
    },
    {
        name: "Peripherals / Core Independent Peripherals -- Logic / Safety & Monitoring / Low Power (CLB, CLC, MULT, MathACC, CRC/SCAN, HLT, WWDT, HEF, PPS, IDLE, DO)",
        description:
            "Peripherals / Core Independent Peripherals -- Logic / Safety & Monitoring / Low Power (CLB, CLC, MULT, MathACC, CRC/SCAN, HLT, WWDT, HEF, PPS, IDLE, DO)",
    },
    {
        name: "Peripherals / Core Independent Peripherals -- Communications (EUSART/AUSART, I2C/SPI, USB, CAN, LIN, KeeLoq)",
        description:
            "Peripherals / Core Independent Peripherals -- Communications (EUSART/AUSART, I2C/SPI, USB, CAN, LIN, KeeLoq)",
    },
    {
        name: "Analog (ADC, Comp, DAC, HC I/O, OPA, PRG, SlopeComp, ZCD)",
        description:
            "Analog (ADC, Comp, DAC, HC I/O, OPA, PRG, SlopeComp, ZCD)",
    },
    {
        name: "Legacy PIC Microcontrollers (PIC14000, PIC17, PICxxC)",
        description: "Legacy PIC Microcontrollers (PIC14000, PIC17, PICxxC)",
    },
    {
        name: "8-Bit Microcontrollers",
        description: "8-Bit Microcontrollers",
    },
    {
        name: "PIC64GX",
        description: "PIC64GX",
    },
    {
        name: "64-Bit Microprocessors",
        description: "64-Bit Microprocessors",
    },
    {
        name: "PIC32",
        description: "PIC32",
    },
    {
        name: "PIC24",
        description: "PIC24",
    },
    {
        name: "16 bit Microcontrollers",
        description: "16 bit Microcontrollers",
    },
];
