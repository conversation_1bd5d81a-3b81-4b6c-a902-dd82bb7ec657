version: '3.8'

services:
  db:
    image: pgvector/pgvector:pg17
    container_name: postgres
    env_file:
      - .env
    environment:
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
      image: redis:latest
      container_name: redis
      ports:
          - "6379:6379"
      command: ["redis-server", "--appendonly", "yes"]

  local-ses:
    image: kamranahmed/local-ses:latest
    ports:
      - "8282:8282"
    container_name: local-ses
    stop_grace_period: 0s

volumes:
  postgres_data:
