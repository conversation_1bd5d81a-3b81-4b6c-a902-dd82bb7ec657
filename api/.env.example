NODE_ENV=development
APP_BASE_URL=http://localhost
DATABASE_URL=postgresql://root:strongPASS@localhost:5432?connection_limit=10
DATABASE_URL_APP_USER=postgresql://app_user:strongPASS@localhost:5432?connection_limit=10
POSTGRES_USER=root
POSTGRES_PASSWORD=strongPASS
ADMIN_USER=admin
ADMIN_PASS=strongPASSWORD9@!
ADMIN_EMAIL=<EMAIL>
JWT_SECRET=do-not-use-this-secret-in-production
NOREPLY_EMAIL=<EMAIL>
SES_ENDPOINT=http://localhost:8282
ENVIRONMENT=test
YOUTUBE_API_KEY=
REDIS_HOST=localhost
REDIS_PORT=6379
REDDIT_CLIENT_ID=
REDDIT_CLIENT_SECRET=
SALESFORCE_ENDPOINT=http://example.com
SALESFORCE_CLIENT_ID=
SALESFORCE_CLIENT_SECRET=
OPENAI_API_KEY=
OPENAI_MODEL=gpt-4.1-nano
