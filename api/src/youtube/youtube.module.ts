import { BullModule } from "@nestjs/bullmq";
import { Modu<PERSON> } from "@nestjs/common";

import { BullMQAdapter } from "@bull-board/api/bullMQAdapter";
import { BullBoardModule } from "@bull-board/nestjs";

import { CacheModule } from "../cache/cache.module";
import {
    AI_ANALYSIS_QUEUE,
    YOUTUBE_CHANNEL_QUEUE,
    YOUTUBE_REFRESH_QUEUE,
    YOUTUBE_VIDEO_QUEUE,
    YOUTUBE_VIDEO_REFRESH_QUEUE,
} from "../task/constants";
import { YoutubeApiModule } from "../youtube-api/youtube-api.module";
import { YoutubeSubtitlesModule } from "../youtube-subtitles/youtube-subtitles.module";
import { YtDlpModule } from "../yt-dlp/yt-dlp.module";
import { YoutubeChannelProcessor } from "./channel.processor";
import { YoutubeRefreshProcessor } from "./refresh.processor";
import { YoutubeVideoRefreshProcessor } from "./video-refresh.processor";
import { YoutubeVideoProcessor } from "./video.processor";
import { YoutubeRepositoryService } from "./youtube-repository.service";
import { YoutubeService } from "./youtube.service";

@Module({
    imports: [
        BullBoardModule.forFeature(
            {
                name: YOUTUBE_CHANNEL_QUEUE,
                adapter: BullMQAdapter,
            },
            {
                name: YOUTUBE_VIDEO_QUEUE,
                adapter: BullMQAdapter,
            },
            {
                name: YOUTUBE_REFRESH_QUEUE,
                adapter: BullMQAdapter,
            },
            {
                name: YOUTUBE_VIDEO_REFRESH_QUEUE,
                adapter: BullMQAdapter,
            },
        ),
        BullModule.registerQueue(
            { name: YOUTUBE_CHANNEL_QUEUE },
            { name: YOUTUBE_VIDEO_QUEUE },
            { name: YOUTUBE_REFRESH_QUEUE },
            { name: YOUTUBE_VIDEO_REFRESH_QUEUE },
            { name: AI_ANALYSIS_QUEUE },
        ),
        CacheModule,
        YoutubeApiModule,
        YoutubeSubtitlesModule,
        YtDlpModule,
    ],
    providers: [
        YoutubeChannelProcessor,
        YoutubeRefreshProcessor,
        YoutubeRepositoryService,
        YoutubeService,
        YoutubeVideoProcessor,
        YoutubeVideoRefreshProcessor,
    ],
})
export class YoutubeModule {}
