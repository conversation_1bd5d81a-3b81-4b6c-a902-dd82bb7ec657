import { Injectable } from "@nestjs/common";

import { PrismaService } from "../prisma/prisma.service";

const ONE_DAY = 1 * 24 * 60 * 60 * 1000;
const REFRESH_SCHEDULE = [3, 7, 14, 21, 28];

@Injectable()
export class YoutubeRepositoryService {
    constructor(private readonly prisma: PrismaService) {}

    async getChannel(channelHandle: string) {
        return this.prisma.youtubeChannel.findUniqueOrThrow({
            where: {
                channelHandle,
            },
        });
    }

    async filterNewVideos<T extends { id: string }>(videos: T[]) {
        const videoIds = videos.map(({ id }) => id);
        const existingVideos = await this.prisma.youtubeVideo.findMany({
            where: {
                id: {
                    in: videoIds,
                },
            },
        });

        const existingVideoIdSet = new Set(existingVideos.map(({ id }) => id));

        return videos.filter(({ id }) => !existingVideoIdSet.has(id));
    }

    async *getVideosPendingUpdate() {
        for (const updateDay of REFRESH_SCHEDULE) {
            for (const video of await this.getVideosWithExactAge(updateDay)) {
                yield video;
            }
        }
    }

    private async getVideosWithExactAge(daysAgo: number) {
        const today = new Date(Date.now());
        today.setHours(0, 0, 0, 0);

        const targetDate = new Date(today.valueOf() - daysAgo * ONE_DAY);

        const dateStart = new Date(targetDate);
        const dateEnd = new Date(targetDate);
        dateEnd.setHours(23, 59, 59, 999);

        return this.prisma.youtubeVideo.findMany({
            select: {
                id: true,
                title: true,
                channel: true,
            },
            where: {
                AND: {
                    createdAt: {
                        gte: dateStart,
                        lte: dateEnd,
                    },
                },
            },
        });
    }
}
