import { Processor, WorkerHost } from "@nestjs/bullmq";

import { ClsService } from "nestjs-cls";

import { UseTenantCls } from "../generic/bullmq/processor";
import { PrismaService } from "../prisma/prisma.service";
import { YOUTUBE_VIDEO_REFRESH_QUEUE } from "../task/constants";
import { YoutubeVideoRefreshJob } from "../task/types";
import { YoutubeService } from "./youtube.service";

@Processor(YOUTUBE_VIDEO_REFRESH_QUEUE)
export class YoutubeVideoRefreshProcessor extends WorkerHost {
    constructor(
        private readonly youtubeService: YoutubeService,
        private readonly prisma: PrismaService,
        private readonly cls: ClsService,
    ) {
        super();
    }

    @UseTenantCls()
    async process(job: YoutubeVideoRefreshJob) {
        const { youtubeId } = job.data;

        job.log("Fetching video statistics");
        const statistics =
            await this.youtubeService.fetchVideoStatistics(youtubeId);
        await this.prisma.youtubeVideo.update({
            where: {
                tenantId_id: {
                    tenantId: this.cls.get("tenantId"),
                    id: youtubeId,
                },
            },
            data: {
                ...statistics,
            },
        });
        const { viewCount, likeCount, commentCount } = statistics;
        job.log(
            `${viewCount} views, ${commentCount} comments, ${likeCount} likes`,
        );

        // TODO - update comment threads ?
    }
}
