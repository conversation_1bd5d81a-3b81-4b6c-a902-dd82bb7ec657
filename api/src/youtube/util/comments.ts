import { youtube_v3 } from "@googleapis/youtube";
import { YoutubeComment } from "@prisma/client";
import { GaxiosResponse } from "gaxios";

export function getCleanFlatComments(
    rawCommentThreads: GaxiosResponse<youtube_v3.Schema$CommentThreadListResponse>,
): Omit<YoutubeComment, "tenantId">[] {
    const rawFlatComments = rawCommentThreads.data.items.flatMap((thread) => {
        const rawTopLevelComment = thread.snippet.topLevelComment;
        const replyCount = thread.snippet.totalReplyCount;
        const rawReplies = thread.replies?.comments || [];
        return [{ ...rawTopLevelComment, replyCount }, ...rawReplies];
    });

    const cleanComments = rawFlatComments.map((rawComment) => ({
        videoId: rawComment.snippet.videoId,
        id: rawComment.id,
        parentId: rawComment.snippet.parentId,
        body: rawComment.snippet.textDisplay,
        publishedAt: new Date(rawComment.snippet.publishedAt),
        score: rawComment.snippet.likeCount,
        replyCount: rawComment["replyCount"] ?? 0,
    }));

    return cleanComments;
}
