export function cleanSubtitles(rawSubtitles: string): string {
    return rawSubtitles
        .split("\n")
        .slice(3)
        .filter((line) => !line.match(/.*(\d\d:\d\d:\d\d)/))
        .filter((line, index) => index % 2)
        .map((line) => line.trim())
        .filter(Boolean)
        .join(" ");
}

export function isValidSubtitleContent(subtitles: string): boolean {
    return subtitles.match(/\s*WEBVTT/g) !== null;
}
