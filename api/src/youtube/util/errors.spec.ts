import { isDisabledCommentsError } from "./errors";

describe("isDisabledCommentsError", () => {
    it("true if the error is a disabled comments error", () => {
        const error = {
            response: {
                data: {
                    error: {
                        errors: [{ reason: "commentsDisabled" }],
                    },
                },
            },
        };
        expect(isDisabledCommentsError(error)).toBe(true);
    });

    it("false if the error is not a disabled comments error", () => {
        const error = {
            response: {
                data: {
                    error: { errors: [{ reason: "tooManyRequests" }] },
                },
            },
        };
        expect(isDisabledCommentsError(error)).toBe(false);
    });

    it("safely handles missing error data", () => {
        const error = {
            response: {},
        };
        expect(isDisabledCommentsError(error)).toBe(false);
    });
});
