import {
    extractSubtitleURLFromManifest,
    isManifestURL,
    isSubtitleURL,
} from "./subtitles-fetching";

describe("subtitle fetching utils", () => {
    it("isManifestURL - false", () => {
        expect(
            isManifestURL(
                "https://www.youtube.com/api/timedtext?v=WUrq28sBrm0&ei=9xDSZ4PxE46wzN0P176x6AE&caps=asr&opi=112496729&xoaf=5&hl=en&ip=0.0.0.0&ipbits=0&expire=1741845351&sparams=ip%2Cipbits%2Cexpire%2Cv%2Cei%2Ccaps%2Copi%2Cxoaf&signature=3FB067B4A64BB4AF9B33460FB60E786420EDD60D.164EC8F76033129F10E556C38CEAD3B39CCAA26B&key=yt8&kind=asr&lang=en&fmt=vtt",
            ),
        ).toBeFalsy();
    });

    it("isManifestURL - true", () => {
        expect(
            isManifestURL(
                "https://manifest.googlevideo.com/api/manifest/hls_timedtext_playlist/expire/1741841857/ei/YRHSZ-HUOs70zN0P9Y3O6QE/ip/*************/id/0056a1c5c16daa31/source/youtube/requiressl/yes/dur/320.887/tts_params/caps%3Dasr%26v%3DAFahxcFtqjE%26lang%3Den%26name%3DDTVCC1%26ip%3D*************%26ipbits%3D0%26expire%3D1741841857%26sparams%3Dip,ipbits,expire,caps,v%26signature%3D15513983544D9C830DC748211F32E961FA1EB5B5.81891A718230771A9246644E259D63B923B1F10D%26key%3Ddg_yt0/rqh/4/hls_chunk_host/rr4---sn-q4flrnee.googlevideo.com/xpc/EgVo2aDSNQ%3D%3D/met/1741820257,/mh/vS/mm/31,29/mn/sn-q4flrnee,sn-cvb7lnee/ms/au,rdu/mv/m/mvi/4/pl/22/rms/au,au/pcm2/yes/initcwndbps/1823750/bui/AUWDL3y8iNqwlMoZq6Z2dvu7bkT8vGlu3q7eRj6bhZNnbxGsLwh1MNwPUGEB5YPx_JGNm6_JeNfv1FMu/spc/RjZbSVJpKWTxcvKDP2hcLqWTQcxnnj1_Be7xp2emxNDRYmDNCM-JfIM87KA/vprv/1/playlist_type/DVR/mt/1741819985/fvip/5/short_key/1/keepalive/yes/fexp/51326932,51358317,51411872/sparams/expire,ei,ip,id,source,requiressl,dur,tts_params,rqh,xpc,pcm2,bui,spc,vprv,playlist_type/sig/AJfQdSswRgIhAInzX6zMa466FKXa1N-BCkauAFgeZ5oxk8XPDe2iCEBFAiEArmE-GpzlaN8pM92siTrSAa7Fw8Hk75I5jIo2O6b0Qm0%3D/lsparams/hls_chunk_host,met,mh,mm,mn,ms,mv,mvi,pl,rms,initcwndbps/lsig/AFVRHeAwRgIhANRrzfvHUq_19COs5UeWWNwVNsY-q0Z8if6Sz0T3clbtAiEAhNR2Wn-E2W7mdPTkCv7wgX5lh9rtjlIWQ4vDUeKRKwI%3D/playlist/index.m3u8",
            ),
        ).toBeTruthy();
    });

    it("isSubtitleURL - empty input", () => {
        expect(isSubtitleURL()).toBeFalsy();
    });

    it("isSubtitleURL - false", () => {
        expect(
            isSubtitleURL(
                "https://manifest.googlevideo.com/api/manifest/hls_timedtext_playlist/expire/1741841857/ei/YRHSZ-HUOs70zN0P9Y3O6QE/ip/*************/id/0056a1c5c16daa31/source/youtube/requiressl/yes/dur/320.887/tts_params/caps%3Dasr%26v%3DAFahxcFtqjE%26lang%3Den%26name%3DDTVCC1%26ip%3D*************%26ipbits%3D0%26expire%3D1741841857%26sparams%3Dip,ipbits,expire,caps,v%26signature%3D15513983544D9C830DC748211F32E961FA1EB5B5.81891A718230771A9246644E259D63B923B1F10D%26key%3Ddg_yt0/rqh/4/hls_chunk_host/rr4---sn-q4flrnee.googlevideo.com/xpc/EgVo2aDSNQ%3D%3D/met/1741820257,/mh/vS/mm/31,29/mn/sn-q4flrnee,sn-cvb7lnee/ms/au,rdu/mv/m/mvi/4/pl/22/rms/au,au/pcm2/yes/initcwndbps/1823750/bui/AUWDL3y8iNqwlMoZq6Z2dvu7bkT8vGlu3q7eRj6bhZNnbxGsLwh1MNwPUGEB5YPx_JGNm6_JeNfv1FMu/spc/RjZbSVJpKWTxcvKDP2hcLqWTQcxnnj1_Be7xp2emxNDRYmDNCM-JfIM87KA/vprv/1/playlist_type/DVR/mt/1741819985/fvip/5/short_key/1/keepalive/yes/fexp/51326932,51358317,51411872/sparams/expire,ei,ip,id,source,requiressl,dur,tts_params,rqh,xpc,pcm2,bui,spc,vprv,playlist_type/sig/AJfQdSswRgIhAInzX6zMa466FKXa1N-BCkauAFgeZ5oxk8XPDe2iCEBFAiEArmE-GpzlaN8pM92siTrSAa7Fw8Hk75I5jIo2O6b0Qm0%3D/lsparams/hls_chunk_host,met,mh,mm,mn,ms,mv,mvi,pl,rms,initcwndbps/lsig/AFVRHeAwRgIhANRrzfvHUq_19COs5UeWWNwVNsY-q0Z8if6Sz0T3clbtAiEAhNR2Wn-E2W7mdPTkCv7wgX5lh9rtjlIWQ4vDUeKRKwI%3D/playlist/index.m3u8",
            ),
        ).toBeFalsy();
    });

    it("isSubtitleURL - true", () => {
        expect(
            isSubtitleURL(
                "https://www.youtube.com/api/timedtext?caps=asr&v=cWjm53WMesA&lang=en&name=DTVCC1&ip=***************&ipbits=0&expire=1741828747&sparams=ip,ipbits,expire,caps,v&signature=92A6A959DE8F8CD8FCDC51B946193F17F965FFC0.3DB6DCAA09894A932CE9CE8642ECE91D108FBA45&key=dg_yt0&fmt=vtt&range=0-564",
            ),
        ).toBeTruthy();
    });

    it("extractSubtitleURLFromManifest", () => {
        const url = extractSubtitleURLFromManifest(SUBTITLE_MANIFEST);
        expect(url).toEqual(
            "https://www.youtube.com/api/timedtext?caps=asr&v=cWjm53WMesA&lang=en&name=DTVCC1&ip=***************&ipbits=0&expire=1741828747&sparams=ip,ipbits,expire,caps,v&signature=92A6A959DE8F8CD8FCDC51B946193F17F965FFC0.3DB6DCAA09894A932CE9CE8642ECE91D108FBA45&key=dg_yt0&fmt=vtt&range=0-564",
        );
    });
});

const SUBTITLE_MANIFEST = `
#EXTM3U
#EXT-X-VERSION:3
#EXT-X-PLAYLIST-TYPE:VOD
#EXT-X-TARGETDURATION:600
#EXTINF:564,
https://www.youtube.com/api/timedtext?caps=asr&v=cWjm53WMesA&lang=en&name=DTVCC1&ip=***************&ipbits=0&expire=1741828747&sparams=ip,ipbits,expire,caps,v&signature=92A6A959DE8F8CD8FCDC51B946193F17F965FFC0.3DB6DCAA09894A932CE9CE8642ECE91D108FBA45&key=dg_yt0&fmt=vtt&range=0-564
#EXT-X-ENDLIST
`;
