export function isManifestURL(url?: string): boolean {
    return url?.startsWith("https://manifest.googlevideo.com");
}

export function isSubtitleURL(url?: string): boolean {
    return url?.startsWith("https://www.youtube.com/api/timedtext");
}

export function extractSubtitleURLFromManifest(manifest: string) {
    const url = manifest
        .split("\n")
        .find((line) =>
            line.startsWith("https://www.youtube.com/api/timedtext"),
        );

    return url;
}
