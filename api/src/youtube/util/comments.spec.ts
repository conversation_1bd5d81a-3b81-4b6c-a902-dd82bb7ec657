import { youtube_v3 } from "@googleapis/youtube";
import { GaxiosResponse } from "gaxios";

import { getCleanFlatComments } from "./comments";

describe("youtube getCleanFlatComments", () => {
    it("flattens and cleans comments", () => {
        expect(getCleanFlatComments(RAW_COMMENTS)).toEqual(CLEAN_COMMENTS);
    });
});

const RAW_COMMENTS: GaxiosResponse<youtube_v3.Schema$CommentThreadListResponse> =
    {
        config: {},
        status: 200,
        statusText: "",
        headers: {},
        request: { responseURL: "" },
        data: {
            items: [
                {
                    kind: "youtube#commentThread",
                    etag: "ihy5AZuoB4GxkNAc43Y6KAdKh7I",
                    id: "Ugz4miX-NksSgSNd-Ap4AaABAg",
                    snippet: {
                        channelId: "UC3I2GFN_F8WudD_2jUZbojA",
                        videoId: "js3SsO5g5OM",
                        topLevelComment: {
                            kind: "youtube#comment",
                            etag: "x01Leo16-THkOJFL--rB3PDdhi0",
                            id: "Ugz4miX-NksSgSNd-Ap4AaABAg",
                            snippet: {
                                channelId: "UC3I2GFN_F8WudD_2jUZbojA",
                                videoId: "js3SsO5g5OM",
                                textDisplay:
                                    "Late to discovering Odesza. My loss. But now that I have I’m hooked. This piece in particular hit a bullseye in my heart..",
                                textOriginal:
                                    "Late to discovering Odesza. My loss. But now that I have I’m hooked. This piece in particular hit a bullseye in my heart..",
                                authorDisplayName: "@deborahings1543",
                                authorProfileImageUrl:
                                    "https://yt3.ggpht.com/ytc/AIdro_nJEmuNojPL6xCJh4bIEedjLUMWK76vrPaWYFN8yGNilQ=s48-c-k-c0x00ffffff-no-rj",
                                authorChannelUrl:
                                    "http://www.youtube.com/@deborahings1543",
                                authorChannelId: {
                                    value: "UCQ8MDtG214UQjGNo-fv615Q",
                                },
                                canRate: true,
                                viewerRating: "none",
                                likeCount: 1,
                                publishedAt: "2024-07-21T14:28:13Z",
                                updatedAt: "2024-07-21T14:28:13Z",
                            },
                        },
                        canReply: true,
                        totalReplyCount: 1,
                        isPublic: true,
                    },
                    replies: {
                        comments: [
                            {
                                kind: "youtube#comment",
                                etag: "-4qv4DV_fkxXjRdqRxyqeqowkGY",
                                id: "Ugz4miX-NksSgSNd-Ap4AaABAg.A69YHeccIVnA7BkoJZpWfs",
                                snippet: {
                                    channelId: "UC3I2GFN_F8WudD_2jUZbojA",
                                    videoId: "js3SsO5g5OM",
                                    textDisplay:
                                        "Hope you listen to all their albums<br>Take your time but don't miss them",
                                    textOriginal:
                                        "Hope you listen to all their albums\nTake your time but don't miss them",
                                    parentId: "Ugz4miX-NksSgSNd-Ap4AaABAg",
                                    authorDisplayName: "@wokeclub1844",
                                    authorProfileImageUrl:
                                        "https://yt3.ggpht.com/ytc/AIdro_mi0ygaRjzKYdl7yu-cgJ_bOtziifkcAQyeOt69y84=s48-c-k-c0x00ffffff-no-rj",
                                    authorChannelUrl:
                                        "http://www.youtube.com/@wokeclub1844",
                                    authorChannelId: {
                                        value: "UCtjYHf_qb8srBURVNHqxK6Q",
                                    },
                                    canRate: true,
                                    viewerRating: "none",
                                    likeCount: 0,
                                    publishedAt: "2024-08-16T07:36:17Z",
                                    updatedAt: "2024-08-16T07:36:17Z",
                                },
                            },
                        ],
                    },
                },
                {
                    kind: "youtube#commentThread",
                    etag: "XOFUuv0mKeJBLa4AXOFQ6qRbujw",
                    id: "UgxSMrmtjEmB3ncrELB4AaABAg",
                    snippet: {
                        channelId: "UC3I2GFN_F8WudD_2jUZbojA",
                        videoId: "js3SsO5g5OM",
                        topLevelComment: {
                            kind: "youtube#comment",
                            etag: "MiabJCW2y2cYFbnDm4ZkZ8WzQmM",
                            id: "UgxSMrmtjEmB3ncrELB4AaABAg",
                            snippet: {
                                channelId: "UC3I2GFN_F8WudD_2jUZbojA",
                                videoId: "js3SsO5g5OM",
                                textDisplay:
                                    "What a masterpiece !! The violins give it like the opening credit scene to a James Bond movie vibe…",
                                textOriginal:
                                    "What a masterpiece !! The violins give it like the opening credit scene to a James Bond movie vibe…",
                                authorDisplayName: "@sal1701",
                                authorProfileImageUrl:
                                    "https://yt3.ggpht.com/_xoOWNcSvefdXn4c1HeY4tCg7tUoPo24mA2sfTTZOR9-jHvD83h9uHZP0xqicJf-Vg1s6-UpJQ=s48-c-k-c0x00ffffff-no-rj",
                                authorChannelUrl:
                                    "http://www.youtube.com/@sal1701",
                                authorChannelId: {
                                    value: "UCczzhTZK7eYgQKWElXSl8pw",
                                },
                                canRate: true,
                                viewerRating: "none",
                                likeCount: 1,
                                publishedAt: "2024-10-28T20:29:15Z",
                                updatedAt: "2024-10-28T20:29:15Z",
                            },
                        },
                        canReply: true,
                        totalReplyCount: 0,
                        isPublic: true,
                    },
                },
            ],
        },
    };

const CLEAN_COMMENTS = [
    {
        videoId: "js3SsO5g5OM",
        id: "Ugz4miX-NksSgSNd-Ap4AaABAg",
        body: "Late to discovering Odesza. My loss. But now that I have I’m hooked. This piece in particular hit a bullseye in my heart..",
        publishedAt: new Date("2024-07-21T14:28:13Z"),
        score: 1,
        replyCount: 1,
    },
    {
        videoId: "js3SsO5g5OM",
        id: "Ugz4miX-NksSgSNd-Ap4AaABAg.A69YHeccIVnA7BkoJZpWfs",
        parentId: "Ugz4miX-NksSgSNd-Ap4AaABAg",
        body: "Hope you listen to all their albums<br>Take your time but don't miss them",
        publishedAt: new Date("2024-08-16T07:36:17Z"),
        score: 0,
        replyCount: 0,
    },
    {
        videoId: "js3SsO5g5OM",
        id: "UgxSMrmtjEmB3ncrELB4AaABAg",
        body: "What a masterpiece !! The violins give it like the opening credit scene to a James Bond movie vibe…",
        publishedAt: new Date("2024-10-28T20:29:15Z"),
        score: 1,
        replyCount: 0,
    },
];
