import { InjectQueue, Processor, WorkerHost } from "@nestjs/bullmq";

import { UseTenantCls } from "../generic/bullmq/processor";
import {
    YOUTUBE_REFRESH_QUEUE,
    YOUTUBE_VIDEO_REFRESH_QUEUE,
} from "../task/constants";
import { YoutubeRefreshJob, YoutubeVideoRefreshQueue } from "../task/types";
import { YoutubeRepositoryService } from "./youtube-repository.service";

@Processor(YOUTUBE_REFRESH_QUEUE)
export class YoutubeRefreshProcessor extends WorkerHost {
    constructor(
        private readonly youtubeRepositoryService: YoutubeRepositoryService,
        @InjectQueue(YOUTUBE_VIDEO_REFRESH_QUEUE)
        private youtubeVideoRefreshQueue: YoutubeVideoRefreshQueue,
    ) {
        super();
    }

    @UseTenantCls()
    async process(job: YoutubeRefreshJob) {
        let videoCount = 0;

        const videosPendingUpdateGenerator =
            this.youtubeRepositoryService.getVideosPendingUpdate();
        for await (const video of videosPendingUpdateGenerator) {
            const refreshJob = {
                youtubeId: video.id,
                title: video.title,
                channelHandle: video.channel.channelHandle,
                tenantId: job.data.tenantId,
            };
            await this.youtubeVideoRefreshQueue.add(video.title, refreshJob);
            videoCount += 1;
        }

        job.log(`Added ${videoCount} YouTube video refresh jobs`);
    }
}
