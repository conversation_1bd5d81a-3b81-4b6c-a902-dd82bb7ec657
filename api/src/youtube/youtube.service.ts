import { Injectable } from "@nestjs/common";

import cachified from "@epic-web/cachified";
import { youtube_v3 } from "@googleapis/youtube";
import { GaxiosResponse } from "gaxios";

import { YoutubeApiService } from "../youtube-api/youtube-api.service";
import { CacheService } from "./../cache/cache.service";
import { isDisabledCommentsError } from "./util/errors";

const ONE_DAY = 1 * 24 * 60 * 60 * 1000;

const EMPTY_RESPONSE = {
    data: { items: [] },
} as GaxiosResponse<youtube_v3.Schema$CommentThreadListResponse>;

@Injectable()
export class YoutubeService {
    constructor(
        private readonly cacheService: CacheService,
        private readonly youtubeApiService: YoutubeApiService,
    ) {}

    async searchChannelVideos(uploadsPlaylistId: string, maxResults = 50) {
        const searchResult = await cachified({
            key: `youtube-videos-${uploadsPlaylistId}-${maxResults}`,
            cache: this.cacheService.cache,
            ttl: ONE_DAY,
            getFreshValue: () =>
                this.youtubeApiService.searchVideos(uploadsPlaylistId),
        });

        const rawVideos = searchResult.data.items;
        const videos = rawVideos
            .filter((video) => !!video?.snippet?.resourceId?.videoId)
            .map((video) => ({
                id: video.snippet.resourceId.videoId,
                title: video.snippet.title,
                subtitles: "",
                publishedAt: video.snippet.publishedAt,
            }));

        return videos;
    }

    async fetchVideoStatistics(videoId: string) {
        const videoListing = await cachified({
            key: `youtube-video-statistics-${videoId}`,
            cache: this.cacheService.cache,
            getFreshValue: () =>
                this.youtubeApiService.getVideoListingWithStatistics(videoId),
            ttl: ONE_DAY,
        });

        const { statistics } = videoListing.data.items[0];

        return {
            viewCount: Number(statistics.viewCount),
            commentCount: Number(statistics.commentCount),
            likeCount: Number(statistics.likeCount),
            favoriteCount: Number(statistics.favoriteCount),
        };
    }

    async getCommentThreads(videoId: string) {
        return cachified({
            key: `youtube-comment-threads-${videoId}`,
            cache: this.cacheService.cache,
            getFreshValue: () =>
                this.youtubeApiService
                    .getCommentThreads(videoId)
                    .catch((error) => {
                        if (isDisabledCommentsError(error))
                            return EMPTY_RESPONSE;
                        throw error;
                    }),
            ttl: ONE_DAY,
        });
    }
}
