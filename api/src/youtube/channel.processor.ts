import { InjectQueue, Processor, WorkerHost } from "@nestjs/bullmq";

import { ClsService } from "nestjs-cls";

import { UseTenantCls } from "../generic/bullmq/processor";
import { PrismaService } from "../prisma/prisma.service";
import { YOUTUBE_CHANNEL_QUEUE, YOUTUBE_VIDEO_QUEUE } from "../task/constants";
import { YoutubeChannelJob, YoutubeVideoQueue } from "../task/types";
import { YoutubeRepositoryService } from "./youtube-repository.service";
import { YoutubeService } from "./youtube.service";

@Processor(YOUTUBE_CHANNEL_QUEUE)
export class YoutubeChannelProcessor extends WorkerHost {
    constructor(
        private readonly youtubeRepositoryService: YoutubeRepositoryService,
        private readonly youtubeService: YoutubeService,
        private readonly prisma: PrismaService,
        @InjectQueue(YOUTUBE_VIDEO_QUEUE)
        private youtubeVideoQueue: YoutubeVideoQueue,
        private readonly cls: ClsService,
    ) {
        super();
    }

    @UseTenantCls()
    async process(job: YoutubeChannelJob) {
        const { channelHandle, maxResults } = job.data;

        const channel =
            await this.youtubeRepositoryService.getChannel(channelHandle);
        const videos = await this.youtubeService.searchChannelVideos(
            channel.uploadsPlaylistId,
            maxResults,
        );
        job.log(`Fetched ${videos.length} videos`);

        const newVideos =
            await this.youtubeRepositoryService.filterNewVideos(videos);
        job.log(`Processing ${newVideos.length} new videos`);

        for (const video of newVideos) {
            job.log(`"${video.title}"`);
            // Add video id to the database
            await this.prisma.youtubeVideo.upsert({
                where: {
                    tenantId_id: {
                        tenantId: this.cls.get("tenantId"),
                        id: video.id,
                    },
                },
                create: {
                    ...video,
                    channel: {
                        connect: {
                            tenantId_id: {
                                tenantId: this.cls.get("tenantId"),
                                id: channel.id,
                            },
                        },
                    },
                },
                update: video,
            });

            // Add a job to process the video
            await this.youtubeVideoQueue.add(`"${video.title}"`, {
                youtubeId: video.id,
                videoURL: `https://www.youtube.com/watch?v=${video.id}`,
                channelHandle,
                tenantId: job.data.tenantId,
            });
        }
    }
}
