import { Injectable, Logger } from "@nestjs/common";

import * as https from "https";
import { SocksProxyAgent } from "socks-proxy-agent";
import { ManifestURL, SubtitleURL } from "src/youtube/types";

import { YoutubeSubtitlesServiceInterface } from "./youtube-subtitles.service.interface";

@Injectable()
export class YoutubeSubtitlesService
    implements YoutubeSubtitlesServiceInterface
{
    private logger = new Logger("YoutubeSubtitlesService");

    async getSubtitleManifest(
        manifestURL: ManifestURL,
        proxyURL?: string,
    ): Promise<string> {
        this.logger.log(`Fetching manifest from ${manifestURL}`);
        return this.fetch(manifestURL, proxyURL);
    }

    async getSubtitles(
        subtitlesURL: SubtitleURL,
        proxyURL?: string,
    ): Promise<string> {
        this.logger.log(`Fetching subtitles from ${subtitlesURL}`);
        return this.fetch(subtitlesURL, proxyURL);
    }

    // Native fetch cannot be used here because the request may be proxied through SOCKS
    private async fetch(url: string, proxyURL?: string): Promise<string> {
        const agent = proxyURL ? new SocksProxyAgent(proxyURL) : undefined;
        if (agent) this.logger.log(`Using proxy ${proxyURL} for ${url}`);

        return new Promise<string>((resolve, reject) => {
            https.get(url, { agent }, (res) => {
                if (res.statusCode !== 200) {
                    reject(
                        new Error(`Failed to fetch ${url}: ${res.statusCode}`),
                    );
                }
                let data = "";
                res.on("data", (chunk) => (data += chunk));
                res.on("end", () => resolve(data));
            });
        });
    }
}
