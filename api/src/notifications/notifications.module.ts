import { BullModule } from "@nestjs/bullmq";
import { <PERSON><PERSON><PERSON> } from "@nestjs/common";

import { BullMQAdapter } from "@bull-board/api/bullMQAdapter";
import { BullBoardModule } from "@bull-board/nestjs";

import { EmailModule } from "../email/email.module";
import { InsightsModule } from "../insights/insights.module";
import {
    NOTIFICATIONS_GROUP_SHARE_QUEUE,
    NOTIFICATIONS_PERIODIC_DIGEST_QUEUE,
} from "../task/constants";
import { TenantsModule } from "../tenants/tenants.module";
import { EmailTemplateService } from "./email-template.service";
import { GroupShareProcessor } from "./group-share.processor";
import { NotificationsController } from "./notifications.controller";
import { NotificationsService } from "./notifications.service";
import { PeriodicDigestProcessor } from "./periodic-digest.processor";

@Module({
    imports: [
        BullBoardModule.forFeature(
            {
                name: NOTIFICATIONS_PERIODIC_DIGEST_QUEUE,
                adapter: BullMQAdapter,
            },
            {
                name: NOTIFICATIONS_GROUP_SHARE_QUEUE,
                adapter: BullMQAdapter,
            },
        ),
        BullModule.registerQueue(
            { name: NOTIFICATIONS_PERIODIC_DIGEST_QUEUE },
            { name: NOTIFICATIONS_GROUP_SHARE_QUEUE },
        ),
        EmailModule,
        InsightsModule,
        TenantsModule,
    ],

    controllers: [NotificationsController],
    providers: [
        EmailTemplateService,
        GroupShareProcessor,
        NotificationsService,
        PeriodicDigestProcessor,
    ],
})
export class NotificationsModule {}
