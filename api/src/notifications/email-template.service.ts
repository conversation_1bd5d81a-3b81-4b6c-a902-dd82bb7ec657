import { Injectable } from "@nestjs/common";

import { NotificationCadence } from "@prisma/client";
import * as fs from "fs/promises";
import handlebars from "handlebars";
import * as path from "path";

handlebars.registerHelper("toLowerCase", (value: string) => {
    return value.toLowerCase();
});

export type TemplateData = {
    "periodic-digest": {
        period: NotificationCadence;
        appBaseUrl: string;
        user: {
            firstName: string;
        };
        digestItems: {
            [sectionName: string]: {
                summary: string;
                internalUrl: string;
                title: string;
            }[];
        };
    };
    "group-share": {
        appBaseUrl: string;
        user: {
            firstName: string;
        };
        shareItems: {
            [groupName: string]: {
                sharedBy: string;
                summary: string;
                internalUrl: string;
                title: string;
            }[];
        };
    };
};

@Injectable()
export class EmailTemplateService {
    constructor() {}

    async render<T extends keyof TemplateData>(
        templateName: T,
        data: TemplateData[T],
    ) {
        const template = await fs.readFile(
            path.join(__dirname, "templates", `${templateName}.hbs`),
            "utf8",
        );
        return handlebars.compile(template)(data);
    }
}
