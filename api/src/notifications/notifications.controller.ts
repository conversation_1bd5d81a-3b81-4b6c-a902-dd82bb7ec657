import { <PERSON>, <PERSON>, <PERSON><PERSON> } from "@nestjs/common";
import { <PERSON><PERSON> } from "@nestjs/schedule";
import { ApiResponse, ApiTags } from "@nestjs/swagger";

import { NotificationCadence } from "@prisma/client";
import { Response } from "express";

import { Role } from "../roles/role.enum";
import { Roles } from "../roles/roles.decorator";
import { TenantsService } from "../tenants/tenants.service";
import { NotificationsService } from "./notifications.service";

@Controller("notifications")
@ApiTags("background jobs - notifications")
export class NotificationsController {
    constructor(
        private readonly notificationService: NotificationsService,
        private readonly tenantService: TenantsService,
    ) {}

    @Cron("0 18 * * *")
    async sendDailyDigestCron(): Promise<void> {
        await this.tenantService.runForAllTenants(() =>
            this.notificationService.sendPeriodicDigest(
                NotificationCadence.DAILY,
            ),
        );
    }

    @Cron("0 18 * * 1")
    async sendWeeklyDigestCron(): Promise<void> {
        await this.tenantService.runForAllTenants(() =>
            this.notificationService.sendPeriodicDigest(
                NotificationCadence.WEEKLY,
            ),
        );
    }

    @Cron("0 18 1 * *")
    async sendMonthlyDigestCron(): Promise<void> {
        await this.tenantService.runForAllTenants(() =>
            this.notificationService.sendPeriodicDigest(
                NotificationCadence.MONTHLY,
            ),
        );
    }

    @Cron("0 18 * * 1")
    async sendGroupShareCron(): Promise<void> {
        await this.tenantService.runForAllTenants(() =>
            this.notificationService.sendGroupShare(),
        );
    }

    @Post("daily-digest")
    @Roles(Role.Admin)
    @ApiResponse({ status: 204 })
    async sendDailyDigestHttp(@Res() res: Response): Promise<void> {
        await this.notificationService.sendPeriodicDigest(
            NotificationCadence.DAILY,
        );

        res.sendStatus(204);
    }

    @Post("weekly-digest")
    @Roles(Role.Admin)
    @ApiResponse({ status: 204 })
    async sendWeeklyDigestHttp(@Res() res: Response): Promise<void> {
        await this.notificationService.sendPeriodicDigest(
            NotificationCadence.WEEKLY,
        );

        res.sendStatus(204);
    }

    @Post("monthly-digest")
    @Roles(Role.Admin)
    @ApiResponse({ status: 204 })
    async sendMonthlyDigestHttp(@Res() res: Response): Promise<void> {
        await this.notificationService.sendPeriodicDigest(
            NotificationCadence.MONTHLY,
        );

        res.sendStatus(204);
    }

    @Post("group-share")
    @Roles(Role.Admin)
    @ApiResponse({ status: 204 })
    async sendGroupShareHttp(@Res() res: Response): Promise<void> {
        await this.notificationService.sendGroupShare();

        res.sendStatus(204);
    }
}
