import { Processor, WorkerHost } from "@nestjs/bullmq";

import { EmailService } from "../email/email.service";
import { UseTenantCls } from "../generic/bullmq/processor";
import { TimelineService } from "../insights/timeline.service";
import { PrismaService } from "../prisma/prisma.service";
import { NOTIFICATIONS_GROUP_SHARE_QUEUE } from "../task/constants";
import { EmailTemplateService } from "./email-template.service";
import { GroupShareEmailJob } from "./types";

@Processor(NOTIFICATIONS_GROUP_SHARE_QUEUE)
export class GroupShareProcessor extends WorkerHost {
    constructor(
        private readonly emailService: EmailService,
        private readonly prisma: PrismaService,
        private readonly emailTemplateService: EmailTemplateService,
        private readonly timelineService: TimelineService,
    ) {
        super();
    }

    @UseTenantCls()
    async process(job: GroupShareEmailJob) {
        const user = await this.prisma.user.findUnique({
            where: {
                id: job.data.user.id,
            },
            include: {
                groups: {
                    include: {
                        group: true,
                    },
                },
            },
        });

        const oneDayAgo = new Date(Date.now() - 1000 * 60 * 60 * 24);
        const groups = await this.prisma.group.findMany({
            include: {
                shares: {
                    where: {
                        createdAt: {
                            gte: oneDayAgo,
                        },
                    },
                    include: {
                        user: {
                            select: {
                                firstName: true,
                                lastName: true,
                            },
                        },
                        analysis: {
                            select: {
                                source: true,
                                sourceId: true,
                            },
                        },
                    },
                },
            },
            where: {
                id: { in: user.groups.map((group) => group.group.id) },
                shares: {
                    some: {
                        createdAt: {
                            gte: oneDayAgo,
                        },
                    },
                },
            },
        });

        type ShareItem = {
            sharedBy: string;
            summary: string;
            internalUrl: string;
            title: string;
        };

        const shareItems: { [groupName: string]: ShareItem[] } = {};

        for (const group of groups) {
            for (const share of group.shares) {
                const { source, sourceId } = share.analysis;
                const timelineItem =
                    await this.timelineService.getTimelineItemDetails(
                        source,
                        sourceId,
                        user.id,
                    );

                if (!timelineItem) continue;

                if (!shareItems[group.name]) {
                    shareItems[group.name] = [];
                }

                const { id, summary, title } = timelineItem;
                shareItems[group.name].push({
                    sharedBy: `${share.user.firstName} ${share.user.lastName}`,
                    summary,
                    internalUrl: `${process.env.APP_BASE_URL}/timeline/${id}`,
                    title,
                });
            }
        }

        const subject = "Shared with you";
        const htmlContent = await this.emailTemplateService.render(
            "group-share",
            {
                appBaseUrl: process.env.APP_BASE_URL,
                user,
                shareItems,
            },
        );

        job.log("Sending email");

        await this.emailService.sendHtmlEmail(
            user.email,
            subject,
            htmlContent,
            [process.env.NOREPLY_EMAIL],
        );
    }
}
