import { Processor, WorkerHost } from "@nestjs/bullmq";

import {
    DataSource,
    NotificationCadence,
    UserGroupPreference,
    UserTagPreference,
    UserTopicPreference,
} from "@prisma/client";

import { UseTenantCls } from "..//generic/bullmq/processor";
import { EmailService } from "../email/email.service";
import { TimelineParams, TimelineService } from "../insights/timeline.service";
import { PrismaService } from "../prisma/prisma.service";
import { NOTIFICATIONS_PERIODIC_DIGEST_QUEUE } from "../task/constants";
import { EmailTemplateService } from "./email-template.service";
import { PeriodicDigestEmailJob } from "./types";
import { truncate } from "./util";

type DigestItem = {
    summary: string;
    title: string;
    internalUrl: string;
};
@Processor(NOTIFICATIONS_PERIODIC_DIGEST_QUEUE)
export class PeriodicDigestProcessor extends WorkerHost {
    constructor(
        private readonly emailService: EmailService,
        private readonly prisma: PrismaService,
        private readonly emailTemplateService: EmailTemplateService,
        private readonly timelineService: TimelineService,
    ) {
        super();
    }

    @UseTenantCls()
    async process(job: PeriodicDigestEmailJob) {
        const { period } = job.data;

        const user = await this.getUserWithPreferences(job.data.user.id);

        const { topicPreferences, groupPreferences, tagPreferences } = user;

        const topicCandidateItems = await this.getTopicDigestCandidateItems(
            user.id,
            topicPreferences,
        );

        const groupCandidateItems = await this.getGroupDigestCandidateItems(
            user.id,
            groupPreferences,
        );

        const tagCandidateItems = await this.getTagDigestCandidateItems(
            user.id,
            tagPreferences,
        );

        const candidateItems = [
            ...topicCandidateItems,
            ...groupCandidateItems,
            ...tagCandidateItems,
        ].flat();

        const digestItems = await this.processCandidateItems(
            user.id,
            candidateItems,
        );

        if (Object.keys(digestItems).length === 0) {
            job.log("No digest items found, skipping email");
            return;
        }

        const subject = `Truthkeep ${period.toLowerCase()} digest`;
        const htmlContent = await this.emailTemplateService.render(
            "periodic-digest",
            {
                period,
                appBaseUrl: process.env.APP_BASE_URL,
                user,
                digestItems,
            },
        );

        await this.emailService.sendHtmlEmail(
            user.email,
            subject,
            htmlContent,
            [process.env.NOREPLY_EMAIL],
        );

        job.log("Email sent");
    }

    private async getUserWithPreferences(id: string) {
        return this.prisma.user.findUnique({
            where: {
                id,
            },
            include: {
                topicPreferences: {
                    include: {
                        topic: {
                            select: {
                                id: true,
                                name: true,
                            },
                        },
                    },
                },
                groupPreferences: {
                    include: {
                        group: {
                            select: {
                                id: true,
                                name: true,
                            },
                        },
                    },
                },
                tagPreferences: {
                    include: {
                        tag: {
                            select: {
                                id: true,
                                name: true,
                            },
                        },
                    },
                },
            },
        });
    }

    private async getTopicDigestCandidateItems(
        userId: string,
        topicPreferences: (UserTopicPreference & {
            topic: { id: number; name: string };
        })[],
    ): Promise<
        {
            sectionKey: string;
            analysisId: number;
            source: DataSource;
            sourceId: string;
        }[][]
    > {
        const results = [];

        for (const topicPreference of topicPreferences) {
            const { topic } = topicPreference;
            const searchParams = {
                userId,
                topicIds: [topic.id],
            };

            results.push(
                ...(
                    await this.getTimelineItems(
                        topicPreference.notificationCadence,
                        searchParams,
                    )
                ).map((result) => ({
                    ...result,
                    sectionKey: topic.name,
                })),
            );
            results.push(
                ...(
                    await this.getHighRelevanceTimelineItems(
                        topicPreference.highRelevanceNotifications,
                        searchParams,
                    )
                ).map((result) => ({
                    ...result,
                    sectionKey: `${topic.name} - High relevance`,
                })),
            );
            results.push(
                ...(
                    await this.getNegativeSentimentTimelineItems(
                        topicPreference.negativeSentimentNotifications,
                        searchParams,
                    )
                ).map((result) => ({
                    ...result,
                    sectionKey: `${topic.name} - Negative sentiment`,
                })),
            );
        }
        return results;
    }

    private async getGroupDigestCandidateItems(
        userId: string,
        groupPreferences: (UserGroupPreference & {
            group: { id: number; name: string };
        })[],
    ): Promise<
        {
            sectionKey: string;
            analysisId: number;
            source: DataSource;
            sourceId: string;
        }[][]
    > {
        const results = [];

        for (const groupPreference of groupPreferences) {
            const { group } = groupPreference;
            const searchParams = {
                userId,
                groupIds: [group.id],
            };

            results.push(
                ...(
                    await this.getTimelineItems(
                        groupPreference.notificationCadence,
                        searchParams,
                    )
                ).map((result) => ({
                    ...result,
                    sectionKey: group.name,
                })),
            );
            results.push(
                ...(
                    await this.getHighRelevanceTimelineItems(
                        groupPreference.highRelevanceNotifications,
                        searchParams,
                    )
                ).map((result) => ({
                    ...result,
                    sectionKey: `${group.name} - High relevance`,
                })),
            );
            results.push(
                ...(
                    await this.getNegativeSentimentTimelineItems(
                        groupPreference.negativeSentimentNotifications,
                        searchParams,
                    )
                ).map((result) => ({
                    ...result,
                    sectionKey: `${group.name} - Negative sentiment`,
                })),
            );
        }
        return results;
    }
    private async getTagDigestCandidateItems(
        userId: string,
        tagPreferences: (UserTagPreference & {
            tag: { id: number; name: string };
        })[],
    ) {
        const results = [];

        for (const tagPreference of tagPreferences) {
            const { tag } = tagPreference;

            const searchParams = {
                userId,
                tagIds: [tag.id],
            };

            results.push(
                ...(
                    await this.getTimelineItems(
                        tagPreference.notificationCadence,
                        searchParams,
                    )
                ).map((result) => ({
                    ...result,
                    sectionKey: tag.name,
                })),
            );
            results.push(
                ...(
                    await this.getHighRelevanceTimelineItems(
                        tagPreference.highRelevanceNotifications,
                        searchParams,
                    )
                ).map((result) => ({
                    ...result,
                    sectionKey: `${tag.name} - High relevance`,
                })),
            );
            results.push(
                ...(
                    await this.getNegativeSentimentTimelineItems(
                        tagPreference.negativeSentimentNotifications,
                        searchParams,
                    )
                ).map((result) => ({
                    ...result,
                    sectionKey: `${tag.name} - Negative sentiment`,
                })),
            );
        }

        return results;
    }

    private async processCandidateItems(
        userId: string,
        candidateItems: {
            sectionKey: string;
            analysisId: number;
            source: DataSource;
            sourceId: string;
        }[],
    ) {
        const itemIds = new Set<number>();
        const digestItems: { [sectionKey: string]: DigestItem[] } = {};

        for (const candidateItem of candidateItems) {
            const { sectionKey, analysisId, source, sourceId } = candidateItem;
            if (itemIds.has(analysisId)) continue;

            if (digestItems[sectionKey]?.length >= 3) continue;

            const details = await this.timelineService.getTimelineItemDetails(
                source,
                sourceId,
                userId,
            );

            if (!digestItems[sectionKey]) {
                digestItems[sectionKey] = [];
            }

            digestItems[sectionKey].push({
                summary: details.summary,
                title: truncate(details.title, 80),
                internalUrl: `${process.env.APP_BASE_URL}/timeline/${details.id}`,
            });
            itemIds.add(analysisId);
        }

        return digestItems;
    }

    private async getTimelineItems(
        period: NotificationCadence,
        params: Omit<
            TimelineParams,
            "take" | "skip" | "dateFrom" | "dateTo" | "timeZone"
        >,
    ) {
        let days = 0;
        switch (period) {
            case NotificationCadence.DAILY:
                days = 1;
                break;
            case NotificationCadence.WEEKLY:
                days = 7;
                break;
            case NotificationCadence.MONTHLY:
                days = 30;
                break;
            default:
                return [];
        }

        const dateFrom = new Date(Date.now() - days * 24 * 60 * 60 * 1000)
            .toISOString()
            .slice(0, 10);

        const dateTo = new Date().toISOString().slice(0, 10);

        const { items } = await this.timelineService.getTimelineItems({
            dateFrom,
            dateTo,
            take: 25, // Grab 25 items since there can be many duplicates across topics, groups, and tags
            skip: 0,
            timeZone: "America/Phoenix",
            ...params,
        });

        return items;
    }

    private async getNegativeSentimentTimelineItems(
        period: NotificationCadence,
        params: Omit<
            TimelineParams,
            "take" | "skip" | "dateFrom" | "dateTo" | "timeZone"
        >,
    ) {
        return this.getTimelineItems(period, {
            ...params,
            sentiments: ["NEGATIVE"],
            sort: "NEGATIVE_SENTIMENT",
        });
    }

    private async getHighRelevanceTimelineItems(
        period: NotificationCadence,
        params: Omit<
            TimelineParams,
            "take" | "skip" | "dateFrom" | "dateTo" | "timeZone"
        >,
    ) {
        return this.getTimelineItems(period, {
            ...params,
            minRelevance: 90,
            sort: "RELEVANCE",
        });
    }
}
