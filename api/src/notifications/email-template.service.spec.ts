import { NotificationCadence } from "@prisma/client";

import { EmailTemplateService } from "./email-template.service";

describe("EmailTemplateService", () => {
    const service = new EmailTemplateService();

    it("renders weekly digest email", async () => {
        const result = await service.render("periodic-digest", {
            period: NotificationCadence.WEEKLY,
            appBaseUrl: "https://www.example.com",
            user: {
                firstName: "<PERSON>",
            },
            digestItems: {
                hardware: [
                    {
                        summary: "Post about hardware",
                        title: "Hardware is cool",
                        internalUrl: "https://www.example.com/timeline/123",
                    },
                ],
            },
        });

        expect(result).toContain("weekly");
        expect(result).toContain("Hardware is cool");
        expect(result).toContain("https://www.example.com/timeline/123");
    });
});
