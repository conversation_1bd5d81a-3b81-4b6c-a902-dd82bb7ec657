import { NotificationCadence } from "@prisma/client";
import { Queue } from "bullmq";

import { Job } from "../task/types";

export type PeriodicDigestEmailJob = Job<{
    user: {
        id: string;
        email: string;
    };
    tenantId: string;
    period: NotificationCadence;
}>;

export type GroupShareEmailJob = Job<{
    user: {
        id: string;
        email: string;
    };
}>;

export type PeriodicDigestEmailQueue = Queue<PeriodicDigestEmailJob>;

export type GroupShareEmailQueue = Queue<GroupShareEmailJob>;
