import { InjectQueue } from "@nestjs/bullmq";
import { Injectable } from "@nestjs/common";

import { NotificationCadence } from "@prisma/client";
import { ClsService } from "nestjs-cls";

import { PrismaService } from "../prisma/prisma.service";
import {
    NOTIFICATIONS_GROUP_SHARE_QUEUE,
    NOTIFICATIONS_PERIODIC_DIGEST_QUEUE,
} from "../task/constants";
import { GroupShareEmailQueue, PeriodicDigestEmailQueue } from "./types";

@Injectable()
export class NotificationsService {
    constructor(
        private readonly prisma: PrismaService,
        private readonly cls: ClsService,
        @InjectQueue(NOTIFICATIONS_PERIODIC_DIGEST_QUEUE)
        private readonly periodicDigestEmailQueue: PeriodicDigestEmailQueue,
        @InjectQueue(NOTIFICATIONS_GROUP_SHARE_QUEUE)
        private readonly groupShareEmailQueue: GroupShareEmailQueue,
    ) {}

    async sendPeriodicDigest(period: NotificationCadence): Promise<void> {
        const users: {
            id: string;
            email: string;
            firstName: string;
            lastName: string;
        }[] = await this.prisma.$queryRawWithTenant`
            SELECT
                DISTINCT "userId" AS id,
                email,
                "firstName",
                "lastName"
            FROM
                "UserTopicPreference"
                NATURAL FULL JOIN "UserTagPreference"
                NATURAL FULL JOIN "UserGroupPreference"
                JOIN "User" on "User".id = "userId"
            WHERE
                "notificationCadence" = ${period}::"NotificationCadence"
                OR "highRelevanceNotifications" = ${period}::"NotificationCadence"        
                OR "negativeSentimentNotifications" = ${period}::"NotificationCadence"
        `;

        for (const user of users) {
            await this.periodicDigestEmailQueue.add(
                `${user.firstName} ${user.lastName}`,
                {
                    user: {
                        id: user.id,
                        email: user.email,
                    },
                    tenantId: this.cls.get("tenantId"),
                    period,
                },
            );
        }
    }

    async sendGroupShare(): Promise<void> {
        const oneDayAgo = new Date(Date.now() - 1000 * 60 * 60 * 24);
        const groups = await this.prisma.group.findMany({
            where: {
                shares: {
                    some: {
                        createdAt: {
                            gte: oneDayAgo,
                        },
                    },
                },
            },
        });

        const users = await this.prisma.user.findMany({
            where: {
                groups: {
                    some: {
                        group: { id: { in: groups.map((group) => group.id) } },
                    },
                },
            },
        });

        for (const user of users) {
            await this.groupShareEmailQueue.add(
                `${user.firstName} ${user.lastName}`,
                {
                    user: {
                        id: user.id,
                        email: user.email,
                    },
                    tenantId: this.cls.get("tenantId"),
                },
            );
        }
    }
}
