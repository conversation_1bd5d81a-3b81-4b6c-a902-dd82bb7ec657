import { Body, Controller, Post, Query, Req } from "@nestjs/common";
import { ApiCreatedResponse, ApiTags } from "@nestjs/swagger";

import { Request } from "express";

import { PaginatedResponseDto } from "../generic/dto/paginated.dto";
import { paginated } from "../generic/util";
import {
    SearchRequestDto,
    SearchRequestQueryDto,
    SearchResultDto,
} from "./dto/search.dto";
import { SearchService } from "./search.service";

@Controller("search")
@ApiTags("*** insights - chat")
export class SearchController {
    constructor(private readonly searchService: SearchService) {}

    @Post()
    @ApiCreatedResponse({
        type: PaginatedResponseDto<SearchResultDto>,
    })
    async search(
        @Req() req: Request,
        @Query() query: SearchRequestQueryDto,
        @Body() body: SearchRequestDto,
    ): Promise<PaginatedResponseDto<SearchResultDto>> {
        const userId = req.user["userId"];
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { page: _page, size: _size, ...filters } = query;
        const result = await paginated(
            (take, skip) =>
                this.searchService.search({
                    query: body.query,
                    type: "hybrid",
                    take,
                    skip,
                    userId,
                    ...filters,
                }),
            query,
        );
        return result;
    }
}
