import { Prisma } from "@prisma/client";
import { uniq } from "lodash";
import { DateFilteredRequestDto } from "src/generic/dto/date-filtered.dto";
import { Temporal } from "temporal-polyfill";

import { enumList } from "../generic/prisma";
import { SortKey, TimelineFilterParams } from "./dto/timeline.dto";
import { AnalyticsFiltersWithLocalRange } from "./types";

export function getLocalDateRangeStart(date: string, timeZone: string): Date {
    const plainDate = Temporal.PlainDate.from(date);
    const zonedDateTime = plainDate.toZonedDateTime({
        timeZone,
        plainTime: Temporal.PlainTime.from("00:00:00"),
    });
    return new Date(zonedDateTime.toInstant().toString());
}

export function getLocalDateRangeEnd(date: string, timeZone: string): Date {
    const plainDate = Temporal.PlainDate.from(date);
    const zonedDateTime = plainDate.toZonedDateTime({
        timeZone,
        plainTime: Temporal.PlainTime.from("23:59:59"),
    });
    return new Date(zonedDateTime.toInstant().toString());
}

export function getLocalDateRange(query: DateFilteredRequestDto): {
    localDateFrom: Date;
    localDateTo: Date;
} {
    const dateFrom = query.dateFrom ?? "1900-01-01";
    const dateTo = query.dateTo ?? new Date().toISOString().slice(0, 10);
    const timeZone = query.timeZone;

    const localDateFrom = getLocalDateRangeStart(dateFrom, timeZone);
    const localDateTo = getLocalDateRangeEnd(dateTo, timeZone);
    return {
        localDateFrom,
        localDateTo,
    };
}

export function getSentimentFilter(
    sentiments: TimelineFilterParams["sentiments"] = [],
): Prisma.AnalysisWhereInput {
    const FILTERS = {
        POSITIVE: {
            sentiment: {
                gte: 50,
                lte: 100,
            },
        },
        NEGATIVE: {
            sentiment: {
                gte: 0,
                lte: 50,
            },
        },
        UNKNOWN: {
            sentiment: {
                gte: -1,
                lte: -1,
            },
        },
    };

    const filters = sentiments.map((sentiment) => FILTERS[sentiment]);

    if (filters.length === 0) return {};
    if (filters.length === 1) return filters[0];
    return {
        OR: filters,
    };
}

export function getRawSentimentFilter(
    sentiments: TimelineFilterParams["sentiments"] = [],
): Prisma.Sql {
    if (sentiments.length === 0) return Prisma.empty;

    const filters = [];

    if (sentiments.includes("POSITIVE"))
        filters.push(Prisma.sql`"Analysis"."sentiment" >= 50`);
    if (sentiments.includes("NEGATIVE"))
        filters.push(Prisma.sql`"Analysis"."sentiment" <= 50`);
    if (sentiments.includes("UNKNOWN"))
        filters.push(Prisma.sql`"Analysis"."sentiment" = -1`);

    return Prisma.sql`(${Prisma.join(filters, " OR ")})`;
}

export function getTimelineOrderBy(
    sort: SortKey = "RELEVANCE",
): Prisma.TimelineItemOrderByWithRelationInput[] {
    const BASE_ORDERS: Partial<
        Record<
            SortKey,
            | Prisma.TimelineItemOrderByWithRelationInput
            | Prisma.TimelineItemOrderByWithRelationInput[]
        >
    > = {
        RELEVANCE: {
            analysis: {
                relevance: "desc",
            },
        },
        ENGAGEMENT: [
            {
                commentCount: "desc",
            },
            {
                likeCount: "desc",
            },
        ],
        DATE: {
            publishedAt: "desc",
        },
    };

    const sortKeys = uniq([sort, "RELEVANCE", "ENGAGEMENT", "DATE"]);

    const prismaOrder: Prisma.TimelineItemOrderByWithRelationInput[] = sortKeys
        .flatMap((key) => BASE_ORDERS[key])
        .filter(Boolean);

    if (sort === "POSITIVE_SENTIMENT") {
        prismaOrder.unshift({
            analysis: {
                sentiment: "desc",
            },
        });
    }

    if (sort === "NEGATIVE_SENTIMENT") {
        prismaOrder.unshift({
            analysis: {
                sentiment: "asc",
            },
        });
    }

    return prismaOrder;
}

export function getAnalyticsSqlFilters(
    filters: Partial<AnalyticsFiltersWithLocalRange> = {},
): Prisma.Sql[] {
    const dateFilter =
        filters.localDateFrom &&
        filters.localDateTo &&
        Prisma.sql`AND "TimelineItem"."publishedAt" BETWEEN ${filters.localDateFrom} AND ${filters.localDateTo}`;

    const topicFilter =
        filters.topicIds?.length &&
        Prisma.sql`AND "Topic"."id" IN (${Prisma.join(filters.topicIds)})`;

    const sentimentFilter =
        filters.sentiments?.length &&
        Prisma.sql`AND ${getRawSentimentFilter(filters.sentiments)}`;

    const sourceFilter =
        filters.sources?.length &&
        Prisma.sql`AND "TimelineItem"."source" IN (${enumList(
            filters.sources,
            "DataSource",
        )})`;

    const narrativeFilter =
        filters.narrativeIds?.length &&
        Prisma.sql`AND "Narrative"."id" IN (${Prisma.join(
            filters.narrativeIds,
        )})`;

    const aspectFilter =
        filters.aspects?.length &&
        Prisma.sql`AND "Narrative"."aspect" IN (${enumList(
            filters.aspects,
            "NarrativeAspect",
        )})`;

    const sqlFilters = [
        dateFilter,
        topicFilter,
        sentimentFilter,
        sourceFilter,
        narrativeFilter,
        aspectFilter,
    ];

    return sqlFilters.filter(Boolean);
}
