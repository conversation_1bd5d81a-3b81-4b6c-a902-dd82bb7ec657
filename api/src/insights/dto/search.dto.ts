import { ApiPropertyOptional } from "@nestjs/swagger";

import { Transform } from "class-transformer";
import {
    IsArray,
    IsBoolean,
    IsNumber,
    IsOptional,
    IsString,
} from "class-validator";

import { PaginatedRequestDto } from "../../generic/dto/paginated.dto";
import { GroupShareFilterParams } from "./shared.dto";
import { TimelineItemDto } from "./timeline.dto";

export interface SearchFilterParams extends GroupShareFilterParams {
    bookmarkedOnly?: boolean;
}

export class SearchRequestDto {
    @IsString()
    query: string;
}

export class SearchRequestQueryDto
    extends PaginatedRequestDto
    implements SearchFilterParams
{
    @IsOptional()
    @IsBoolean()
    @Transform(({ value }) => value === "true" || value === true)
    @ApiPropertyOptional({
        default: false,
    })
    bookmarkedOnly?: boolean;

    @IsOptional()
    @IsArray()
    @IsNumber({}, { each: true })
    @Transform(({ value }) =>
        Array.isArray(value) ? value.map(Number) : [Number(value)],
    )
    sharedWithGroupIds?: number[] = [];
}

export class SearchResultDto extends TimelineItemDto {
    @IsNumber()
    score: number;

    @IsString()
    searchType: "semantic" | "keyword";
}
