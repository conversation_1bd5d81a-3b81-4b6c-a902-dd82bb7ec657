import { ApiPropertyOptional } from "@nestjs/swagger";

import { DataSource, NarrativeAspect } from "@prisma/client";
import { Transform } from "class-transformer";
import {
    IsOptional,
    IsEnum,
    IsArray,
    IsNumber,
    IsBoolean,
} from "class-validator";

export const Sentiment = {
    POSITIVE: "POSITIVE",
    NEGATIVE: "NEGATIVE",
    UNKNOWN: "UNKNOWN",
} as const;

export type Sentiment = (typeof Sentiment)[keyof typeof Sentiment];

export type SentimentFilterParams = {
    sentiments?: Sentiment[];
};

export type TopicFilterParams = {
    topicIds?: number[];
};

export type SourceFilterParams = {
    sources?: DataSource[];
};

export type NarrativeFilterParams = {
    narrativeIds?: number[];
};

export type NarrativeAspectFilterParams = {
    aspects?: NarrativeAspect[];
};

export type GroupFilterParams = {
    groupIds?: number[];
};

export type GroupShareFilterParams = {
    sharedWithGroupIds?: number[];
};

export type FavouriteFilterParams = {
    onlyFavourited?: boolean;
};

export class SentimentFilteredDto implements SentimentFilterParams {
    @IsOptional()
    @IsEnum(Sentiment, { each: true })
    @Transform(({ value }) => (Array.isArray(value) ? value : [value]))
    @ApiPropertyOptional({
        isArray: true,
        enum: Sentiment,
        example: [],
        default: [],
    })
    sentiments?: Sentiment[] = [];
}

export class TopicFilteredDto implements TopicFilterParams {
    @IsArray()
    @ApiPropertyOptional({
        type: Number,
        isArray: true,
        description: "Array of topic IDs",
        example: [],
    })
    @Transform(({ value }) =>
        Array.isArray(value) ? value.map(Number) : [Number(value)],
    )
    @IsOptional()
    topicIds?: number[] = [];
}

export class SourceFilteredDto implements SourceFilterParams {
    @IsOptional()
    @IsEnum(DataSource, { each: true })
    @Transform(({ value }) => (Array.isArray(value) ? value : [value]))
    @ApiPropertyOptional({
        isArray: true,
        enum: DataSource,
        example: [],
        default: [],
    })
    sources?: DataSource[] = [];
}
export class NarrativeFilteredDto implements NarrativeFilterParams {
    @IsOptional()
    @IsArray()
    @IsNumber({}, { each: true })
    @Transform(({ value }) =>
        Array.isArray(value) ? value.map(Number) : [Number(value)],
    )
    @ApiPropertyOptional({
        isArray: true,
        type: Number,
        example: [],
    })
    narrativeIds?: number[] = [];
}

export class NarrativeAspectFilteredDto implements NarrativeAspectFilterParams {
    @IsOptional()
    @IsArray()
    @IsEnum(NarrativeAspect, { each: true })
    @Transform(({ value }) => (Array.isArray(value) ? value : [value]))
    @ApiPropertyOptional({
        isArray: true,
        enum: NarrativeAspect,
        example: [],
        default: [],
    })
    aspects?: NarrativeAspect[] = [];
}

export class GroupFilteredDto implements GroupFilterParams {
    @IsOptional()
    @IsArray()
    @IsNumber({}, { each: true })
    @Transform(({ value }) =>
        Array.isArray(value) ? value.map(Number) : [Number(value)],
    )
    groupIds?: number[] = [];
}

export class GroupShareFilteredDto implements GroupShareFilterParams {
    @IsOptional()
    @IsArray()
    @IsNumber({}, { each: true })
    @Transform(({ value }) =>
        Array.isArray(value) ? value.map(Number) : [Number(value)],
    )
    sharedWithGroupIds?: number[] = [];
}

export class FavouriteFilteredDto implements FavouriteFilterParams {
    @IsOptional()
    @IsBoolean()
    @Transform(({ value }) => value === "true" || value === true)
    onlyFavourited?: boolean = false;
}
