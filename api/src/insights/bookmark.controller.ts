import { <PERSON>, Delete, Param, Post, Req, Res } from "@nestjs/common";
import { ApiNoContentResponse, ApiOkResponse, ApiTags } from "@nestjs/swagger";

import { Request, Response } from "express";

import { BookmarkService } from "./bookmark.service";

@Controller("insights/timeline")
@ApiTags("*** insights - timeline")
export class BookmarkController {
    constructor(private readonly bookmarkService: BookmarkService) {}

    @Post(":analysisId/bookmark")
    @ApiOkResponse()
    async bookmarkAnalysis(
        @Req() req: Request,
        @Param("analysisId") analysisId: string,
    ): Promise<void> {
        const userId = req.user["userId"];
        await this.bookmarkService.bookmarkAnalysis(Number(analysisId), userId);
    }

    @Delete(":analysisId/bookmark")
    @ApiNoContentResponse()
    async unbookmarkAnalysis(
        @Req() req: Request,
        @Param("analysisId") analysisId: string,
        @Res() res: Response,
    ): Promise<void> {
        const userId = req.user["userId"];
        await this.bookmarkService.unbookmarkAnalysis(
            Number(analysisId),
            userId,
        );

        res.sendStatus(204);
    }
}
