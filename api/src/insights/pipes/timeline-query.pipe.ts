import { PipeTransform, Injectable, Logger } from "@nestjs/common";

import { PrismaService } from "../../prisma/prisma.service";
import { TimelineRequestDto } from "../dto/timeline.dto";

const MICROCHIP_INC_TOPIC = "Microchip Technology Inc";

@Injectable()
export class TimelineQueryPipe implements PipeTransform {
    private logger = new Logger("TimelineQueryPipe");

    constructor(private readonly prisma: PrismaService) {}

    async transform(value: TimelineRequestDto): Promise<TimelineRequestDto> {
        // High relevance requests automatically get topic filter for "Microchip Technology Inc"
        const minRelevance = value.minRelevance;
        if (!(minRelevance >= 87.5)) return value;

        const microchipTopics = await this.prisma.topic.findMany({
            where: { name: { startsWith: MICROCHIP_INC_TOPIC } },
        });
        if (microchipTopics.length < 1) {
            this.logger.warn(
                `No topic found matching "${MICROCHIP_INC_TOPIC}"`,
            );
            return value;
        }
        if (microchipTopics.length > 1) {
            this.logger.warn(
                `More than one topic found matching "${MICROCHIP_INC_TOPIC}"`,
            );
            return value;
        }
        const [microchipTopic] = microchipTopics;

        const topicIds = [...new Set([microchipTopic.id, ...value.topicIds])];

        if (topicIds.length !== value.topicIds.length) {
            this.logger.log(`Adding "${MICROCHIP_INC_TOPIC}" topic filter`);
        }

        return { ...value, topicIds };
    }
}
