import { Injectable } from "@nestjs/common";

import { PrismaService } from "../prisma/prisma.service";

@Injectable()
export class BookmarkService {
    constructor(private readonly prisma: PrismaService) {}

    async bookmarkAnalysis(analysisId: number, userId: string) {
        await this.prisma.analysis.findUniqueOrThrow({
            where: { id: analysisId },
        });

        await this.prisma.analysis.update({
            where: { id: analysisId },
            data: {
                bookmarkedBy: {
                    connect: {
                        id: userId,
                    },
                },
            },
        });
    }

    async unbookmarkAnalysis(analysisId: number, userId: string) {
        await this.prisma.analysis.update({
            where: { id: analysisId },
            data: {
                bookmarkedBy: {
                    disconnect: {
                        id: userId,
                    },
                },
            },
        });
    }
}
