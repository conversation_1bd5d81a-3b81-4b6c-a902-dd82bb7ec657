import { Prisma } from "@prisma/client";

import { enumList } from "../generic/prisma";
import {
    getAnalyticsSqlFilters,
    getRawSentimentFilter,
    getSentimentFilter,
    getTimelineOrderBy,
} from "./util";

describe("getSentimentFilter", () => {
    it("should return an empty object if no sentiments are provided", () => {
        const actual = getSentimentFilter();
        expect(actual).toEqual({});
    });

    it("should return a filter for positive sentiments", () => {
        const actual = getSentimentFilter(["POSITIVE"]);
        expect(actual).toEqual({
            sentiment: {
                gte: 50,
                lte: 100,
            },
        });
    });

    it("should return a filter for negative sentiments", () => {
        const actual = getSentimentFilter(["NEGATIVE"]);
        expect(actual).toEqual({
            sentiment: {
                gte: 0,
                lte: 50,
            },
        });
    });

    it("should return a filter for both positive and negative sentiments", () => {
        const actual = getSentimentFilter(["POSITIVE", "NEGATIVE"]);
        expect(actual).toEqual({
            OR: [
                {
                    sentiment: {
                        gte: 50,
                        lte: 100,
                    },
                },
                {
                    sentiment: {
                        gte: 0,
                        lte: 50,
                    },
                },
            ],
        });
    });

    it("should return a filter for unknown sentiments", () => {
        const actual = getSentimentFilter(["UNKNOWN"]);
        expect(actual).toEqual({
            sentiment: {
                gte: -1,
                lte: -1,
            },
        });
    });

    it("should return a filter for all sentiments", () => {
        const actual = getSentimentFilter(["POSITIVE", "NEGATIVE", "UNKNOWN"]);
        expect(actual).toEqual({
            OR: [
                {
                    sentiment: {
                        gte: 50,
                        lte: 100,
                    },
                },
                {
                    sentiment: {
                        gte: 0,
                        lte: 50,
                    },
                },
                {
                    sentiment: {
                        gte: -1,
                        lte: -1,
                    },
                },
            ],
        });
    });
});

describe("getRawSentimentFilter", () => {
    it("should return empty if no sentiments are provided", () => {
        const actual = getRawSentimentFilter();
        expect(actual).toEqual(Prisma.empty);
    });

    it("should return a filter for positive sentiments", () => {
        const actual = getRawSentimentFilter(["POSITIVE"]);
        expect(actual).toEqual(Prisma.sql`("Analysis"."sentiment" >= 50)`);
    });

    it("should return a filter for negative sentiments", () => {
        const actual = getRawSentimentFilter(["NEGATIVE"]);
        expect(actual).toEqual(Prisma.sql`("Analysis"."sentiment" <= 50)`);
    });

    it("should return a filter for unknown sentiments", () => {
        const actual = getRawSentimentFilter(["UNKNOWN"]);
        expect(actual).toEqual(Prisma.sql`("Analysis"."sentiment" = -1)`);
    });

    it("should return a filter for all sentiments", () => {
        const actual = getRawSentimentFilter([
            "POSITIVE",
            "NEGATIVE",
            "UNKNOWN",
        ]);
        expect(actual).toEqual(
            Prisma.sql`("Analysis"."sentiment" >= 50 OR "Analysis"."sentiment" <= 50 OR "Analysis"."sentiment" = -1)`,
        );
    });
});

describe("getTimelineOrderBy", () => {
    it("defaults to relevance, comment count, like count, date", () => {
        const actual = getTimelineOrderBy();
        expect(actual).toEqual([
            {
                analysis: {
                    relevance: "desc",
                },
            },
            {
                commentCount: "desc",
            },
            {
                likeCount: "desc",
            },
            {
                publishedAt: "desc",
            },
        ]);
    });

    it("re-orders when sort is provided", () => {
        const actual = getTimelineOrderBy("DATE");
        expect(actual).toEqual([
            {
                publishedAt: "desc",
            },
            {
                analysis: {
                    relevance: "desc",
                },
            },
            {
                commentCount: "desc",
            },
            {
                likeCount: "desc",
            },
        ]);
    });

    it("supports optional sentiment sort", () => {
        const actual = getTimelineOrderBy("POSITIVE_SENTIMENT");
        expect(actual).toEqual([
            {
                analysis: {
                    sentiment: "desc",
                },
            },
            {
                analysis: {
                    relevance: "desc",
                },
            },
            {
                commentCount: "desc",
            },
            {
                likeCount: "desc",
            },
            {
                publishedAt: "desc",
            },
        ]);
    });

    it("handles negative sentiment sort", () => {
        const actual = getTimelineOrderBy("NEGATIVE_SENTIMENT");
        expect(actual).toEqual([
            {
                analysis: {
                    sentiment: "asc",
                },
            },
            {
                analysis: {
                    relevance: "desc",
                },
            },
            {
                commentCount: "desc",
            },
            {
                likeCount: "desc",
            },
            {
                publishedAt: "desc",
            },
        ]);
    });
});

describe("getAnalyticsSqlFilters", () => {
    it("returns an empty array", () => {
        const actual = getAnalyticsSqlFilters();
        expect(actual).toEqual([]);
    });

    it("returns date range filters", () => {
        const actual = getAnalyticsSqlFilters({
            localDateFrom: new Date("2025-01-01"),
            localDateTo: new Date("2025-01-02"),
        });
        expect(actual).toEqual([
            Prisma.sql`AND "TimelineItem"."publishedAt" BETWEEN ${new Date("2025-01-01")} AND ${new Date("2025-01-02")}`,
        ]);
    });

    it("returns a topic filter", () => {
        const actual = getAnalyticsSqlFilters({
            topicIds: [1, 2],
        });
        expect(actual).toEqual([
            Prisma.sql`AND "Topic"."id" IN (${Prisma.join([1, 2])})`,
        ]);
    });

    it("returns a sentiment filter", () => {
        const actual = getAnalyticsSqlFilters({
            sentiments: ["POSITIVE"],
        });
        expect(actual).toEqual([
            Prisma.sql`AND ("Analysis"."sentiment" >= 50)`,
        ]);
    });

    it("returns a source filter", () => {
        const actual = getAnalyticsSqlFilters({
            sources: ["AVR_FREAKS"],
        });
        expect(actual).toEqual([
            Prisma.sql`AND "TimelineItem"."source" IN (${enumList(["AVR_FREAKS"], "DataSource")})`,
        ]);
    });

    it("returns a narrative filter", () => {
        const actual = getAnalyticsSqlFilters({
            narrativeIds: [1, 2],
        });
        expect(actual).toEqual([
            Prisma.sql`AND "Narrative"."id" IN (${Prisma.join([1, 2])})`,
        ]);
    });

    it("returns an aspect filter", () => {
        const actual = getAnalyticsSqlFilters({
            aspects: ["LEARNING_CURVE"],
        });
        expect(actual).toEqual([
            Prisma.sql`AND "Narrative"."aspect" IN (${enumList(["LEARNING_CURVE"], "NarrativeAspect")})`,
        ]);
    });
});
