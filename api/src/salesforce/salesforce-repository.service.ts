import { Injectable } from "@nestjs/common";

import { PrismaService } from "../prisma/prisma.service";

const ONE_DAY = 1 * 24 * 60 * 60 * 1000;
const REFRESH_SCHEDULE = [3, 7, 30, 60, 90];

@Injectable()
export class SalesforceRepositoryService {
    constructor(private readonly prisma: PrismaService) {}

    async *getPostsPendingUpdate() {
        for (const updateDay of REFRESH_SCHEDULE) {
            for (const post of await this.getPostsWithExactAge(updateDay)) {
                yield post;
            }
        }
    }

    private async getPostsWithExactAge(daysAgo: number) {
        const today = new Date(Date.now());
        today.setHours(0, 0, 0, 0);

        const targetDate = new Date(today.valueOf() - daysAgo * ONE_DAY);

        const dateStart = new Date(targetDate);
        const dateEnd = new Date(targetDate);
        dateEnd.setHours(23, 59, 59, 999);

        return this.prisma.salesforcePost.findMany({
            select: {
                id: true,
                url: true,
                title: true,
                forum: {
                    select: {
                        name: true,
                        site: true,
                    },
                },
            },
            where: {
                AND: {
                    createdAt: {
                        gte: dateStart,
                        lte: dateEnd,
                    },
                },
            },
        });
    }
}
