import { BullModule } from "@nestjs/bullmq";
import { Module } from "@nestjs/common";

import { BullMQAdapter } from "@bull-board/api/bullMQAdapter";
import { BullBoardModule } from "@bull-board/nestjs";

import { CacheModule } from "../cache/cache.module";
import { SalesforceApiModule } from "../salesforce-api/salesforce-api.module";
import {
    AI_ANALYSIS_QUEUE,
    SALESFORCE_FORUM_POST_QUEUE,
    SALESFORCE_FORUM_POST_REFRESH_QUEUE,
    SALESFORCE_FORUM_QUEUE,
    SALESFORCE_REFRESH_QUEUE,
} from "../task/constants";
import { SalesforceForumProcessor } from "./forum.processor";
import { SalesforceForumPostRefreshProcessor } from "./post-refresh.processor";
import { SalesforceForumPostProcessor } from "./post.processor";
import { SalesforceRefreshProcessor } from "./refresh.processor";
import { SalesforceRepositoryService } from "./salesforce-repository.service";
import { SalesforceService } from "./salesforce.service";

@Module({
    imports: [
        BullBoardModule.forFeature(
            {
                name: SALESFORCE_FORUM_QUEUE,
                adapter: BullMQAdapter,
            },
            {
                name: SALESFORCE_FORUM_POST_QUEUE,
                adapter: BullMQAdapter,
            },
            {
                name: SALESFORCE_REFRESH_QUEUE,
                adapter: BullMQAdapter,
            },
            {
                name: SALESFORCE_FORUM_POST_REFRESH_QUEUE,
                adapter: BullMQAdapter,
            },
        ),
        BullModule.registerQueue(
            { name: SALESFORCE_FORUM_QUEUE },
            { name: SALESFORCE_FORUM_POST_QUEUE },
            { name: SALESFORCE_REFRESH_QUEUE },
            { name: SALESFORCE_FORUM_POST_REFRESH_QUEUE },
            { name: AI_ANALYSIS_QUEUE },
        ),
        CacheModule,
        SalesforceApiModule,
    ],
    providers: [
        SalesforceForumPostProcessor,
        SalesforceForumPostRefreshProcessor,
        SalesforceForumProcessor,
        SalesforceRefreshProcessor,
        SalesforceRepositoryService,
        SalesforceService,
    ],
    exports: [SalesforceService],
})
export class SalesforceModule {}
