import { InjectQueue, Processor, WorkerHost } from "@nestjs/bullmq";
import { InternalServerErrorException } from "@nestjs/common";

import { UseTenantCls } from "../generic/bullmq/processor";
import { PrismaService } from "../prisma/prisma.service";
import {
    AI_ANALYSIS_QUEUE,
    SALESFORCE_FORUM_POST_REFRESH_QUEUE,
} from "../task/constants";
import { AIAnalysisQueue, SalesforceForumPostRefreshJob } from "../task/types";
import { SalesforceService } from "./salesforce.service";

@Processor(SALESFORCE_FORUM_POST_REFRESH_QUEUE)
export class SalesforceForumPostRefreshProcessor extends WorkerHost {
    constructor(
        private readonly prisma: PrismaService,
        private readonly salesforceService: SalesforceService,
        @InjectQueue(AI_ANALYSIS_QUEUE)
        private readonly aiAnalysisQueue: AIAnalysisQueue,
    ) {
        super();
    }

    @UseTenantCls()
    async process(job: SalesforceForumPostRefreshJob) {
        const { siteName } = job.data;
        const topLevelPostId = job.data.postId;

        // Update view count
        const topic = await this.salesforceService.getTopic(
            siteName,
            job.data.forumName,
            topLevelPostId,
        );

        this.prisma.salesforcePost.update({
            where: {
                id: topLevelPostId,
            },
            data: {
                viewCount: topic.topicViewCount,
            },
        });

        // Update replies
        const originalReplyCount = await this.prisma.salesforcePostReply.count({
            where: {
                post: {
                    id: topLevelPostId,
                },
            },
        });

        const { total, items: replies } = await this.salesforceService.getPosts(
            siteName,
            topLevelPostId,
        );

        if (total > replies.length) {
            throw new InternalServerErrorException(
                `Forum thread has ${total} replies, but only fetched ${replies.length}`,
            );
        }

        job.log(`${replies.length - 1} replies found`);

        let replyCount = 0;

        for (const reply of replies) {
            const stats = {
                memberLikeCount: reply.memberLikeCount,
                memberDislikeCount: reply.memberDislikeCount,
                guestLikeCount: reply.guestLikeCount,
                guestDislikeCount: reply.guestDislikeCount,
            };

            if (reply.isStarterPost) {
                await this.prisma.salesforcePost.update({
                    where: {
                        id: topLevelPostId,
                    },
                    data: {
                        ...stats,
                    },
                });
                continue;
            }

            const replyId = reply.postid;
            const {
                textContent: text,
                createdDate: publishedAt,
                name,
                userScreenName,
            } = reply;

            await this.prisma.salesforcePostReply.upsert({
                where: {
                    id: replyId,
                    post: {
                        id: topLevelPostId,
                    },
                },
                create: {
                    id: replyId,
                    post: {
                        connect: {
                            id: topLevelPostId,
                        },
                    },
                    text,
                    publishedAt,
                    name,
                    userScreenName,
                    ...stats,
                },
                update: {
                    ...stats,
                },
            });

            replyCount++;
        }

        const newReplyCount = replyCount - originalReplyCount;
        job.log(`${newReplyCount} replies added`);

        if (newReplyCount === 0) return;

        // Re-analyze thread
        await this.prisma.requestCache.deleteMany({
            where: {
                key: {
                    contains: topLevelPostId,
                },
            },
        });

        await this.aiAnalysisQueue.add("salesforce", {
            type: "salesforce",
            forumName: job.data.forumName,
            siteName: job.data.siteName,
            topicId: topLevelPostId,
            topicURL: job.data.postURL,
            tenantId: job.data.tenantId,
        });
    }
}
