import { InjectQueue, Processor, WorkerHost } from "@nestjs/bullmq";

import { UseTenantCls } from "../generic/bullmq/processor";
import {
    SALESFORCE_FORUM_POST_REFRESH_QUEUE,
    SALESFORCE_REFRESH_QUEUE,
} from "../task/constants";
import {
    RedditRefreshJob,
    SalesforceForumPostRefreshQueue,
} from "../task/types";
import { SalesforceRepositoryService } from "./salesforce-repository.service";
import { SalesforceSiteName } from "./types";

@Processor(SALESFORCE_REFRESH_QUEUE)
export class SalesforceRefreshProcessor extends WorkerHost {
    constructor(
        @InjectQueue(SALESFORCE_FORUM_POST_REFRESH_QUEUE)
        private readonly salesforceForumPostRefreshQueue: SalesforceForumPostRefreshQueue,
        private readonly salesforceRepositoryService: SalesforceRepositoryService,
    ) {
        super();
    }

    @UseTenantCls()
    async process(job: RedditRefreshJob) {
        let postCount = 0;

        const postsPendingUpdateGenerator =
            this.salesforceRepositoryService.getPostsPendingUpdate();

        for await (const post of postsPendingUpdateGenerator) {
            const refreshJob = {
                forumName: post.forum.name,
                siteName: post.forum.site.name as SalesforceSiteName,
                postId: post.id,
                postURL: post.url,
                tenantId: job.data.tenantId,
            };
            await this.salesforceForumPostRefreshQueue.add(
                post.title,
                refreshJob,
            );
            postCount += 1;
        }

        job.log(`Added ${postCount} Salesforce post refresh jobs`);
    }
}
