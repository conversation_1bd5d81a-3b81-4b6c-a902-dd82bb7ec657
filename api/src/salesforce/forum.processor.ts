import { InjectQueue, Processor, WorkerHost } from "@nestjs/bullmq";

import { UseTenantCls } from "../generic/bullmq/processor";
import { PrismaService } from "../prisma/prisma.service";
import {
    SALESFORCE_FORUM_POST_QUEUE,
    SALESFORCE_FORUM_QUEUE,
} from "../task/constants";
import { SalesforceForumJob, SalesforceForumPostQueue } from "../task/types";
import { SalesforceService } from "./salesforce.service";

@Processor(SALESFORCE_FORUM_QUEUE)
export class SalesforceForumProcessor extends WorkerHost {
    constructor(
        private readonly prisma: PrismaService,
        @InjectQueue(SALESFORCE_FORUM_POST_QUEUE)
        private readonly salesforceForumPostQueue: SalesforceForumPostQueue,
        private readonly salesforceService: SalesforceService,
    ) {
        super();
    }

    @UseTenantCls()
    async process(job: SalesforceForumJob) {
        const { siteName, forumName, limit, dateAfter, dateBefore } = job.data;

        const forum = await this.prisma.salesforceForum.findFirstOrThrow({
            where: { name: forumName },
        });

        // List "topics" (top level posts)
        const topicsGenerator = this.salesforceService.getTopics({
            site: siteName,
            forumName,
            limit,
            dateAfter,
            dateBefore,
        });

        let newPostCount = 0;
        for await (const topic of topicsGenerator) {
            const { id, topicTitle: title } = topic;

            job.log(title);

            const existingPost = await this.prisma.salesforcePost.findUnique({
                where: { id },
            });
            if (existingPost) continue;

            await this.prisma.salesforcePost.create({
                data: {
                    id,
                    title,
                    text: "",
                    publishedAt: topic.createdDate,
                    url: topic.url,
                    forum: {
                        connect: {
                            id: forum.id,
                        },
                    },
                },
            });

            // Add job to process the post
            await this.salesforceForumPostQueue.add(title, {
                siteName,
                forumName,
                topicTitle: title,
                topicId: id,
                topicURL: topic.url,
                tenantId: job.data.tenantId,
            });

            newPostCount += 1;
        }

        job.log(`Added ${newPostCount} new posts`);
    }
}
