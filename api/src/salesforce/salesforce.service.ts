import { BadGatewayException, Injectable } from "@nestjs/common";

import { cachified } from "@epic-web/cachified";
import { SalesforceGetTopicsParams } from "src/salesforce-api/types";
import { z, ZodObject, ZodRawShape } from "zod";

import { CacheService } from "../cache/cache.service";
import { cacheKeys } from "../cache/constants";
import { SalesforceApiService } from "../salesforce-api/salesforce-api.service";
import {
    SalesforceAuthResponse,
    SalesforceForumResponse,
    SalesforcePostResponse,
    SalesforceSiteName,
    SalesforceTopicResponse,
} from "./types";
import { extractPostURL } from "./util";

const ACCESS_TOKEN_TTL = 60 * 60 * 1000; // 1 hour

const MAX_PAGE_SIZE = 2000;

@Injectable()
export class SalesforceService {
    constructor(
        private readonly salesforceApiService: SalesforceApiService,
        private readonly cacheService: CacheService,
    ) {}

    private async getAccessToken(): Promise<string> {
        const token = cachified({
            key: cacheKeys.SALESFORCE_ACCESS_TOKEN,
            cache: this.cacheService.cache,
            ttl: ACCESS_TOKEN_TTL,
            getFreshValue: async () => {
                const rawResponse =
                    await this.salesforceApiService.getAccessToken();
                const { access_token } = this.parseResponse(
                    rawResponse,
                    SalesforceAuthResponse,
                );
                return access_token;
            },
        });

        if (!token) {
            throw new BadGatewayException(
                "Failed to get access token from Salesforce",
            );
        }

        return token;
    }

    private async getForums(
        site: SalesforceSiteName,
        type?: "Forum" | "Sub Forum",
    ): Promise<SalesforceForumResponse> {
        const accessToken = await this.getAccessToken();
        const rawResponse = await this.salesforceApiService.getForums(
            accessToken,
            site,
            type,
        );

        const parsed = this.parseResponse(rawResponse, SalesforceForumResponse);
        return parsed;
    }

    async getTopLevelForums(
        site: SalesforceSiteName,
    ): Promise<SalesforceForumResponse> {
        return this.getForums(site, "Forum");
    }

    async getSubForums(
        site: SalesforceSiteName,
    ): Promise<SalesforceForumResponse> {
        return this.getForums(site, "Sub Forum");
    }

    async *getTopics(params: {
        site: SalesforceSiteName;
        forumName: string;
        limit: number;
        dateAfter?: string;
        dateBefore?: string;
    }) {
        const { site, forumName, limit, dateAfter, dateBefore } = params;
        let remaining = limit ?? MAX_PAGE_SIZE;
        let lastId: string | undefined;

        while (remaining > 0) {
            const pageSize = Math.min(remaining, MAX_PAGE_SIZE);
            const items = await this.getTopicsPaginated({
                site,
                forumName,
                pageSize,
                lastId,
                dateAfter,
                dateBefore,
            });

            if (items.length === 0) break;

            for (const item of items) {
                yield item;
                lastId = item.id;
            }

            remaining -= items.length;
        }
    }

    private async getTopicsPaginated(params: SalesforceGetTopicsParams) {
        const accessToken = await this.getAccessToken();
        const rawResponse = await this.salesforceApiService.getTopics(
            accessToken,
            params,
        );

        const parsed = this.parseResponse(rawResponse, SalesforceTopicResponse);

        const items = parsed.records.map((record) => ({
            ...record,
            id: record.topicid,
            url: extractPostURL(record.url),
        }));

        return items;
    }

    async getTopic(site: SalesforceSiteName, forumName: string, id: string) {
        const accessToken = await this.getAccessToken();
        const rawResponse = await this.salesforceApiService.getTopics(
            accessToken,
            {
                site,
                forumName,
                pageSize: 1,
                id,
            },
        );

        const parsed = this.parseResponse(rawResponse, SalesforceTopicResponse);
        return parsed.records[0];
    }

    async getPosts(site: SalesforceSiteName, topicId: string) {
        const accessToken = await this.getAccessToken();
        const rawResponse = await this.salesforceApiService.getPosts(
            accessToken,
            site,
            topicId,
        );

        const parsed = this.parseResponse(rawResponse, SalesforcePostResponse);

        return {
            total: parsed.recordcount,
            items: parsed.records,
        };
    }

    private parseResponse<T extends ZodRawShape>(
        rawResponse: unknown,
        schema: ZodObject<T>,
    ): z.infer<typeof schema> {
        const result = schema.safeParse(rawResponse);
        if (result.success) return result.data;
        throw new BadGatewayException(
            `Error parsing result ${JSON.stringify(rawResponse)}`,
        );
    }
}
