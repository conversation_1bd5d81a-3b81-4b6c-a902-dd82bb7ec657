import { extractPostURL } from "./util";

describe("extractPostURL", () => {
    it("should extract the post URL from the anchor", () => {
        const anchor = `<a href="https://microchip--mcuat.sandbox.my.site.com/microchip/s/topic/a5C3l0000003iog" target="_blank">New Getting the right _etext symbol when using xc32-ld and best fit allocator</a>`;
        const url = extractPostURL(anchor);
        expect(url).toBe(
            "https://microchip--mcuat.sandbox.my.site.com/microchip/s/topic/a5C3l0000003iog",
        );
    });
});
