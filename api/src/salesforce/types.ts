import { z } from "zod";

export enum SalesforceSiteName {
    AVR_FREAKS = "AVR_FREAKS",
    MICROCHIP_CLASSIC = "MICROCHIP_CLASSIC",
}

export const SalesforceAuthResponse = z.object({
    access_token: z.string(),
});

export const SalesforceForumResponse = z.object({
    RecordCount: z.number(),
    records: z.array(
        z.object({
            forumLevel: z.enum(["Site", "Forum", "Sub Forum"]),
            forumParentName: z.string().nullable(),
            forumName: z.string(),
            id: z.string(),
        }),
    ),
});

export const SalesforceTopicResponse = z.object({
    recordcount: z.number(),
    records: z.array(
        z.object({
            topicid: z.string(),
            topicTitle: z.string(),
            url: z.string(),
            topicViewCount: z.number().nullable(),
            createdDate: z.string(),
        }),
    ),
});

export const SalesforcePostResponse = z.object({
    recordcount: z.number(),
    records: z.array(
        z.object({
            postid: z.string(),
            textContent: z.string(),
            createdDate: z.string(),
            isStarterPost: z.boolean(),
            name: z.string(),
            userScreenName: z.string().nullable(),
            memberLikeCount: z.number(),
            memberDislikeCount: z.number(),
            guestLikeCount: z.number(),
            guestDislikeCount: z.number(),
        }),
    ),
});

export type SalesforceAuthResponse = z.infer<typeof SalesforceAuthResponse>;
export type SalesforceTopicResponse = z.infer<typeof SalesforceTopicResponse>;
export type SalesforceForumResponse = z.infer<typeof SalesforceForumResponse>;
export type SalesforcePostResponse = z.infer<typeof SalesforcePostResponse>;
