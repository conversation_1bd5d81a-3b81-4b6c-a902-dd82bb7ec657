import { InjectQueue, Processor, WorkerHost } from "@nestjs/bullmq";
import { InternalServerErrorException } from "@nestjs/common";

import { truncate } from "lodash";

import { UseTenantCls } from "../generic/bullmq/processor";
import { PrismaService } from "../prisma/prisma.service";
import {
    AI_ANALYSIS_QUEUE,
    SALESFORCE_FORUM_POST_QUEUE,
} from "../task/constants";
import { AIAnalysisQueue, SalesforceForumPostJob } from "../task/types";
import { SalesforceService } from "./salesforce.service";

@Processor(SALESFORCE_FORUM_POST_QUEUE)
export class SalesforceForumPostProcessor extends WorkerHost {
    constructor(
        private readonly prisma: PrismaService,
        private readonly salesforceService: SalesforceService,
        @InjectQueue(AI_ANALYSIS_QUEUE)
        private readonly aiAnalysisQueue: AIAnalysisQueue,
    ) {
        super();
    }

    @UseTenantCls()
    async process(job: SalesforceForumPostJob) {
        const { siteName, forumName, topicId, topicTitle, topicURL } = job.data;

        const { items: posts } = await this.salesforceService.getPosts(
            siteName,
            topicId,
        );

        const topLevelPost = posts.find((post) => post.isStarterPost);
        if (!topLevelPost) {
            throw new InternalServerErrorException(
                "Top level post record not found",
            );
        }

        const replies = posts.filter((post) => !post.isStarterPost);

        job.log(truncate(topLevelPost.textContent, { length: 100 }));
        await this.prisma.salesforcePost.update({
            where: {
                // We use the topicId as the id of the top level post even though it does have its own id too
                id: topicId,
            },
            data: {
                text: topLevelPost.textContent,
                memberLikeCount: topLevelPost.memberLikeCount,
                memberDislikeCount: topLevelPost.memberDislikeCount,
                guestLikeCount: topLevelPost.guestLikeCount,
                guestDislikeCount: topLevelPost.guestDislikeCount,
            },
        });

        for (const reply of replies) {
            job.log(truncate(reply.textContent, { length: 100 }));
            await this.prisma.salesforcePostReply.upsert({
                where: {
                    id: reply.postid,
                },
                update: {},
                create: {
                    id: reply.postid,
                    text: reply.textContent,
                    publishedAt: reply.createdDate,
                    name: reply.name,
                    userScreenName: reply.userScreenName,
                    memberLikeCount: reply.memberLikeCount,
                    memberDislikeCount: reply.memberDislikeCount,
                    guestLikeCount: reply.guestLikeCount,
                    guestDislikeCount: reply.guestDislikeCount,
                    post: {
                        connect: {
                            id: topicId,
                        },
                    },
                },
            });
        }

        await this.aiAnalysisQueue.add(topicTitle, {
            type: "salesforce",
            forumName,
            siteName,
            topicId,
            topicURL,
            tenantId: job.data.tenantId,
        });
    }
}
