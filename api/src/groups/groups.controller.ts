import {
    Body,
    Controller,
    Delete,
    Get,
    Param,
    Patch,
    Post,
    Put,
    Query,
    Req,
    Res,
} from "@nestjs/common";
import {
    ApiCreatedResponse,
    ApiNoContentResponse,
    ApiOkResponse,
    ApiTags,
} from "@nestjs/swagger";

import { Response, Request } from "express";

import { BulkPatchPreferenceRequestDto } from "../common/dto/preference.dto";
import { paginated } from "../generic/util";
import { Role } from "../roles/role.enum";
import { Roles } from "../roles/roles.decorator";
import { CreateGroupRequestDto } from "./dto/create-group.dto";
import {
    GetGroupsRequestDto,
    GetGroupsResponseDto,
    GroupDto,
} from "./dto/get-groups.dto";
import { UpdateGroupRequestDto } from "./dto/update-group.dto";
import { GroupsService } from "./groups.service";

@Controller("groups")
@ApiTags("** topic management - groups (teams)")
export class GroupsController {
    constructor(private readonly groupsService: GroupsService) {}

    @Get()
    @ApiOkResponse({ type: GetGroupsResponseDto })
    async getGroups(
        @Query() params: GetGroupsRequestDto,
        @Req() req: Request,
    ): Promise<GetGroupsResponseDto> {
        return paginated(
            (take, skip) =>
                this.groupsService.getGroups(
                    take,
                    skip,
                    params,
                    req.user["userId"],
                ),
            params,
        );
    }

    @Post()
    @Roles(Role.Admin)
    @ApiCreatedResponse()
    async createGroup(
        @Body() createGroupRequestDto: CreateGroupRequestDto,
    ): Promise<GroupDto> {
        const group = await this.groupsService.createGroup(
            createGroupRequestDto,
        );

        return {
            ...group,
            isUserMember: false,
        };
    }

    @Put(":id")
    @Roles(Role.Admin)
    @ApiNoContentResponse()
    async updateGroup(
        @Param("id") groupId: string,
        @Body() updateGroupRequestDto: UpdateGroupRequestDto,
        @Res() res: Response,
        @Req() req: Request,
    ): Promise<void> {
        await this.groupsService.updateGroup(
            Number(groupId),
            updateGroupRequestDto,
            req.user["userId"],
        );
        res.sendStatus(204);
    }

    @Delete(":id")
    @Roles(Role.Admin)
    @ApiNoContentResponse()
    async deleteGroup(
        @Param("id") groupId: string,
        @Res() res: Response,
    ): Promise<void> {
        await this.groupsService.deleteGroup(Number(groupId));
        res.sendStatus(204);
    }

    @Put(":id/users/me")
    @ApiNoContentResponse()
    async addUserToGroup(
        @Param("id") groupId: string,
        @Req() req: Request,
        @Res() res: Response,
    ): Promise<void> {
        const userId = req.user["userId"];
        await this.groupsService.addUserToGroups(userId, [Number(groupId)]);
        res.sendStatus(204);
    }

    @Patch("preferences")
    @ApiNoContentResponse()
    async patchPreferences(
        @Body() bulkPatchRequestDto: BulkPatchPreferenceRequestDto,
        @Req() req: Request,
        @Res() res: Response,
    ): Promise<void> {
        const userId = req.user["userId"];
        await this.groupsService.bulkPatch(bulkPatchRequestDto, userId);
        res.sendStatus(204);
    }

    @Delete(":id/users/me")
    @ApiNoContentResponse()
    async removeUserFromGroup(
        @Param("id") groupId: string,
        @Res() res: Response,
        @Req() req: Request,
    ): Promise<void> {
        const userId = req.user["userId"];
        await this.groupsService.removeUserFromGroups(userId, [
            Number(groupId),
        ]);
        res.sendStatus(204);
    }
}
