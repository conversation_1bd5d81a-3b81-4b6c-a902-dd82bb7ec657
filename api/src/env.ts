// https://www.jacobparis.com/content/type-safe-env
import { z, TypeOf } from "zod";

const zodEnv = z
    .object({
        NODE_ENV: z
            .enum(["development", "production", "test", "provision"])
            .default("development"),
        APP_BASE_URL: z.string().url(),
        PORT: z.coerce.number().min(1000).default(3000),
        DATABASE_URL: z.string(),
        DATABASE_URL_APP_USER: z.string(),
        ADMIN_USER: z.string(),
        ADMIN_PASS: z.string().min(8),
        JWT_SECRET: z.string().min(16),
        NOREPLY_EMAIL: z.string().email(),
        SES_ENDPOINT: z.string(),
        YOUTUBE_API_KEY: z.string().optional(),
        REDIS_HOST: z.string(),
        REDIS_PORT: z.coerce.number().min(1000),
        REDDIT_CLIENT_ID: z.string().optional(),
        REDDIT_CLIENT_SECRET: z.string().optional(),
        SALESFORCE_ENDPOINT: z.string().url().optional(),
        SALESFORCE_CLIENT_ID: z.string().optional(),
        SALESFORCE_CLIENT_SECRET: z.string().optional(),
        OPENAI_API_KEY: z.string().optional(),
        OPENAI_MODEL: z.string().optional(),
    })
    .refine(
        ({ DATABASE_URL, DATABASE_URL_APP_USER }) => {
            const databaseUrl = new URL(DATABASE_URL);
            const databaseUrlAppUser = new URL(DATABASE_URL_APP_USER);

            const databasesMatch =
                databaseUrl.hostname === databaseUrlAppUser.hostname &&
                databaseUrl.port === databaseUrlAppUser.port &&
                databaseUrl.pathname === databaseUrlAppUser.pathname;

            return databasesMatch;
        },
        {
            path: ["DATABASE_URL", "DATABASE_URL_APP_USER"],
            message:
                "DATABASE_URL and DATABASE_URL_APP_USER must have the same hostname, port, and pathname",
        },
    );

declare global {
    // eslint-disable-next-line @typescript-eslint/no-namespace
    namespace NodeJS {
        // eslint-disable-next-line @typescript-eslint/no-empty-object-type
        interface ProcessEnv extends TypeOf<typeof zodEnv> {}
    }
}
try {
    zodEnv.parse(process.env);
} catch (err) {
    if (err instanceof z.ZodError) {
        const { fieldErrors } = err.flatten();
        const errorMessage = Object.entries(fieldErrors)
            .map(([field, errors]) =>
                errors ? `${field}: ${errors.join(", ")}` : field,
            )
            .join("\n  ");
        throw new Error(`Bad environment variables:\n  ${errorMessage}`);
    }
}
