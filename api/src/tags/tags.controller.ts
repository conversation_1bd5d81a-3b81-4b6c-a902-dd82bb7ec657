import {
    Controller,
    Get,
    Post,
    Body,
    Query,
    Param,
    Delete,
    Res,
    Put,
    Req,
    ForbiddenException,
    Patch,
} from "@nestjs/common";
import {
    ApiCreatedResponse,
    ApiNoContentResponse,
    ApiOkResponse,
    ApiResponse,
    ApiTags,
} from "@nestjs/swagger";

import { Response, Request } from "express";

import { BulkPatchPreferenceRequestDto } from "../common/dto/preference.dto";
import { paginated } from "../generic/util";
import { Role } from "../roles/role.enum";
import { RolesService } from "../roles/roles.service";
import {
    CreateTagRequestDto,
    CreateTagResponseDto,
} from "./dto/create-tag.dto";
import { GetTagsRequestDto, GetTagsResponseDto } from "./dto/get-tags.dto";
import { UpdateTagRequestDto } from "./dto/update-tag.dto";
import { TagsService } from "./tags.service";

@Controller("tags")
@ApiTags("** topic management - tags (topic groups)")
export class TagsController {
    constructor(
        private readonly tagsService: TagsService,
        private readonly rolesService: RolesService,
    ) {}

    @Post()
    @ApiCreatedResponse({ type: CreateTagResponseDto })
    async create(
        @Body() createTagDto: CreateTagRequestDto,
        @Req() req: Request,
    ): Promise<CreateTagResponseDto> {
        const userId = req.user["userId"];

        if (
            !createTagDto.isPersonal &&
            !(await this.rolesService.hasUserRole(userId, Role.Admin))
        ) {
            throw new ForbiddenException();
        }

        return this.tagsService.create(createTagDto, userId);
    }

    @Get()
    @ApiResponse({ type: GetTagsResponseDto })
    async findAll(
        @Query() params: GetTagsRequestDto,
        @Req() req: Request,
    ): Promise<GetTagsResponseDto> {
        return paginated(
            (take, skip) =>
                this.tagsService.findAll({
                    take,
                    skip,
                    filterParams: params,
                    userId: req.user["userId"],
                }),
            params,
        );
    }

    @Put(":id")
    @ApiOkResponse()
    async update(
        @Param("id") id: string,
        @Body() updateTagRequestDto: UpdateTagRequestDto,
        @Res() res: Response,
        @Req() req: Request,
    ): Promise<void> {
        const tagId = Number(id);
        await this.tagsService.assertUserHasWritePermission(
            tagId,
            req.user["userId"],
        );

        this.tagsService.update(tagId, updateTagRequestDto);
        res.sendStatus(200);
    }

    @Patch("preferences")
    @ApiNoContentResponse()
    async patchPreferences(
        @Body() bulkPatchRequestDto: BulkPatchPreferenceRequestDto,
        @Req() req: Request,
        @Res() res: Response,
    ): Promise<void> {
        const userId = req.user["userId"];
        for (const item of bulkPatchRequestDto.items) {
            await this.tagsService.assertUserHasWritePermission(
                item.id,
                userId,
            );
        }
        await this.tagsService.bulkPatch(bulkPatchRequestDto, userId);
        res.sendStatus(204);
    }

    @Delete(":id")
    @ApiNoContentResponse()
    async delete(
        @Param("id") id: string,
        @Res() res: Response,
        @Req() req: Request,
    ): Promise<void> {
        const tagId = Number(id);
        await this.tagsService.assertUserHasWritePermission(
            tagId,
            req.user["userId"],
        );

        await this.tagsService.delete(tagId);
        res.sendStatus(204);
    }
}
