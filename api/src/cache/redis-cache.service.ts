import { Injectable, Logger } from "@nestjs/common";

import { Cache, CacheEntry } from "@epic-web/cachified";
import Redis, { Cluster } from "ioredis";

import { getRedis } from "../common/redis";
import { CacheServiceInterface } from "./cache.service-interface";

@Injectable()
export class RedisCacheService implements CacheServiceInterface {
    private redis: Redis | Cluster;
    private logger = new Logger("RedisCacheService");

    onModuleInit() {
        this.redis = getRedis();
    }

    onModuleDestroy() {
        this.redis.disconnect();
    }

    cache: Cache = {
        get: async (key: string) => {
            const cacheRecord = await this.redis.get(key);

            if (!cacheRecord) return;

            const { metadata, value } = JSON.parse(cacheRecord);

            this.logger.log(`Using cached value for "${key}"`);
            return { metadata, value };
        },

        set: async (key: string, { value, metadata }: CacheEntry) => {
            this.logger.log(`Caching "${key}"`);
            await this.redis.set(key, JSON.stringify({ metadata, value }));
        },

        delete: async (key: string) => {
            this.logger.log(`Removing "${key}" from cache`);
            await this.redis.del(key);
        },
    };

    async clearCache(pattern: string, limit: number = 100) {
        let count = 0;
        let cursor = "0";
        pattern = `*${pattern}*`;

        do {
            const [newCursor, keys] = await this.redis.scan(
                cursor,
                "MATCH",
                pattern,
                "COUNT",
                limit,
            );
            cursor = newCursor;

            for (const key of keys) {
                await this.redis.del(key);
            }
            count += keys.length;
        } while (cursor !== "0");

        return { count };
    }
}
