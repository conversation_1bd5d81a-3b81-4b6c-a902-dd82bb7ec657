import { Cache, CACHE_MANAGER } from "@nestjs/cache-manager";
import { Controller, Delete, Inject, Query } from "@nestjs/common";
import {
    ApiNoContentResponse,
    ApiOkResponse,
    ApiOperation,
    ApiTags,
} from "@nestjs/swagger";

import { Role } from "../roles/role.enum";
import { Roles } from "../roles/roles.decorator";
import { CacheService } from "./cache.service";
import {
    ClearCacheRequestDto,
    ClearCacheResponseDto,
} from "./dto/clear-cache.dto";
import { RedisCacheService } from "./redis-cache.service";

@Controller("cache")
@ApiTags("request cache")
export class CacheController {
    constructor(
        private readonly cacheService: CacheService,
        private readonly redisCacheService: RedisCacheService,
        @Inject(CACHE_MANAGER) private cacheManager: Cache,
    ) {}

    @Delete()
    @Roles(Role.Admin)
    @ApiOkResponse({ type: ClearCacheResponseDto })
    @ApiOperation({
        summary:
            "Delete from the request cache - used by background jobs to cache responses from 3rd party APIs",
    })
    async clearCache(
        @Query() query: ClearCacheRequestDto,
    ): Promise<ClearCacheResponseDto> {
        const result = await this.cacheService.clearCache(query.pattern);

        return {
            deletedCount: result.count,
        };
    }

    @Delete("redis")
    @Roles(Role.Admin)
    @ApiOkResponse({ type: ClearCacheResponseDto })
    @ApiOperation({
        summary:
            "Delete from the Redis cache - used to cache intermediate results in timeline endpoint",
    })
    async clearRedisCache(
        @Query() query: ClearCacheRequestDto,
    ): Promise<ClearCacheResponseDto> {
        const result = await this.redisCacheService.clearCache(query.pattern);

        return {
            deletedCount: result.count,
        };
    }

    @Delete("http-cache")
    @Roles(Role.Admin)
    @ApiNoContentResponse()
    @ApiOperation({
        summary:
            "Clear the in-memory controller level HTTP cache - caches analytics responses",
    })
    async clearHttpCache(): Promise<void> {
        await this.cacheManager.clear();
    }
}
