import { <PERSON>du<PERSON> } from "@nestjs/common";

import { PrismaModule } from "../prisma/prisma.module";
import { CacheController } from "./cache.controller";
import { CacheService } from "./cache.service";
import { RedisCacheService } from "./redis-cache.service";

@Module({
    imports: [PrismaModule],
    controllers: [CacheController],
    providers: [CacheService, RedisCacheService],
    exports: [CacheService, RedisCacheService],
})
export class CacheModule {}
