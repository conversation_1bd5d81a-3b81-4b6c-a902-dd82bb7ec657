import { BadRequestException, Injectable, Logger } from "@nestjs/common";

import { <PERSON><PERSON>, CacheEntry } from "@epic-web/cachified";

import { PrismaService } from "../prisma/prisma.service";
import { CacheServiceInterface } from "./cache.service-interface";

@Injectable()
export class CacheService implements CacheServiceInterface {
    private logger = new Logger("CacheService");

    constructor(private readonly prisma: PrismaService) {}

    cache: Cache = {
        get: async (key: string) => {
            const cacheRecord = await this.prisma.requestCache.findFirst({
                where: { key },
            });

            if (!cacheRecord) return;

            const result = {
                metadata: JSON.parse(cacheRecord.metadata),
                value: JSON.parse(cacheRecord.value),
            };

            this.logger.log(`Using cached value for "${key}"`);
            return result;
        },

        set: (key: string, { value, metadata }: CacheEntry) => {
            this.logger.log(`Caching "${key}"`);
            return this.prisma.requestCache.upsert({
                where: { key },
                update: {
                    value: JSON.stringify(value),
                    metadata: JSON.stringify(metadata),
                },
                create: {
                    key,
                    value: JSON.stringify(value),
                    metadata: JSON.stringify(metadata),
                },
            });
        },

        delete: (key: string) => {
            this.logger.log(`Removing "${key}" from cache`);
            return this.prisma.requestCache.delete({ where: { key } });
        },
    };

    async clearCache(pattern: string) {
        if (!pattern) throw new BadRequestException("Pattern is required");

        return this.prisma.requestCache.deleteMany({
            where: {
                key: {
                    contains: pattern,
                },
            },
        });
    }
}
