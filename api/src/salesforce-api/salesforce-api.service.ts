import { Injectable, Logger } from "@nestjs/common";

import { SalesforceSiteName } from "../salesforce/types";
import { SalesforceApiServiceInterface } from "./salesforce-api.service.interface";
import { SalesforceGetTopicsParams } from "./types";

@Injectable()
export class SalesforceApiService implements SalesforceApiServiceInterface {
    private logger = new Logger("SalesforceApiService");

    async getAccessToken() {
        const url = new URL(
            `${process.env.SALESFORCE_ENDPOINT}/services/oauth2/token`,
        );

        url.searchParams.set("client_id", process.env.SALESFORCE_CLIENT_ID);
        url.searchParams.set(
            "client_secret",
            process.env.SALESFORCE_CLIENT_SECRET,
        );
        url.searchParams.set("grant_type", "client_credentials");

        this.logger.log(`Fetching ${url.toString()}`);

        return fetch(url).then((response) => response.json());
    }

    async getForums(
        accessToken: string,
        site: SalesforceSiteName,
        type?: "Forum" | "Sub Forum",
    ): Promise<unknown> {
        const formattedSite = site.replace("_", " ");

        const url = new URL(
            `${process.env.SALESFORCE_ENDPOINT}/services/apexrest/api/getforumdetails`,
        );
        url.searchParams.set("site", formattedSite);
        if (type) url.searchParams.set("type", type);

        this.logger.log(`Fetching ${url.toString()}`);

        return fetch(url, {
            headers: {
                Authorization: `Bearer ${accessToken}`,
            },
        }).then((response) => response.json());
    }

    async getTopics(
        accessToken: string,
        params: SalesforceGetTopicsParams,
    ): Promise<unknown> {
        const formattedSite = params.site.replace("_", " ");

        const url = new URL(
            `${process.env.SALESFORCE_ENDPOINT}/services/apexrest/api/getForumTopics/`,
        );
        url.searchParams.set("site", formattedSite);
        url.searchParams.set("subforumname", params.forumName);
        url.searchParams.set("pageSize", params.pageSize.toString());
        if (params.lastId) url.searchParams.set("lastId", params.lastId);
        if (params.dateAfter)
            url.searchParams.set("dateafter", params.dateAfter);
        if (params.dateBefore)
            url.searchParams.set("datebefore", params.dateBefore);
        if (params.id) url.searchParams.set("Id", params.id);

        this.logger.log(url);

        return fetch(url, {
            headers: {
                Authorization: `Bearer ${accessToken}`,
            },
        }).then((response) => response.json());
    }

    async getPosts(
        accessToken: string,
        site: SalesforceSiteName,
        topicId: string,
    ): Promise<unknown> {
        const formattedSite = site.replace("_", " ");

        const url = new URL(
            `${process.env.SALESFORCE_ENDPOINT}/services/apexrest/api/getAllTopicPosts/`,
        );
        url.searchParams.set("site", formattedSite);
        url.searchParams.set("topicid", topicId);

        this.logger.log(url);

        return fetch(url, {
            headers: {
                Authorization: `Bearer ${accessToken}`,
            },
        }).then((response) => response.json());
    }
}
