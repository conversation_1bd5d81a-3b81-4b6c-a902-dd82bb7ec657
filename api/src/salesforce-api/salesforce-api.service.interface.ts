import { SalesforceSiteName } from "../salesforce/types";
import { SalesforceGetTopicsParams } from "./types";

export interface SalesforceApiServiceInterface {
    getAccessToken(): Promise<unknown>;
    getForums(accessToken: string, site: SalesforceSiteName): Promise<unknown>;
    getTopics(
        accessToken: string,
        params: SalesforceGetTopicsParams,
    ): Promise<unknown>;
    getPosts(
        accessToken: string,
        site: SalesforceSiteName,
        topicId: string,
    ): Promise<unknown>;
}
