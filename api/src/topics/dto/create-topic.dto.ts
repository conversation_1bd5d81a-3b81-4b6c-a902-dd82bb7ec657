import { Type } from "class-transformer";
import {
    IsArray,
    IsN<PERSON>ber,
    IsObject,
    IsOptional,
    IsString,
} from "class-validator";

import { GroupDto } from "../../groups/dto/get-groups.dto";
import { TagDto } from "../../tags/dto/get-tags.dto";
import { PreferenceDto } from "./preference.dto";

export class CreateTopicRequestDto {
    @IsString()
    name: string;

    @IsString()
    @IsOptional()
    description: string;

    @IsArray()
    @IsOptional()
    tags: number[] = [];

    @IsArray()
    @IsOptional()
    groups: string[] = [];

    @IsObject()
    @IsOptional()
    @Type(() => PreferenceDto)
    userTopicPreference: PreferenceDto;
}

export class CreateTopicResponseDto {
    @IsNumber()
    id: number;

    @IsString()
    name: string;

    @IsString()
    @IsOptional()
    description: string;

    @IsArray()
    @Type(() => TagDto)
    tags: TagDto[];

    @IsArray()
    @Type(() => GroupDto)
    groups: GroupDto[];

    @IsObject()
    @IsOptional()
    @Type(() => PreferenceDto)
    userTopicPreference: PreferenceDto;
}
