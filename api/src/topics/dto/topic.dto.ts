import { Type } from "class-transformer";
import { IsObject, IsOptional, IsString } from "class-validator";

import { PreferenceDto } from "./preference.dto";

class CreatedBy {
    id: string;
    firstName: string;
    lastName: string;
}

export class TopicDto {
    @IsString()
    name: string;

    @IsString()
    @IsOptional()
    description: string;

    @IsObject()
    createdBy: CreatedBy;

    @IsObject()
    @IsOptional()
    @Type(() => PreferenceDto)
    userTopicPreference: PreferenceDto;
}
