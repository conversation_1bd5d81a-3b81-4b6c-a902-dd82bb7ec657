import { ApiProperty } from "@nestjs/swagger";

import { NotificationCadence } from "@prisma/client";
import { IsBoolean, IsEnum, IsOptional } from "class-validator";

import {
    CreateTopicRequestDto,
    CreateTopicResponseDto,
} from "./create-topic.dto";

export class UpdateTopicRequestDto extends CreateTopicRequestDto {
    @IsBoolean()
    @IsOptional()
    negativeSentimentNotifications: boolean = false;

    @IsEnum(NotificationCadence)
    @IsOptional()
    @ApiProperty({ example: "WEEKLY | OFF" })
    notificationCadence: NotificationCadence = NotificationCadence.WEEKLY;
}
export class UpdateTopicResponseDto extends CreateTopicResponseDto {}
