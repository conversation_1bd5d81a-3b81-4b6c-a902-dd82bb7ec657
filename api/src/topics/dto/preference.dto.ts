import { ApiPropertyOptional } from "@nestjs/swagger";

import { NotificationCadence } from "@prisma/client";
import { IsBoolean, IsEnum, IsOptional } from "class-validator";

export class PreferenceDto {
    @IsBoolean()
    @IsOptional()
    isFavourite?: boolean;

    @IsEnum(NotificationCadence)
    @IsOptional()
    @ApiPropertyOptional({ example: "OFF" })
    notificationCadence?: NotificationCadence;

    @IsEnum(NotificationCadence)
    @IsOptional()
    @ApiPropertyOptional({ example: "OFF" })
    highRelevanceNotifications?: NotificationCadence;

    @IsEnum(NotificationCadence)
    @IsOptional()
    @ApiPropertyOptional({ example: "OFF" })
    negativeSentimentNotifications?: NotificationCadence;
}
