import { getReplyIDFromUrl, getThreadSlugFromUrl } from "./util";

describe("getThreadSlugFromUrl", () => {
    it("should return the thread slug from a url", () => {
        const actual = getThreadSlugFromUrl(
            "https://forum.allaboutcircuits.com/threads/arduino-ide-board-manager-memory.207478",
        );
        const expected = "arduino-ide-board-manager-memory.207478";

        expect(actual).toEqual(expected);
    });

    it("Handles trailing slash", () => {
        const actual = getThreadSlugFromUrl(
            "https://forum.allaboutcircuits.com/threads/arduino-ide-board-manager-memory.207478/",
        );
        const expected = "arduino-ide-board-manager-memory.207478";

        expect(actual).toEqual(expected);
    });
});

describe("getReplyIdFromUrl", () => {
    it("should return the reply id from a url", () => {
        const actual = getReplyIDFromUrl(
            "https://forum.allaboutcircuits.com/threads/arduino-ide-board-manager-memory.207478/#post-1993247",
        );
        const expected = "post-1993247";

        expect(actual).toEqual(expected);
    });
});
