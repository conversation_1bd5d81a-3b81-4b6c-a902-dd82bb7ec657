import { BullModule } from "@nestjs/bullmq";
import { <PERSON><PERSON><PERSON> } from "@nestjs/common";

import { BullMQAdapter } from "@bull-board/api/bullMQAdapter";
import { BullBoardModule } from "@bull-board/nestjs";

import { AllAboutCircuitsS3Module } from "../all-about-circuits-s3/all-about-circuits-s3.module";
import { AAC_FORUM_QUEUE, AI_ANALYSIS_QUEUE } from "../task/constants";
import { AllAboutCircuitsProcessor } from "./all-about-circuits.processor";
import { AllAboutCircuitsService } from "./all-about-circuits.service";

@Module({
    imports: [
        AllAboutCircuitsS3Module,
        BullBoardModule.forFeature({
            name: AAC_FORUM_QUEUE,
            adapter: BullMQAdapter,
        }),
        BullModule.registerQueue({ name: AAC_FORUM_QUEUE }),
        BullModule.registerQueue({ name: AI_ANALYSIS_QUEUE }),
    ],
    providers: [AllAboutCircuitsProcessor, AllAboutCircuitsService],
})
export class AllAboutCircuitsModule {}
