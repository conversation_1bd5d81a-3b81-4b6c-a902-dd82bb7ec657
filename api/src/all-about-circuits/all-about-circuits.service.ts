import { Injectable } from "@nestjs/common";

import { AllAboutCircuitsS3Service } from "../all-about-circuits-s3/all-about-circuits-s3.service";
import {
    ForumsLatestPostResponse,
    ForumsResponse,
    ThreadResponse,
    ThreadsMetadataResponse,
} from "./types";

@Injectable()
export class AllAboutCircuitsService {
    constructor(
        private readonly allAboutCircuitsS3Service: AllAboutCircuitsS3Service,
    ) {}

    async getForums(): Promise<ForumsResponse> {
        return this.allAboutCircuitsS3Service
            .getForums()
            .then(JSON.parse)
            .then(ForumsResponse.parse);
    }

    async *getRecentThreadUrls(
        forumSlug: string,
        dateFrom?: Date,
    ): AsyncGenerator<string> {
        const forums = await this.allAboutCircuitsS3Service
            .getForumsLatestPost()
            .then(JSON.parse)
            .then(ForumsLatestPostResponse.parse);

        for (const forumName in forums) {
            if (forumName !== forumSlug) {
                continue;
            }

            const updatedAt = forums[forumName];

            if (updatedAt < dateFrom) {
                continue;
            }

            const threadKey = `threads_metadata_${forumName}.json`;
            const threads = await this.allAboutCircuitsS3Service
                .getThreadsMetadata(threadKey)
                .then(JSON.parse)
                .then(ThreadsMetadataResponse.parse);

            for (const thread of threads) {
                if (thread.last_post < dateFrom) {
                    continue;
                }

                yield thread.thread_url;
            }
        }
    }

    async getThread(
        forumSlug: string,
        threadSlug: string,
    ): Promise<ThreadResponse> {
        const threadKey = `threads/${forumSlug}/${threadSlug}.json`;

        const thread = await this.allAboutCircuitsS3Service
            .getThread(threadKey)
            .then(JSON.parse)
            .then(ThreadResponse.parse);

        return thread;
    }
}
