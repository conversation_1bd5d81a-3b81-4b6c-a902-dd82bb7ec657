import { z } from "zod";

export const ForumsResponse = z.array(
    z.object({
        name: z.string(),
        slug: z.string(),
        url: z.string().url(),
    }),
);

export const ForumsLatestPostResponse = z.record(z.string(), z.coerce.date());

export const ThreadsMetadataResponse = z.array(
    z.object({
        thread_title: z.string(),
        thread_url: z.string().url(),
        author: z.string(),
        replies: z.number(),
        start_date: z.coerce.date(),
        last_post: z.coerce.date(),
    }),
);

export const ThreadResponse = z.object({
    thread_title: z.string(),
    thread_url: z.string().url(),
    post_count: z.number(),
    posts: z.array(
        z.object({
            index: z.number(),
            author: z.string(),
            timestamp: z.coerce.date(),
            content: z.string(),
            post_url: z.string().url(),
        }),
    ),
});

export type ForumsResponse = z.infer<typeof ForumsResponse>;
export type ForumsLatestPostResponse = z.infer<typeof ForumsLatestPostResponse>;
export type ThreadsMetadataResponse = z.infer<typeof ThreadsMetadataResponse>;
export type ThreadResponse = z.infer<typeof ThreadResponse>;
