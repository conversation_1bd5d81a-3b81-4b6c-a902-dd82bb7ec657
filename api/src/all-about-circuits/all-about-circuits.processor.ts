import { InjectQueue, Processor, WorkerHost } from "@nestjs/bullmq";
import { InternalServerErrorException } from "@nestjs/common";

import { UseTenantCls } from "../generic/bullmq/processor";
import { PrismaService } from "../prisma/prisma.service";
import { AAC_FORUM_QUEUE, AI_ANALYSIS_QUEUE } from "../task/constants";
import { AIAnalysisQueue, AllAboutCircuitsJob } from "../task/types";
import { AllAboutCircuitsService } from "./all-about-circuits.service";
import { getReplyIDFromUrl, getThreadSlugFromUrl } from "./util";

@Processor(AAC_FORUM_QUEUE)
export class AllAboutCircuitsProcessor extends WorkerHost {
    constructor(
        private readonly allAboutCircuitsService: AllAboutCircuitsService,
        private readonly prisma: PrismaService,
        @InjectQueue(AI_ANALYSIS_QUEUE)
        private aiAnalysisQueue: AIAnalysisQueue,
    ) {
        super();
    }

    @UseTenantCls()
    async process(job: AllAboutCircuitsJob) {
        const dateFrom = job.data.dateFrom
            ? new Date(job.data.dateFrom)
            : new Date(Date.now() - 24 * 60 * 60 * 1000); // 24 hours ago

        const forums = await this.allAboutCircuitsService.getForums();

        // Ensure all forums exist
        for (const forum of forums) {
            job.log(`Processing forum: ${forum.name}`);
            const { id: forumId } =
                await this.prisma.allAboutCircuitsForum.upsert({
                    where: {
                        slug: forum.slug,
                    },
                    update: {},
                    create: {
                        name: forum.name,
                        slug: forum.slug,
                        url: forum.url,
                        tenantId: job.data.tenantId,
                    },
                });

            job.log(`Fetching threads from ${dateFrom.toISOString()} to now`);
            const postGenerator =
                this.allAboutCircuitsService.getRecentThreadUrls(
                    forum.slug,
                    dateFrom,
                );

            for await (const postUrl of postGenerator) {
                // Fetch thread
                const threadSlug = getThreadSlugFromUrl(postUrl);
                job.log(`Processing thread: ${postUrl}`);

                const thread = await this.allAboutCircuitsService.getThread(
                    forum.slug,
                    threadSlug,
                );

                const [topLevelPost, ...replies] = thread.posts || [];
                if (!topLevelPost)
                    throw new InternalServerErrorException(
                        "No top level post found",
                    );

                // Upsert thread
                await this.prisma.allAboutCircuitsPost.upsert({
                    where: {
                        id: threadSlug,
                    },
                    update: {
                        replyCount: thread.post_count - 1,
                    },
                    create: {
                        id: threadSlug,
                        author: topLevelPost.author,
                        title: thread.thread_title,
                        text: topLevelPost.content,
                        publishedAt: new Date(topLevelPost.timestamp),
                        replyCount: thread.post_count - 1,
                        url: thread.thread_url,
                        forum: {
                            connect: {
                                id: forumId,
                            },
                        },
                    },
                });

                // Upsert replies
                for (const reply of replies) {
                    const replyId = getReplyIDFromUrl(reply.post_url);
                    await this.prisma.allAboutCircuitsPostReply.upsert({
                        where: {
                            id: replyId,
                        },
                        update: {},
                        create: {
                            id: replyId,
                            postId: threadSlug,
                            author: reply.author,
                            text: reply.content,
                            publishedAt: new Date(reply.timestamp),
                            url: reply.post_url,
                        },
                    });
                }

                // Add analysis job
                await this.aiAnalysisQueue.add(job.name, {
                    type: "allAboutCircuits",
                    forumName: "test",
                    postId: threadSlug,
                    postURL: "https://example.com",
                    tenantId: job.data.tenantId,
                });
            }
        }
    }
}
