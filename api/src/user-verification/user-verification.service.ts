import { Injectable, Logger, NotFoundException } from "@nestjs/common";

import { User, UserVerification } from "@prisma/client";
import { randomUUID } from "crypto";

import { EmailService } from "../email/email.service";
import { PrismaRootService } from "../prisma-root/prisma-root.service";

@Injectable()
export class UserVerificationService {
    private logger = new Logger("UserVerificationService");

    constructor(
        private prisma: PrismaRootService,
        private emailService: EmailService,
    ) {}

    async createPasswordResetVerification(userId: string) {
        const uuid = randomUUID();
        const code = `${uuid.slice(0, 4)}-${uuid.slice(4, 8)}`;

        const expiresAt = new Date(Date.now() + 10 * 60 * 1000);

        return this.prisma.userVerification.create({
            data: {
                userId,
                code,
                expiresAt,
            },
        });
    }

    async sendPasswordResetVerificationEmail(
        user: User,
        userVerification: UserVerification,
    ): Promise<void> {
        await this.emailService.sendEmail(
            user.email,
            "Truthkeep password reset",
            `Your password reset code is ${userVerification.code}. It will expire in ten minutes. `,
        );
    }

    async createUserOnboardingVerification(
        userId: string,
    ): Promise<UserVerification> {
        const code = randomUUID();
        const expiresAt = new Date(Date.now() + 14 * 24 * 60 * 60 * 1000); // 14 days from now

        const userVerification = await this.prisma.userVerification.create({
            data: {
                userId,
                code,
                expiresAt,
            },
        });

        return userVerification;
    }

    async sendUserOnboardingVerificationEmail(
        user: User,
        userVerification: UserVerification,
        origin: string,
    ): Promise<void> {
        const onboardingLink = `${origin}/onboarding?userId=${user.id}&code=${userVerification.code}`;

        await this.emailService.sendEmail(
            user.email,
            "Welcome to Truthkeep",
            `Follow the link to complete setting up your account: ${onboardingLink}\nIt will expire in 48 hours. `,
        );
    }

    async getUserVerification(userId: string, code: string) {
        const userVerification =
            await this.prisma.userVerification.findUniqueOrThrow({
                where: {
                    userId_code: {
                        userId,
                        code,
                    },
                },
            });

        if (userVerification.usedAt) {
            this.logger.log(
                `Attempt to access verification code which has already been consumed: ${code}`,
            );
            throw new NotFoundException();
        }

        if (userVerification.expiresAt.valueOf() < Date.now()) {
            this.logger.log(
                `Attempt to access expired verification code: ${code}`,
            );
            throw new NotFoundException();
        }

        return userVerification;
    }

    async consumeUserVerification(userId: string, code: string): Promise<void> {
        await this.prisma.userVerification.update({
            where: {
                userId_code: {
                    userId,
                    code,
                },
            },
            data: {
                usedAt: new Date(),
            },
        });
    }
}
