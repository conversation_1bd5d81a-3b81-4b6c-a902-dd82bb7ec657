import { Body, Controller, Get, Param, Post, Query, Req } from "@nestjs/common";
import { ApiCreatedResponse, ApiOkResponse, ApiTags } from "@nestjs/swagger";

import { Request } from "express";

import {
    PaginatedRequestDto,
    PaginatedResponseDto,
} from "../generic/dto/paginated.dto";
import { paginated } from "../generic/util";
import { Role } from "../roles/role.enum";
import { Roles } from "../roles/roles.decorator";
import { UserDto } from "../users/dto/user.dto";
import { CreateTenantDto } from "./dto/create-tenant.dto";
import { TenantDto } from "./dto/get-tenants.dto";
import { TenantsService } from "./tenants.service";

@Controller("tenants")
@ApiTags("tenants")
export class TenantsController {
    constructor(private readonly tenantsService: TenantsService) {}

    @Post()
    @Roles(Role.SuperAdmin)
    @ApiCreatedResponse({ type: TenantDto })
    async createTenant(
        @Body() createTenantDto: CreateTenantDto,
        @Req() req: Request,
    ): Promise<TenantDto> {
        const userId = req.user["userId"];
        return this.tenantsService.createTenant(createTenantDto, userId);
    }

    @Get()
    @Roles(Role.SuperAdmin)
    @ApiOkResponse()
    async getTenants(
        @Query() query: PaginatedRequestDto,
    ): Promise<PaginatedResponseDto<TenantDto>> {
        return paginated(
            (take, skip) => this.tenantsService.getTenants(take, skip),
            query,
        );
    }

    @Get(":id/users")
    @Roles(Role.SuperAdmin)
    @ApiOkResponse()
    async getTenantUsers(
        @Param("id") tenantId: string,
        @Query() query: PaginatedRequestDto,
    ): Promise<PaginatedResponseDto<Pick<UserDto, "id" | "email">>> {
        return paginated(
            async (take, skip) =>
                this.tenantsService.getTenantUsers(tenantId, take, skip),
            query,
        );
    }
}
