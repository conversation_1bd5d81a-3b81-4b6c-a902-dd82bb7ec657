import { Processor, WorkerHost } from "@nestjs/bullmq";

import { ClsService } from "nestjs-cls";

import { UseTenantCls } from "../generic/bullmq/processor";
import { PrismaService } from "../prisma/prisma.service";
import { REDDIT_POST_REFRESH_QUEUE } from "../task/constants";
import { RedditPostRefreshJob } from "../task/types";
import { RedditService } from "./reddit.service";
import { getCleanFlatComments } from "./util";

@Processor(REDDIT_POST_REFRESH_QUEUE, {
    // https://support.reddithelp.com/hc/en-us/articles/16160319875092-Reddit-Data-API-Wiki
    limiter: {
        max: 100,
        duration: 60 * 1000,
    },
})
export class RedditPostRefreshProcessor extends WorkerHost {
    constructor(
        private readonly prisma: PrismaService,
        private readonly redditService: RedditService,
        private readonly cls: ClsService,
    ) {
        super();
    }

    @UseTenantCls()
    async process(job: RedditPostRefreshJob) {
        const { postId, subredditName } = job.data;

        // Update votes and comment count
        const postInfo = await this.redditService.getPostInfo([postId]);
        const [
            {
                data: { score: voteScore, num_comments: commentCount },
            },
        ] = postInfo.data.children;

        await this.prisma.redditPost.update({
            where: {
                tenantId_id: {
                    id: postId,
                    tenantId: this.cls.get("tenantId"),
                },
            },
            data: {
                voteScore,
                commentCount,
            },
        });

        job.log(`${voteScore} upvotes, ${commentCount} comments`);

        job.log("Downloading Reddit comments");

        const rawComments = await this.redditService.fetchPostComments(
            subredditName,
            postId,
        );

        const comments = getCleanFlatComments(postId, rawComments);

        job.log(`Persisting ${comments.length} comments`);

        // TODO - bulk upsert
        for (const comment of comments) {
            await this.prisma.redditComment.upsert({
                where: {
                    tenantId_id: {
                        tenantId: this.cls.get("tenantId"),
                        id: comment.id,
                    },
                },
                create: comment,
                update: comment,
            });
        }

        // TODO - (re)analyze comments

        // TODO - reanalyze post text? they can be updated in between refreshes
    }
}
