import { InjectQueue, Processor, WorkerHost } from "@nestjs/bullmq";

import { UseTenantCls } from "../generic/bullmq/processor";
import {
    REDDIT_POST_REFRESH_QUEUE,
    REDDIT_REFRESH_QUEUE,
} from "../task/constants";
import { RedditRefreshJob } from "../task/types";
import { RedditPostRefreshQueue } from "./../task/types";
import { RedditRepositoryService } from "./reddit-repository.service";

@Processor(REDDIT_REFRESH_QUEUE)
export class RedditRefreshProcessor extends WorkerHost {
    constructor(
        private readonly redditRepositoryService: RedditRepositoryService,
        @InjectQueue(REDDIT_POST_REFRESH_QUEUE)
        private readonly redditPostRefreshQueue: RedditPostRefreshQueue,
    ) {
        super();
    }

    @UseTenantCls()
    async process(job: RedditRefreshJob) {
        let postCount = 0;

        const postsPendingUpdateGenerator =
            this.redditRepositoryService.getPostsPendingUpdate();
        for await (const post of postsPendingUpdateGenerator) {
            const refreshJob = {
                postId: post.id,
                postTitle: post.title,
                subredditName: post.subredditName,
                tenantId: job.data.tenantId,
            };
            await this.redditPostRefreshQueue.add(post.title, refreshJob);
            postCount += 1;
        }

        job.log(`Added ${postCount} Reddit post refresh jobs`);
    }
}
