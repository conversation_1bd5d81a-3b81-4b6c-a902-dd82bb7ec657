import { getCleanFlatComments, RawComments } from "./util";

describe("reddit getCleanFlatComments util", () => {
    it("flattens comments", () => {
        const actual = getCleanFlatComments("test-post-id", RAW_COMMENTS);
        expect(actual).toEqual(CLEAN_COMMENTS);
    });
});

const RAW_COMMENTS: RawComments = [
    {
        kind: "Listing",
        data: {
            children: [
                {
                    kind: "t3",
                    data: {
                        id: "test-post-id",
                        selftext:
                            "selftext f12befbd-7525-4a0d-9570-24afc36a687c",
                    },
                },
            ],
        },
    },
    {
        kind: "Listing",
        data: {
            children: [
                {
                    kind: "t1",
                    data: {
                        id: "A",
                        subreddit: "self",
                        body: "comment e4872486-a564-47c5-99b7-f3c194f9652a",
                        author: "jack",
                        score: -1,
                        created_utc: 1717987200,
                        reply_count: 2,
                        replies: {
                            kind: "Listing",
                            data: {
                                children: [
                                    {
                                        kind: "t1",
                                        data: {
                                            id: "B",
                                            kind: "t1",
                                            author: "jeff",
                                            author_flair_text: null,
                                            body: "comment reply 1391540a-335e-49ff-bc0d-140bd3b04e2b",
                                            score: 1,
                                            created_utc: 1717987200,
                                            reply_count: 0,
                                        },
                                    },
                                    {
                                        kind: "t1",
                                        data: {
                                            id: "C",
                                            kind: "t1",
                                            author: "jean",
                                            body: "comment reply 2a8b2254-86e6-4f17-b1cb-f94e336569c0",
                                            score: 123,
                                            created_utc: 1717987200,
                                            reply_count: 0,
                                            replies: {
                                                kind: "Listing",
                                                data: {
                                                    children: [
                                                        {
                                                            kind: "more",
                                                            data: {
                                                                id: "_",
                                                                children: [],
                                                            },
                                                        },
                                                    ],
                                                },
                                            },
                                        },
                                    },
                                ],
                            },
                        },
                    },
                },
            ],
        },
    },
];

const CLEAN_COMMENTS = [
    {
        postId: "test-post-id",
        id: "A",
        body: "comment e4872486-a564-47c5-99b7-f3c194f9652a",
        score: -1,
        publishedAt: new Date("2024-06-10T02:40:00.000Z"),
        replyCount: 2,
    },
    {
        postId: "test-post-id",
        id: "B",
        parentId: "A",
        body: "comment reply 1391540a-335e-49ff-bc0d-140bd3b04e2b",
        score: 1,
        publishedAt: new Date("2024-06-10T02:40:00.000Z"),
        replyCount: 0,
    },
    {
        postId: "test-post-id",
        id: "C",
        parentId: "A",
        body: "comment reply 2a8b2254-86e6-4f17-b1cb-f94e336569c0",
        score: 123,
        publishedAt: new Date("2024-06-10T02:40:00.000Z"),
        replyCount: 0,
    },
];
