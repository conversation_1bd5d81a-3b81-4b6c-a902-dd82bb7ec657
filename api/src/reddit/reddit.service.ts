import { Injectable } from "@nestjs/common";

import cachified from "@epic-web/cachified";

import { CacheService } from "../cache/cache.service";
import { cacheKeys } from "../cache/constants";
import { RedditApiService } from "../reddit-api/reddit-api.service";
import {
    CommaSeparatedRedditPostFullnames,
    RedditPostFullname,
} from "../reddit-api/types";
import { RawComments } from "./util";

type AuthenticationResponse = {
    access_token: string;
};

type PostsResponse = {
    data: {
        children: Post[];
        after?: RedditPostFullname;
    };
};

export type Post = {
    data: {
        id: string;
        title: string;
        selftext: string;
        created_utc: number;
        score: number;
        num_comments: number;
    };
};

// Access tokens expire after 86400 seconds after being issued, we refresh them twice as often to avoid race conditions
const ACCESS_TOKEN_TTL = (86400 * 1000) / 2;

// Top comments on a post are cached for one day
const COMMENTS_TTL = 24 * 60 * 60 * 1000;

const ONE_DAY = 1 * 24 * 60 * 60 * 1000;

@Injectable()
export class RedditService {
    constructor(
        private readonly cacheService: CacheService,
        private readonly redditApiService: RedditApiService,
    ) {}

    private async getAccessToken(): Promise<string> {
        const authenticationResponse: AuthenticationResponse = await cachified({
            key: cacheKeys.REDDIT_ACCESS_TOKEN,
            cache: this.cacheService.cache,
            ttl: ACCESS_TOKEN_TTL,
            checkValue: (value) => !!value["access_token"].match(/.+\..+/),
            getFreshValue: async () => {
                return this.redditApiService
                    .getAccessToken()
                    .then((response) => response.json());
            },
        });

        return authenticationResponse.access_token;
    }

    async *getPosts(
        subredditName: string,
        sort: "new" | "top",
        t?: "hour" | "day" | "week" | "month" | "year" | "all",
        limit?: number,
    ) {
        const accessToken = await this.getAccessToken();

        let after: RedditPostFullname | undefined;
        let count = 0;

        do {
            const newPostsResponse: PostsResponse = await this.redditApiService
                .getPosts(accessToken, subredditName, sort, after, t)
                .then((response) => response.json());

            for (const post of newPostsResponse.data.children) {
                count += 1;
                yield post;
            }

            after = newPostsResponse.data.after;
        } while (after && count < limit);
    }

    async fetchPostComments(
        subredditName: string,
        postId: string,
    ): Promise<RawComments> {
        const accessToken = await this.getAccessToken();

        const commentsResponse = await cachified({
            key: `reddit-comments-r/${subredditName}/${postId}`,
            cache: this.cacheService.cache,
            ttl: COMMENTS_TTL,
            checkValue: (value) => value && !value["error"],
            getFreshValue: async () => {
                const response = await this.redditApiService.getComments(
                    accessToken,
                    subredditName,
                    postId,
                );
                return response.json();
            },
        });

        return commentsResponse;
    }

    async getPostInfo(postIds: string[]): Promise<PostsResponse> {
        if (postIds.length > 100) {
            throw new Error(
                "getPostInfo cannot fetch more than 100 posts at a time",
            );
        }
        const accessToken = await this.getAccessToken();

        const idList = postIds
            .map((postId) => `t3_${postId}`)
            .join(",") as CommaSeparatedRedditPostFullnames;

        const postsResponse = await cachified({
            key: `reddit-posts-${postIds}`,
            cache: this.cacheService.cache,
            ttl: ONE_DAY,
            checkValue: (value) => value && !value["error"],
            getFreshValue: async () => {
                const response = await this.redditApiService.getPostInfo(
                    accessToken,
                    idList,
                );
                return response.json();
            },
        });

        return postsResponse;
    }
}
