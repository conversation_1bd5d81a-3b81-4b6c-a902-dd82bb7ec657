import { InjectQueue, Processor, WorkerHost } from "@nestjs/bullmq";

import { ClsService } from "nestjs-cls";

import { UseTenantCls } from "../generic/bullmq/processor";
import { PrismaService } from "../prisma/prisma.service";
import { AI_ANALYSIS_QUEUE, REDDIT_POST_QUEUE } from "../task/constants";
import { AIAnalysisQueue, RedditPostJob } from "../task/types";
import { RedditService } from "./reddit.service";
import { getCleanFlatComments } from "./util";

@Processor(REDDIT_POST_QUEUE, {
    // https://support.reddithelp.com/hc/en-us/articles/16160319875092-Reddit-Data-API-Wiki
    limiter: {
        max: 100,
        duration: 60 * 1000,
    },
})
export class RedditPostProcessor extends WorkerHost {
    constructor(
        private readonly redditService: RedditService,
        private readonly prisma: PrismaService,
        @InjectQueue(AI_ANALYSIS_QUEUE)
        private aiAnalysisQueue: AIAnalysisQueue,
        private readonly cls: ClsService,
    ) {
        super();
    }

    @UseTenantCls()
    async process(job: RedditPostJob) {
        const { postId, subredditName, postURL } = job.data;

        job.log(`Processing Reddit post: ${postId} from r/${subredditName}`);

        // Get post info from Reddit API
        const postInfo = await this.redditService.getPostInfo([postId]);
        const postData = postInfo.data.children[0].data;

        // Ensure subreddit exists
        await this.prisma.subreddit.upsert({
            where: {
                tenantId_name: {
                    tenantId: this.cls.get("tenantId"),
                    name: subredditName,
                },
            },
            create: { name: subredditName },
            update: {},
        });

        // Upsert post - create if doesn't exist, update if it does
        await this.prisma.redditPost.upsert({
            where: {
                tenantId_id: {
                    tenantId: this.cls.get("tenantId"),
                    id: postId,
                },
            },
            create: {
                id: postId,
                title: postData.title,
                text: postData.selftext,
                publishedAt: new Date(postData.created_utc * 1000),
                voteScore: postData.score,
                commentCount: postData.num_comments,
                subreddit: {
                    connect: {
                        tenantId_name: {
                            tenantId: this.cls.get("tenantId"),
                            name: subredditName,
                        },
                    },
                },
            },
            update: {
                voteScore: postData.score,
                commentCount: postData.num_comments,
            },
        });

        job.log(`${postData.score} upvotes, ${postData.num_comments} comments`);

        job.log("Downloading Reddit comments");

        const rawComments = await this.redditService.fetchPostComments(
            subredditName,
            postId,
        );

        const comments = getCleanFlatComments(postId, rawComments);

        job.log(`Persisting ${comments.length} comments`);

        // TODO - bulk upsert
        for (const comment of comments) {
            await this.prisma.redditComment.upsert({
                where: {
                    tenantId_id: {
                        tenantId: this.cls.get("tenantId"),
                        id: comment.id,
                    },
                },
                create: comment,
                update: comment,
            });
        }

        await this.aiAnalysisQueue.add(job.name, {
            type: "reddit",
            postId,
            subredditName,
            postURL,
            tenantId: job.data.tenantId,
        });
    }
}
