import { RedditComment } from "@prisma/client";

export type RawComments = [object, Listing];

type RawComment = {
    kind: "t1";
    data: {
        id: string;
        body: string;
        created_utc: number;
        author: string;
        replies?: Listing;
        score: number;
        reply_count: number;
        [key: string]: unknown;
    };
    [key: string]: unknown;
};

function isComment(comment): comment is RawComment {
    return comment?.kind === "t1";
}

type Listing = {
    data: {
        children: (RawComment | More)[];
        [key: string]: unknown;
    };
    [key: string]: unknown;
};

type More = {
    kind: "more";
    data: {
        id: "_";
        children: [];
    };
};

function _getCleanFlatComments(
    postId: string,
    rawComment: RawComment,
    parentId?: string,
): Omit<RedditComment, "analysisId" | "tenantId">[] {
    const { id, body, score, created_utc } = rawComment.data;
    const formattedComment = {
        postId,
        id,
        body,
        score,
        parentId,
        replyCount: Number(rawComment.data.reply_count ?? 0),
        publishedAt: new Date(created_utc * 1000),
    };

    const replies = rawComment.data.replies
        ? rawComment.data.replies.data.children
        : [];

    return [
        formattedComment,
        ...replies.flatMap((reply) => {
            if (isComment(reply))
                return _getCleanFlatComments(postId, reply, id);
            return [];
        }),
    ];
}

export function getCleanFlatComments(
    postId: string,
    rawComments: RawComments,
): Omit<RedditComment, "analysisId" | "tenantId">[] {
    const rawTopLevelComments = rawComments[1].data.children || [];

    return rawTopLevelComments.flatMap((comment) =>
        isComment(comment) ? _getCleanFlatComments(postId, comment) : [],
    );
}
