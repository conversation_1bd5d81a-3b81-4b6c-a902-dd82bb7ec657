import { BullModule } from "@nestjs/bullmq";
import { Mo<PERSON><PERSON> } from "@nestjs/common";

import { BullMQAdapter } from "@bull-board/api/bullMQAdapter";
import { BullBoardModule } from "@bull-board/nestjs";

import { CacheModule } from "../cache/cache.module";
import { RedditApiService } from "../reddit-api/reddit-api.service";
import {
    AI_ANALYSIS_QUEUE,
    REDDIT_POST_QUEUE,
    REDDIT_POST_REFRESH_QUEUE,
    REDDIT_REFRESH_QUEUE,
    REDDIT_SUBREDDIT_QUEUE,
} from "../task/constants";
import { RedditPostRefreshProcessor } from "./post-refresh.processor";
import { RedditPostProcessor } from "./post.processor";
import { RedditRepositoryService } from "./reddit-repository.service";
import { RedditService } from "./reddit.service";
import { RedditRefreshProcessor } from "./refresh.processor";
import { RedditSubredditProcessor } from "./subreddit.processor";

@Module({
    imports: [
        BullBoardModule.forFeature(
            {
                name: REDDIT_SUBREDDIT_QUEUE,
                adapter: BullMQAdapter,
            },
            {
                name: REDDIT_POST_QUEUE,
                adapter: BullMQAdapter,
            },
            {
                name: REDDIT_REFRESH_QUEUE,
                adapter: BullMQAdapter,
            },
            {
                name: REDDIT_POST_REFRESH_QUEUE,
                adapter: BullMQAdapter,
            },
        ),
        BullModule.registerQueue(
            { name: REDDIT_SUBREDDIT_QUEUE },
            { name: REDDIT_POST_QUEUE },
            { name: REDDIT_REFRESH_QUEUE },
            { name: REDDIT_POST_REFRESH_QUEUE },
            { name: AI_ANALYSIS_QUEUE },
        ),
        CacheModule,
    ],
    providers: [
        RedditApiService,
        RedditPostProcessor,
        RedditPostRefreshProcessor,
        RedditRefreshProcessor,
        RedditRepositoryService,
        RedditService,
        RedditSubredditProcessor,
    ],
    exports: [RedditService],
})
export class RedditModule {}
