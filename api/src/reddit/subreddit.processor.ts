import { InjectQueue, Processor, WorkerHost } from "@nestjs/bullmq";

import { UseTenantCls } from "../generic/bullmq/processor";
import { PrismaService } from "../prisma/prisma.service";
import { REDDIT_POST_QUEUE, REDDIT_SUBREDDIT_QUEUE } from "../task/constants";
import { RedditPostQueue, RedditSubredditJob } from "../task/types";
import { RedditService } from "./reddit.service";

@Processor(REDDIT_SUBREDDIT_QUEUE, {
    // https://support.reddithelp.com/hc/en-us/articles/16160319875092-Reddit-Data-API-Wiki
    limiter: {
        max: 100,
        duration: 60 * 1000,
    },
})
export class RedditSubredditProcessor extends WorkerHost {
    constructor(
        private readonly redditService: RedditService,
        private readonly prisma: PrismaService,
        @InjectQueue(REDDIT_POST_QUEUE)
        private redditPostQueue: RedditPostQueue,
    ) {
        super();
    }

    @UseTenantCls()
    async process(job: RedditSubredditJob) {
        const { subredditName, sort, t, limit } = job.data;

        const newPosts = this.redditService.getPosts(
            subredditName,
            sort,
            t,
            limit,
        );

        let newPostCount = 0;
        for await (const post of newPosts) {
            const { id, title, created_utc, selftext } = post.data;
            job.log(`${title}`);

            const existingPost = await this.prisma.redditPost.findUnique({
                where: {
                    tenantId_id: {
                        tenantId: job.data.tenantId,
                        id,
                    },
                },
            });
            if (existingPost) continue;

            // Persist post metadata and text
            await this.prisma.redditPost.create({
                data: {
                    id,
                    title,
                    publishedAt: new Date(created_utc * 1000),
                    text: selftext,
                    subreddit: {
                        connect: {
                            tenantId_name: {
                                tenantId: job.data.tenantId,
                                name: subredditName,
                            },
                        },
                    },
                },
            });

            // Add job to process post comments and do analysis
            await this.redditPostQueue.add(`"${title}"`, {
                postId: id,
                subredditName,
                postURL: `https://www.reddit.com/r/${subredditName}/comments/${id}`,
                tenantId: job.data.tenantId,
            });

            newPostCount += 1;
        }

        job.log(`Added ${newPostCount} Reddit post jobs`);
    }
}
