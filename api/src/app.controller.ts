import {
    Controller,
    Get,
    ServiceUnavailableException,
    Logger,
} from "@nestjs/common";
import { ApiResponse, ApiTags } from "@nestjs/swagger";

import { AppService } from "./app.service";
import { Public } from "./auth/constants";

@Controller()
@ApiTags("misc")
export class AppController {
    private logger = new Logger("AppController");
    constructor(private readonly appService: AppService) {}

    @Public()
    @Get("healthcheck")
    @ApiResponse({ type: String })
    async healthcheck(): Promise<string> {
        const timeoutMs = 5000;

        const withTimeout = async (
            fn: () => Promise<boolean>,
            label: string,
        ): Promise<string> => {
            try {
                const result = await Promise.race([
                    fn(),
                    new Promise<boolean>((_, reject) =>
                        setTimeout(
                            () => reject(new Error(`${label} check timed out`)),
                            timeoutMs,
                        ),
                    ),
                ]);
                return result ? "OK" : "FAILED";
            } catch (err) {
                this.logger.error(
                    `❌ ${label} health check error:`,
                    err.message,
                );
                return "ERROR";
            }
        };

        const [dbStatus, redisStatus] = await Promise.all([
            withTimeout(
                () => this.appService.isDatabaseConnectionHealthy(),
                "Database",
            ),
            withTimeout(
                () => this.appService.isRedisConnectionHealthy(),
                "Redis",
            ),
        ]);

        const statusReport = `Health Check Status:
      - Database: ${dbStatus}
      - Redis: ${redisStatus}
      `;

        if (dbStatus !== "OK" || redisStatus !== "OK") {
            throw new ServiceUnavailableException(statusReport);
        }
        return statusReport;
    }
}
