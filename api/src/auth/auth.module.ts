import { <PERSON>du<PERSON> } from "@nestjs/common";
import { APP_GUARD } from "@nestjs/core";
import { JwtModule } from "@nestjs/jwt";
import { PassportModule } from "@nestjs/passport";

import { EmailModule } from "../email/email.module";
import { RolesGuard } from "../roles/roles.guard";
import { RolesModule } from "../roles/roles.module";
import { UserVerificationModule } from "../user-verification/user-verification.module";
import { UsersModule } from "../users/users.module";
import { AuthController } from "./auth.controller";
import { AuthService } from "./auth.service";
import { jwtConstants } from "./constants";
import { JwtAuthGuard } from "./jwt-auth.guard";
import { JwtStrategy } from "./jwt.strategy";
import { LocalStrategy } from "./local.strategy";

@Module({
    imports: [
        EmailModule,
        JwtModule.register({
            secret: jwtConstants.secret,
            signOptions: { expiresIn: "60s" },
        }),
        JwtModule,
        PassportModule,
        RolesModule,
        UsersModule,
        UserVerificationModule,
    ],
    providers: [
        {
            provide: APP_GUARD,
            useClass: JwtAuthGuard,
        },
        {
            provide: APP_GUARD,
            useClass: RolesGuard,
        },
        AuthService,
        JwtStrategy,
        LocalStrategy,
    ],
    controllers: [AuthController],
    exports: [AuthService, JwtStrategy],
})
export class AuthModule {}
