import { Transform } from "class-transformer";
import { Is<PERSON><PERSON>, <PERSON>Optional, IsString } from "class-validator";

import { sanitizeEmailAddress } from "../../common/util/email";

export class PasswordResetDto {
    @IsEmail()
    @Transform(({ value }) => sanitizeEmailAddress(value))
    username: string;
}

export class VerifyAuthCodeDto {
    @IsString()
    username: string;

    @IsString()
    code: string;
}

export class PasswordResetResponseDto {
    @IsString()
    firstName: string;

    @IsString()
    @IsOptional()
    lastName: string;

    @IsEmail()
    @Transform(({ value }) => sanitizeEmailAddress(value))
    email: string;
}
