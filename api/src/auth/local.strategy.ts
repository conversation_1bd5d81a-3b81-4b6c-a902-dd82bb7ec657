import { Injectable, UnauthorizedException } from "@nestjs/common";
import { PassportStrategy } from "@nestjs/passport";

import { Strategy } from "passport-local";

import { sanitizeEmailAddress } from "../common/util/email";
import { AuthService } from "./auth.service";

@Injectable()
export class LocalStrategy extends PassportStrategy(Strategy) {
    constructor(private authService: AuthService) {
        super();
    }

    async validate(usernameOrEmail: string, password: string) {
        const user = await this.authService.validateUser(
            sanitizeEmailAddress(usernameOrEmail),
            password,
        );
        if (!user) {
            throw new UnauthorizedException();
        }
        return user;
    }
}
