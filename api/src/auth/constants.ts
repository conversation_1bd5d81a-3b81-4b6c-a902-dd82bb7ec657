import { SetMetadata } from "@nestjs/common";

import { CookieOptions } from "express";

export const jwtConstants = {
    secret: process.env["JWT_SECRET"],
};

export const IS_PUBLIC_KEY = "isPublic";
export const Public = () => SetMetadata(IS_PUBLIC_KEY, true);

export const ACCESS_TOKEN_COOKIE_OPTIONS: CookieOptions = {
    httpOnly: true,
    maxAge: 3600000,
    sameSite: "strict",
};

export const REFRESH_TOKEN_COOKIE_OPTIONS: CookieOptions = {
    httpOnly: true,
    sameSite: "strict",
};
