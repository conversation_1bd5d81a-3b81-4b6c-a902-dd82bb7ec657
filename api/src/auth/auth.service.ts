import { Injectable, Logger, UnauthorizedException } from "@nestjs/common";
import { JwtService } from "@nestjs/jwt";

import { compare } from "bcrypt";

import { PrismaRootService } from "../prisma-root/prisma-root.service";
import { UserVerificationService } from "../user-verification/user-verification.service";
import { UsersService } from "../users/users.service";

@Injectable()
export class AuthService {
    private logger = new Logger("AuthService");

    constructor(
        private userService: UsersService,
        private jwtService: JwtService,
        private prismaRootService: PrismaRootService,
        private userVerificationService: UserVerificationService,
    ) {}

    async validateUser(usernameOrEmail: string, pass: string) {
        const user = await this.userService.findOne(usernameOrEmail);
        if (user && (await compare(pass, user.passwordHash))) {
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            const { passwordHash, ...result } = user;
            return result;
        }
        return null;
    }

    private async loginToTenant(user, tenantId: string) {
        const accessToken = this.jwtService.sign(
            { sub: user.id, tenantId },
            { expiresIn: "15m" },
        );

        const session = await this.prismaRootService.userSession.create({
            data: {
                userId: user.id,
            },
        });

        const refreshToken = this.jwtService.sign(
            { sub: user.id, sessionId: session.id, tenantId },
            {
                expiresIn: "30d",
            },
        );

        return { accessToken, refreshToken };
    }

    async login(user, overrideTenantId?: string) {
        // By default, login to the first tenant the user belongs to
        const defaultTenantId = user?.tenants?.[0]?.id;
        if (!overrideTenantId) {
            return this.loginToTenant(user, defaultTenantId);
        }

        // Verify that the user can login to the override tenant
        const overrideTenant = await this.prismaRootService.tenant.findUnique({
            where: {
                id: overrideTenantId,
                users: {
                    some: {
                        id: user.id,
                    },
                },
            },
        });

        if (!overrideTenant) throw new UnauthorizedException();

        // Login to the override tenant
        return this.loginToTenant(user, overrideTenantId);
    }

    async refresh(refreshToken: string) {
        let decoded;
        try {
            decoded = this.jwtService.verify(refreshToken);
        } catch {
            throw new UnauthorizedException();
        }

        await this.prismaRootService.userSession
            .update({
                where: {
                    id: decoded.sessionId,
                },
                data: {
                    updatedAt: new Date(),
                },
            })
            .catch(() => {
                // Session not found
                throw new UnauthorizedException();
            });

        const payload = { sub: decoded.sub, tenantId: decoded.tenantId };
        return this.jwtService.sign(payload);
    }

    async logout(refreshToken: string) {
        const decoded = this.jwtService.verify(refreshToken);
        await this.prismaRootService.userSession.deleteMany({
            where: {
                id: decoded.sessionId,
            },
        });
    }

    async createPasswordResetVerification(username: string) {
        const user = await this.userService.findOne(username);

        if (!user) {
            this.logger.log(
                `Aborting password reset for "${username}" because user doesn't exist`,
            );
            return;
        }

        if (user.onboardingStep !== "COMPLETE") {
            this.logger.log(
                `Aborting password reset for "${username}" because user has not completed onboarding`,
            );
            return;
        }

        const verification =
            await this.userVerificationService.createPasswordResetVerification(
                user.id,
            );
        this.userVerificationService.sendPasswordResetVerificationEmail(
            user,
            verification,
        );
    }

    async consumeEmailVerification(username: string, code: string) {
        const user = await this.userService.findOne(username);
        await this.userVerificationService.consumeUserVerification(
            user.id,
            code,
        );
    }

    async findEmailVerification(email: string, code: string) {
        const user = await this.userService.findOne(email);
        return this.userVerificationService.getUserVerification(user.id, code);
    }
}
