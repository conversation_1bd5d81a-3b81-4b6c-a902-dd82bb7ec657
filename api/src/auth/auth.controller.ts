import {
    Body,
    Controller,
    Get,
    Logger,
    NotFoundException,
    Post,
    Put,
    Query,
    Req,
    Request,
    Res,
    UnauthorizedException,
    UseGuards,
} from "@nestjs/common";
import {
    ApiBody,
    ApiCreatedResponse,
    ApiOkResponse,
    ApiOperation,
    ApiResponse,
    ApiTags,
} from "@nestjs/swagger";

import { OnboardingStep } from "@prisma/client";
import { Request as ExpressRequest, Response } from "express";

import { UserVerificationService } from "../user-verification/user-verification.service";
import { SetPasswordRequestDto } from "../users/dto/onboarding.dto";
import { UsersService } from "../users/users.service";
import { AuthService } from "./auth.service";
import {
    ACCESS_TOKEN_COOKIE_OPTIONS,
    REFRESH_TOKEN_COOKIE_OPTIONS,
    Public,
} from "./constants";
import {
    PasswordResetDto,
    PasswordResetResponseDto,
    VerifyAuthCodeDto,
} from "./dto/forgot-password.dto";
import { SetSessionTenantRequestDto } from "./dto/session-tenant.dto";
import { UserLoginDto } from "./dto/user-login.dto";
import { LocalAuthGuard } from "./local-auth.guard";

@Controller()
@ApiTags("auth")
export class AuthController {
    private logger = new Logger("AuthController");

    constructor(
        private readonly authService: AuthService,
        private readonly userService: UsersService,
        private readonly userVerificationService: UserVerificationService,
    ) {}

    @Post("login")
    @ApiTags("* login")
    @Public()
    @UseGuards(LocalAuthGuard)
    @ApiOperation({
        description:
            "Logs in the user and sets an auth cookie. `username` may be a username or an email",
    })
    @ApiBody({ type: UserLoginDto })
    @ApiResponse({ status: 204, description: `Sets "auth" cookie with a JWT` })
    async login(@Request() req, @Res() res: Response): Promise<void> {
        const { accessToken, refreshToken } = await this.authService.login(
            req.user,
        );

        res.cookie("auth", accessToken, ACCESS_TOKEN_COOKIE_OPTIONS);
        res.cookie("refresh", refreshToken, REFRESH_TOKEN_COOKIE_OPTIONS);

        res.sendStatus(204);
    }

    @Post("refresh")
    @ApiTags("* login")
    @Public()
    @ApiOperation({ summary: `Refresh the auth cookie` })
    @ApiResponse({ status: 204 })
    async refresh(@Request() req, @Res() res: Response): Promise<void> {
        const refeshToken = req.cookies["refresh"];
        if (!refeshToken) throw new UnauthorizedException();

        const accessToken = await this.authService.refresh(refeshToken);

        res.cookie("auth", accessToken, ACCESS_TOKEN_COOKIE_OPTIONS);
        res.sendStatus(204);
    }

    @Put("session-tenant")
    @ApiResponse({ status: 204 })
    async setSessionTenant(
        @Request() req: ExpressRequest,
        @Body() body: SetSessionTenantRequestDto,
        @Res() res: Response,
    ): Promise<void> {
        const user = await this.userService.findById(req.user["userId"]);
        const { accessToken, refreshToken } = await this.authService.login(
            user,
            body.tenantId,
        );

        res.cookie("auth", accessToken, ACCESS_TOKEN_COOKIE_OPTIONS);
        res.cookie("refresh", refreshToken, REFRESH_TOKEN_COOKIE_OPTIONS);

        res.sendStatus(204);
    }

    @Post("forgot-password")
    @Public()
    @ApiOperation({ summary: `Request a "forgot password" email` })
    @ApiResponse({ status: 201 })
    async passwordReset(@Body() { username }: PasswordResetDto): Promise<void> {
        const user = await this.userService.findOne(username);
        if (!user) {
            this.logger.log(
                `Aborting password reset for "${username}" because user doesn't exist`,
            );
            return;
        }

        if (user.onboardingStep !== OnboardingStep.COMPLETE) {
            this.logger.log(
                `Sending onboarding email for "${username}" because user is not onboarded`,
            );
            const userVerification =
                await this.userVerificationService.createUserOnboardingVerification(
                    user.id,
                );
            await this.userVerificationService.sendUserOnboardingVerificationEmail(
                user,
                userVerification,
                process.env.APP_BASE_URL,
            );
            return;
        }

        this.logger.log(`Processing password reset for ${username}`);
        await this.authService.createPasswordResetVerification(username);
    }

    @Get("forgot-password")
    @Public()
    @ApiOperation({
        summary: `Verifies the link from the "forgot password" email is valid`,
    })
    @ApiOkResponse({ type: PasswordResetResponseDto })
    async verify(
        @Query() query: VerifyAuthCodeDto,
    ): Promise<PasswordResetResponseDto> {
        const user = await this.userService.findOne(query.username);
        if (!user) throw new NotFoundException();

        const userVerification = await this.authService.findEmailVerification(
            query.username,
            query.code,
        );
        if (!userVerification) throw new NotFoundException();

        const { firstName, lastName } = await this.userService.findOne(
            query.username,
        );

        return {
            firstName,
            lastName,
            email: user.email,
        };
    }

    @Post("set-password")
    @Public()
    @ApiCreatedResponse()
    async setPassword(
        @Body() setPasswordRequestDto: SetPasswordRequestDto,
    ): Promise<void> {
        const { email, code, password } = setPasswordRequestDto;

        const user = await this.userService.findOne(email);
        if (!user) throw new NotFoundException();

        const userVerification = await this.authService.findEmailVerification(
            email,
            code,
        );
        if (!userVerification) throw new NotFoundException();

        await this.userService.setPassword(user.id, password);
        await this.authService.consumeEmailVerification(email, code);
    }

    @Post("logout")
    @ApiResponse({ status: 204 })
    async logout(@Req() req, @Res() res: Response): Promise<void> {
        const refreshToken = req.cookies["refresh"];

        this.authService.logout(refreshToken);

        res.clearCookie("auth");
        res.clearCookie("refresh");

        res.sendStatus(204);
    }
}
