import { Injectable, Logger } from "@nestjs/common";

import { youtube_v3 } from "@googleapis/youtube";

@Injectable()
export class YoutubeApiService {
    private logger = new Logger("YoutubeApiService");
    private youtube: youtube_v3.Youtube;

    constructor() {
        this.youtube = new youtube_v3.Youtube({
            auth: process.env.YOUTUBE_API_KEY,
        });
    }

    async searchChannels(chanelHandle: string) {
        this.logger.log(`Searching for channel with handle "${chanelHandle}"`);
        return this.youtube.channels.list({
            part: ["snippet", "contentDetails"],
            forHandle: chanelHandle,
        });
    }

    async searchVideos(uploadsPlaylistId: string, maxResults = 50) {
        this.logger.log(
            `Searching for videos from playlist with id "${uploadsPlaylistId}"`,
        );
        return this.youtube.playlistItems.list({
            playlistId: uploadsPlaylistId,
            maxResults,
            part: ["snippet"],
        });
    }

    async getVideoListingWithStatistics(videoId: string) {
        this.logger.log(
            `Fetching video statistics for video with id "${videoId}"`,
        );
        return this.youtube.videos.list({
            part: ["statistics"],
            id: [videoId],
        });
    }

    async getCommentThreads(videoId: string) {
        this.logger.log(
            `Fetching comment threads for video with id "${videoId}"`,
        );
        return this.youtube.commentThreads.list({
            part: ["snippet", "replies"],
            videoId,
        });
    }
}
