import { NarrativeAspect } from "@prisma/client";

export class TopicDto {
    id: number;
    name: string;
}

export class NarrativeDto {
    id: number;
    summary: string;
    topic: TopicDto;
    aspect: NarrativeAspect;
}

export class NarrativeAspectDto {
    value: string;
    label: string;
}

export class NarrativeBreakdownDto {
    id: number;
    longSummary?: string;
    tipsAndActions?: string;
}
