import { BadGatewayException, Injectable, Logger } from "@nestjs/common";

import cachified from "@epic-web/cachified";
import { NarrativeAspect } from "@prisma/client";
import { ClsService } from "nestjs-cls";
import { zodResponseFormat } from "openai/helpers/zod";
import { z } from "zod";

import { CacheService } from "../cache/cache.service";
import { DateFilterParams } from "../generic/dto/date-filtered.dto";
import { TimelineItemDto } from "../insights/dto/timeline.dto";
import { InsightsService } from "../insights/insights.service";
import { OpenaiService } from "../openai/openai.service";
import { PrismaService } from "../prisma/prisma.service";
import {
    NarrativeAspectDto,
    NarrativeBreakdownDto,
    NarrativeDto,
} from "./dto/narrative.dto";
import { formatEnum } from "./util";

const PARAPHRASE_SCHEMA = z.object({
    paraphrased: z.string().describe("The input repeated concisely"),
});

@Injectable()
export class NarrativesService {
    logger = new Logger("NarrativesService");

    constructor(
        private readonly prisma: PrismaService,
        private readonly openaiService: OpenaiService,
        private readonly insightsService: InsightsService,
        private readonly cacheService: CacheService,
        private readonly cls: ClsService,
    ) {}

    async getCandidateNarratives(): Promise<
        { topicName: string; topicId: number; aspect: NarrativeAspect }[]
    > {
        return this.prisma.$queryRawWithTenant`
            WITH
                "Aspect" AS (
                    SELECT UNNEST(enum_range(NULL::"NarrativeAspect")) AS aspect
                ),
                "TopicAspectPair" AS (
                    SELECT "Topic".name AS "topicName", "Topic".id AS "topicId", "Aspect".aspect
                    FROM "Topic"
                        CROSS JOIN "Aspect"
                )

            SELECT "topicName", "TopicAspectPair"."topicId", "TopicAspectPair"."aspect"
            FROM "TopicAspectPair"
                LEFT JOIN "Narrative"
                    ON "Narrative"."topicId" = "TopicAspectPair"."topicId"
                        AND "Narrative".aspect = "TopicAspectPair".aspect
            WHERE
                "Narrative".id IS NULL
                AND "TopicAspectPair".aspect != 'UNKNOWN';
          `;
    }

    async getNarrativeSummary(
        topicName: string,
        aspect: NarrativeAspect,
    ): Promise<string> {
        const formattedAspect = aspect.replace(/_/g, " ").toLowerCase();
        const narrative = await this.openaiService.getCompletion({
            messages: [
                {
                    role: "system",
                    content: [
                        "You concisely repeat the narrative you are given as a single sentence or phrase.",
                        "Narratives must be neutral in sentiment.",
                        "Narratives are expressed from the perspective of a consumer.",
                        `E.g. "Ease of using Raspberry Pi 3"`,
                        `E.g. "Perceived value of dsPIC30F"`,
                        `E.g. Texas Instruments documentation quality`,
                    ].join("\n"),
                },
                {
                    role: "user",
                    content: `topic: "${topicName}" aspect: "${formattedAspect}"`,
                },
            ],
            model: process.env.OPENAI_MODEL,
            response_format: zodResponseFormat(PARAPHRASE_SCHEMA, "paraphrase"),
        });

        const response = JSON.parse(
            narrative.choices[0].message.content,
        ) as z.infer<typeof PARAPHRASE_SCHEMA>;

        return response.paraphrased;
    }

    async persistNarrative(
        topicId: number,
        aspect: NarrativeAspect,
        summary: string,
    ) {
        await this.prisma.narrative.create({
            data: {
                topic: { connect: { id: topicId } },
                aspect,
                summary,
            },
        });
    }

    async getNarratives(
        take: number,
        skip: number,
    ): Promise<{ items: NarrativeDto[]; total: number }> {
        const items = await this.prisma.narrative.findMany({
            select: {
                id: true,
                summary: true,
                aspect: true,
                topic: {
                    select: {
                        id: true,
                        name: true,
                    },
                },
            },
            take,
            skip,
        });

        const total = await this.prisma.narrative.count();

        return { items, total };
    }

    async getNarrative(id: number): Promise<NarrativeDto> {
        return this.prisma.narrative.findUniqueOrThrow({
            where: { id },
            select: {
                id: true,
                summary: true,
                aspect: true,
                topic: { select: { id: true, name: true } },
            },
        });
    }

    async getNarrativeAspects(
        take: number,
        skip: number,
    ): Promise<{ items: NarrativeAspectDto[]; total: number }> {
        const items = Object.values(NarrativeAspect)
            .slice(skip, skip + take)
            .map((aspect) => ({
                value: aspect,
                label: formatEnum(aspect),
            }));

        const total = Object.values(NarrativeAspect).length;

        return { items, total };
    }

    async getNarrativeBreakdown(
        id: number,
        params: DateFilterParams,
        userId: string,
    ): Promise<NarrativeBreakdownDto> {
        const narrative = await this.prisma.narrative.findUnique({
            where: { id },
            select: { summary: true },
        });

        // Get representative posts
        const timelineItems = await this.insightsService.getTimeline({
            narrativeIds: [id],
            take: 10,
            skip: 0,
            userId,
            ...params,
        });

        if (timelineItems.items.length === 0) return { id };

        // Get OpenAI summary
        const cacheKey = [
            "narrative-summary",
            id,
            params.dateFrom,
            params.dateTo,
        ]
            .filter(Boolean)
            .join("-");

        const breakdown = await cachified({
            key: cacheKey,
            cache: this.cacheService.cache,
            getFreshValue: async () =>
                this.getNarrativeBreakdownCompletion(
                    narrative.summary,
                    timelineItems.items,
                ),
            ttl: 1000 * 60 * 60 * 24,
        });

        return {
            id,
            ...breakdown,
        };
    }

    private async getNarrativeBreakdownCompletion(
        narrativeSummary: string,
        timelineItems: TimelineItemDto[],
    ): Promise<Omit<NarrativeBreakdownDto, "id">> {
        const content = timelineItems
            .map(({ title, summary }) => `- ${summary}: "${title}"`)
            .join("\n");

        const schema = z.object({
            longSummary: z
                .string()
                .describe(
                    "Summary of the narrative present in the posts. Respond with up to 4 complete sentences",
                ),
            tipsAndActions: z
                .string()
                .describe(
                    "How should Microchip Technology Incorporated address consumers based on the posts. Only respond with markdown bullet points. Include no more than 5 bullet points.",
                ),
        });

        const result = await this.openaiService.getCompletion({
            model: process.env.OPENAI_MODEL,
            response_format: zodResponseFormat(schema, "narrative"),
            messages: [
                {
                    role: "system",
                    content: `You analyze narratives in social media posts. The following posts have already been identified as being about the same narrative: "${narrativeSummary}"`,
                },
                { role: "user", content },
            ],
        });

        if (!result)
            throw new BadGatewayException(
                "Failed to get a resonse from OpenAI",
            );

        if (result.choices.length === 0)
            throw new BadGatewayException("OpenAI returned no choices");

        const completion = result.choices[0];
        let completionContent;
        try {
            completionContent = schema.safeParse(
                JSON.parse(completion.message.content),
            );
        } catch {
            throw new BadGatewayException("Failed to parse completion content");
        }

        if (!completionContent.success) {
            throw new BadGatewayException("Failed to parse completion content");
        }

        return completionContent.data;
    }
}
