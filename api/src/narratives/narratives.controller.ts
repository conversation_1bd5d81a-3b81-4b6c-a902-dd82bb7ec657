import { Controller, Get, Param, Query, Req } from "@nestjs/common";
import { ApiOkResponse, ApiTags } from "@nestjs/swagger";

import { Request } from "express";

import { DateFilteredRequestDto } from "../generic/dto/date-filtered.dto";
import { PaginatedRequestDto } from "../generic/dto/paginated.dto";
import { paginated } from "../generic/util";
import { PaginatedResponseDto } from "./../generic/dto/paginated.dto";
import {
    NarrativeAspectDto,
    NarrativeBreakdownDto,
    NarrativeDto,
} from "./dto/narrative.dto";
import { NarrativesService } from "./narratives.service";

@Controller("narratives")
@ApiTags("*** insights - narratives")
export class NarrativesController {
    constructor(private readonly narrativesService: NarrativesService) {}

    @Get()
    @ApiOkResponse({ type: [NarrativeDto] })
    async getNarratives(
        @Query() params: PaginatedRequestDto,
    ): Promise<PaginatedResponseDto<NarrativeDto>> {
        return paginated(
            (...args) => this.narrativesService.getNarratives(...args),
            params,
        );
    }

    @Get("aspects")
    @ApiOkResponse({ type: PaginatedResponseDto<NarrativeAspectDto> })
    async getNarrativeAspects(
        @Query() params: PaginatedRequestDto,
    ): Promise<PaginatedResponseDto<NarrativeAspectDto>> {
        return paginated(
            (...args) => this.narrativesService.getNarrativeAspects(...args),
            params,
        );
    }

    @Get(":id")
    @ApiOkResponse({ type: NarrativeDto })
    async getNarrative(@Param("id") id: number): Promise<NarrativeDto> {
        return this.narrativesService.getNarrative(Number(id));
    }

    @Get(":id/breakdown")
    @ApiOkResponse({ type: NarrativeBreakdownDto })
    async getNarrativeBreakdown(
        @Param("id") id: number,
        @Query() params: DateFilteredRequestDto,
        @Req() req: Request,
    ): Promise<NarrativeBreakdownDto> {
        return this.narrativesService.getNarrativeBreakdown(
            Number(id),
            params,
            req.user["userId"],
        );
    }
}
