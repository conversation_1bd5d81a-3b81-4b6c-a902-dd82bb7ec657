import { BullModule } from "@nestjs/bullmq";
import { <PERSON><PERSON><PERSON> } from "@nestjs/common";

import { BullMQAdapter } from "@bull-board/api/bullMQAdapter";
import { BullBoardModule } from "@bull-board/nestjs";

import { CacheModule } from "../cache/cache.module";
import { InsightsModule } from "../insights/insights.module";
import { OpenaiModule } from "../openai/openai.module";
import { NARRATIVE_GENERATION_QUEUE } from "../task/constants";
import { NarrativesController } from "./narratives.controller";
import { NarrativeGenerationProcessor } from "./narratives.processor";
import { NarrativesService } from "./narratives.service";

@Module({
    imports: [
        BullBoardModule.forFeature({
            name: NARRATIVE_GENERATION_QUEUE,
            adapter: BullMQAdapter,
        }),
        BullModule.registerQueue({
            name: NARRATIVE_GENERATION_QUEUE,
        }),
        CacheModule,
        InsightsModule,
        OpenaiModule,
    ],
    providers: [NarrativeGenerationProcessor, NarrativesService],
    controllers: [NarrativesController],
})
export class NarrativesModule {}
