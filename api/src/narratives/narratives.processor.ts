import { Processor, WorkerHost } from "@nestjs/bullmq";

import { UseTenantCls } from "../generic/bullmq/processor";
import { NARRATIVE_GENERATION_QUEUE } from "../task/constants";
import { NarrativeGenerationJob } from "../task/types";
import { NarrativesService } from "./narratives.service";

@Processor(NARRATIVE_GENERATION_QUEUE)
export class NarrativeGenerationProcessor extends WorkerHost {
    constructor(private readonly narrativesService: NarrativesService) {
        super();
    }

    @UseTenantCls()
    async process(job: NarrativeGenerationJob) {
        const candidateNarratives =
            await this.narrativesService.getCandidateNarratives();

        job.log(
            `Processing ${candidateNarratives.length} candidate narratives`,
        );

        for (const candidateNarrative of candidateNarratives) {
            const { topicName, aspect, topicId } = candidateNarrative;
            job.log(`Processing "${topicName}", "${aspect}"`);

            const summary = await this.narrativesService
                .getNarrativeSummary(topicName, aspect)
                // OpenAI likes to add a period to the end of the summary
                .then((summary) => summary.replace(/\.$/, ""))
                .catch((error) => {
                    job.log(
                        `Error processing "${topicName}", "${aspect}": ${error.message}`,
                    );
                    return null;
                });

            if (!summary) {
                job.log("Skipping narrative");
                continue;
            }
            await this.narrativesService.persistNarrative(
                topicId,
                aspect,
                summary,
            );
            job.log(`Persisted "${topicName}", "${aspect}", "${summary}"`);
        }
    }
}
