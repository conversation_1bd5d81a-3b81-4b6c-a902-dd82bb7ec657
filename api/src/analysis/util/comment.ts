import { RedditComment, YoutubeComment } from "@prisma/client";

export type GenericComment = Partial<RedditComment> | Partial<YoutubeComment>;

export function cleanComment(comment: GenericComment) {
    return {
        id: comment.id,
        body: comment.body,
        score: comment.score,
    };
}

export function commentsToMarkdown(comments: GenericComment[]) {
    const tree = buildCommentTree(comments);

    function _commentsToMarkdown(
        node: GenericComment & { replies?: GenericComment[] },
    ): string {
        const bodyMarkdown = `* ${node.body}`;

        const replies = node.replies ?? [];
        const repliesMarkdown = replies.map(
            (reply) => `    ${_commentsToMarkdown(reply)}`,
        );

        const completeMarkdown = [bodyMarkdown, ...repliesMarkdown].join(
            "\n\n",
        );
        return completeMarkdown;
    }

    return tree.map((node) => _commentsToMarkdown(node)).join("\n\n");
}

export function buildCommentTree<T extends GenericComment>(
    comments: T[],
): (T & { replies?: T[] })[] {
    const commentMap = new Map<string | number, T & { replies?: T[] }>();
    const roots: (T & { replies?: T[] })[] = [];

    for (const comment of comments) {
        commentMap.set(comment.id, { ...comment });
    }

    for (const comment of commentMap.values()) {
        if (comment.parentId) {
            const parent = commentMap.get(comment.parentId);
            if (parent) {
                parent.replies = parent.replies || [];
                parent.replies.push(comment);
            }
        } else {
            roots.push(comment);
        }
    }

    roots.sort((a, b) => b.score - a.score);
    return roots;
}
