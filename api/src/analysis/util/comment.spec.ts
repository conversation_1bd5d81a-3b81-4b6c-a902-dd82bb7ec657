import { RedditComment, YoutubeComment } from "@prisma/client";

import {
    buildCommentTree,
    cleanComment,
    commentsToMarkdown,
    GenericComment,
} from "./comment";

describe("cleanComment", () => {
    it("removes fields from a reddit comment", () => {
        const redditComment: Partial<RedditComment> = {
            postId: "test-post-id",
            id: "test-comment-id",
            body: "test",
            parentId: "test-parent-id",
            score: 10,
            replyCount: 2,
            publishedAt: new Date("2024-06-10T02:40:00.000Z"),
        };

        expect(cleanComment(redditComment)).toEqual({
            id: "test-comment-id",
            body: "test",
            score: 10,
        });
    });

    it("removes fields from a youtube comment", () => {
        const redditComment: Partial<YoutubeComment> = {
            videoId: "test-video-id",
            id: "test-comment-id",
            body: "test",
            parentId: "test-parent-id",
            score: 10,
            replyCount: 2,
            publishedAt: new Date("2024-06-10T02:40:00.000Z"),
        };

        expect(cleanComment(redditComment)).toEqual({
            id: "test-comment-id",
            body: "test",
            score: 10,
        });
    });
});

describe("buildCommentTree", () => {
    const FLAT_COMMENTS = [
        {
            id: "1",
            body: "Comment 1",
            score: 1,
        },
        {
            id: "2",
            body: "Comment 2",
            score: 2,
            parentId: "1",
        },
        {
            id: "3",
            body: "Comment 3",
            score: 3,
            parentId: "1",
        },
    ];

    it("should build a comment tree", () => {
        const actual = buildCommentTree(FLAT_COMMENTS);

        expect(actual).toMatchObject([
            {
                id: "1",
                body: "Comment 1",
                score: 1,
                replies: [
                    {
                        id: "2",
                        body: "Comment 2",
                        score: 2,
                    },
                    {
                        id: "3",
                        body: "Comment 3",
                        score: 3,
                    },
                ],
            },
        ]);
    });
});

describe("commentsToMarkdown", () => {
    it("formats a flat list of comments", () => {
        const comment: GenericComment = {
            id: "test-comment-id",
            body: "test",
            score: 10,
        };

        expect(commentsToMarkdown([comment])).toEqual("* test");
    });

    it("formats nested comments", () => {
        const comments: GenericComment[] = [
            {
                id: "1",
                body: "test1",
            },
            {
                id: "2",
                body: "test2",
            },
            {
                id: "3",
                body: "test3",
                parentId: "1",
            },
        ];

        expect(commentsToMarkdown(comments)).toEqual(
            "* test1\n\n    * test3\n\n* test2",
        );
    });
});
