import { Injectable } from "@nestjs/common";

import { RedditPost } from "@prisma/client";
import { YoutubeVideo } from "@prisma/client";
import { WordTokenizer, PorterStemmer, stopwords } from "natural";

import { OpenaiService } from "../openai/openai.service";

@Injectable()
export class KeywordService {
    constructor(private readonly openaiService: OpenaiService) {}

    async getRedditPostSearchVector(summary: string, post: RedditPost) {
        return this.getSearchVector(
            `${summary}\n\n# ${post.title}\n${post.text}`,
        );
    }

    async getYoutubeVideoSearchVector(summary: string, video: YoutubeVideo) {
        return this.getSearchVector(
            `${summary}\n\n# ${video.title}\n${video.subtitles}`,
        );
    }

    async getRedditCommentThreadSearchVector(
        summary: string,
        conversation: string,
    ) {
        return this.getSearchVector(`${summary}\n\n${conversation}`);
    }

    async getYoutubeCommentThreadSearchVector(
        summary: string,
        conversation: string,
    ) {
        return this.getSearchVector(`${summary}\n\n${conversation}`);
    }

    private async getSearchVector(input: string): Promise<string> {
        const tokenizer = new WordTokenizer();
        const stemmer = PorterStemmer;

        // Convert to lowercase and remove extra whitespace
        let text = input.toLowerCase().trim();

        // Remove punctuation and special characters, but keep apostrophes for contractions
        text = text.replace(/[^\w\s']/g, " ");

        // Tokenize using Natural's WordTokenizer
        const tokens = tokenizer.tokenize(text) || [];

        // Process tokens and build tsvector
        const tsvectorParts: string[] = [];

        for (const token of tokens) {
            // Skip if token is too short or is a number
            if (token.length < 2 || /^\d+$/.test(token)) {
                continue;
            }

            // Skip stopwords using Natural's stopwords
            if (stopwords.includes(token.toLowerCase())) {
                continue;
            }

            // Apply stemming using Natural's Porter Stemmer
            const stemmed = stemmer.stem(token);

            // Add to tsvector parts if not empty
            if (stemmed && stemmed.length >= 2) {
                // Escape single quotes and backslashes for PostgreSQL
                const escapedToken = stemmed
                    .replace(/'/g, "''")
                    .replace(/\\/g, "\\\\");
                tsvectorParts.push(`'${escapedToken}'`);
            }
        }

        // Join all parts with spaces
        const tsvectorString = tsvectorParts.join(" ");

        return tsvectorString;
    }
}
