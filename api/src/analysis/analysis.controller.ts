import { Body, Controller, Post } from "@nestjs/common";
import { ApiCreatedResponse, ApiTags } from "@nestjs/swagger";

import { AnalysisService } from "./analysis.service";
import {
    FullAnalysisDto,
    RedditPostAnalysisRequestDto,
    RedditPostAnalysisResponseDto,
} from "./dto/reddit-post.dto";

@Controller("analysis")
@ApiTags("analysis")
export class AnalysisController {
    constructor(private readonly analysisService: AnalysisService) {}

    @Post()
    @ApiCreatedResponse({
        type: RedditPostAnalysisResponseDto,
    })
    async analyzeRedditPost(
        @Body() body: RedditPostAnalysisRequestDto,
    ): Promise<RedditPostAnalysisResponseDto> {
        try {
            const redditPost = {
                id: body.id, // not used in analysis
                title: body.title,
                text: body.text,
                commentCount: body.comments.length, // not used in analysis
                voteScore: 0, // not used in analysis
                subredditName: null, // not used in analysis
                publishedAt: new Date(), // not used in analysis
                createdAt: new Date(), // not used in analysis
                updatedAt: new Date(), // not used in analysis
            };
            const comments = body.comments.map((comment) => ({
                id: comment.id,
                body: comment.body,
                score: 0, // not used in analysis
                publishedAt: new Date(), // not used in analysis
                parentId: comment.parentId,
                postId: body.id,
            }));
            const input = this.analysisService.formattedRedditPostContent(
                redditPost,
                comments,
            );

            const result = await this.analysisService.fetchAnalysisSemantic(
                "REDDIT_POST",
                body.id,
                input,
            );

            return {
                id: body.id,
                input: input,
                analysis: {
                    ...result,
                    topics: [
                        ...new Set(
                            result.narratives?.flatMap(
                                (narrative) => narrative.topic || [],
                            ) || [],
                        ),
                    ],
                    microchip: result.narratives?.some((narrative) =>
                        narrative.topic?.includes("Microchip"),
                    )
                        ? "Yes"
                        : "No",
                    narratives:
                        result.narratives?.map((narrative) => ({
                            ...narrative,
                            topic: narrative.topic?.[0] || "",
                        })) || [],
                } as FullAnalysisDto,
            };
        } catch (error) {
            console.error("Error in controller:", error);
            throw error;
        }
    }
}
