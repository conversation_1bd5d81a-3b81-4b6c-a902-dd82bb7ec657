import {
    Prompt<PERSON>ara<PERSON>,
    SchemaFieldDescriptions,
    SystemInstruction,
} from "./types";

const ASSISTANT_ROLE_INSTRUCTION = `
You analyze social media posts to identify consumer sentiment and market trends related to Microchip Technology Inc.
Focus on mentions of these Microchip Technology Inc. related products/technologies:
- MPLAB
- HI-TECH C compilers
- PICkit, PIC<PERSON>t
- dsPIC3XF, PIC24H
- UNI/O serial EEPROM
- Serial EEPROM (SPI)
- PIC microcontrollers
- MCP250xx devices
- KEELOQ
- Atmel
`;

const ASSISTANT_OUTPUT_INSTRUCTION = `
Respond in valid JSON. Avoid filler or boilerplate phrases. Begin with the analysis content.
`;

const MICROCHIP_COMPANY_BRIEF = `
Microchip Technology Inc. is a leading provider of embedded solutions.
Applications span industrial, automotive, consumer, aerospace, defense, communications, and computing.
`;

const SYSTEM_INSTRUCTION: SystemInstruction = [
    ASSISTANT_ROLE_INSTRUCTION,
    MICROCHIP_COMPANY_BRIEF,
    ASSISTANT_OUTPUT_INSTRUCTION,
].join("\n\n");

const SCHEMA_DESCRIPTION: SchemaFieldDescriptions = {
    tenant: "Is the company Microchip Technology Inc or any of its products mentioned in the post in some way?",
    sentimentReasoning:
        "Describe the level of positive sentiment expressed specifically towards Microchip Technology Incorporated in the post. (max 150 characters).",
    sentiment:
        "What level of positive sentiment is expressed towards Microchip Technology Incorporated. 100 for the most positive, 0 for the most negative, -1 if it is not clear.",
    relevanceReasoning: [
        "Explain how relevant the post is to the company Microchip Technology Incorporated. ",
        "If the post mentions Microchip or its products, cite that.",
        "If not, cite why the post is relevant to Microchip’s core markets.",
        "If you cannot provide either, then say so.",
        "(Max 150 characters)",
    ].join("\n"),
    relevance: [
        "How relevant the post is to the company Microchip Technology Incorporated, on a scale of 0 to 100. ",
        "100: the post specifically talks about the company Microchip Technology Incorporated",
        "75: the post specifically talks about a specific product or company in Microchip Technology's industry",
        "50: the post mentions a topic that is relevant to Microchip Technology's industry",
        "25: the post discusses computer hardware or software",
        "10: the post alludes to a relevant topic but does not express an opionion relevant to the topic. E.g. discussing the commenter's personal life, correcting another commentor's spelling, spamming, etc.",
        "0:  the post does not discuss any topic relevant to Microchip Technology's industry",
    ].join("\n"),
    actionableReasoning: [
        "Should the company Microchip Technology Incorporated take action based on the post?",
        "Taking action might include responding to the post or addressing the concerns expressed by the poster.",
        "(max 150 characters)",
    ].join("\n"),
    isActionable:
        "Whether or not the company Microchip Technology Incorporated should take action based on the post.",
    tipsAndActions:
        "How should Microchip Technology Incorporated address consumers based on the post. Respond only with markdown bullet points, no other text.",
};

export const MICROCHIP_PROMPT_PARAMS: PromptParams = {
    systemInstruction: SYSTEM_INSTRUCTION,
    schemaDescription: SCHEMA_DESCRIPTION,
};
