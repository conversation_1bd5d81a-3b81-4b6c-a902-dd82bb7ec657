import { Injectable, Logger } from "@nestjs/common";

import {
    AllAboutCircuitsPost,
    RedditPost,
    SalesforcePost,
    YoutubeVideo,
} from "@prisma/client";
import { RedditComment } from "@prisma/client";
import { YoutubeComment } from "@prisma/client";
import { DataSource } from "@prisma/client";
import { ClsService } from "nestjs-cls";

import { PrismaRootService } from "../prisma-root/prisma-root.service";
import { PrismaService } from "../prisma/prisma.service";
import { SalesforceSiteName } from "../salesforce/types";
import { AnalysisResult } from "./analysis.service";

@Injectable()
export class AnalysisWriterService {
    private logger = new Logger("AnalysisWriterService");

    constructor(
        private readonly prisma: PrismaService,
        private readonly prismaRootService: PrismaRootService,
        private readonly cls: ClsService,
    ) {}

    async persistRedditAnalysis(
        redditPost: RedditPost,
        analysis: AnalysisResult,
    ) {
        await this.persistAnalysis("REDDIT_POST", redditPost, analysis);
    }

    async persistYoutubeVideoAnalysis(
        youtubeVideo: YoutubeVideo,
        analysis: AnalysisResult,
    ) {
        await this.persistAnalysis("YOUTUBE_VIDEO", youtubeVideo, analysis);
    }

    async persistRedditCommentAnalysis(
        redditComment: RedditComment,
        analysis: AnalysisResult,
    ) {
        await this.persistAnalysis("REDDIT_COMMENT", redditComment, analysis);
    }

    async persistYoutubeCommentAnalysis(
        youtubeComment: YoutubeComment,
        analysis: AnalysisResult,
    ) {
        await this.persistAnalysis("YOUTUBE_COMMENT", youtubeComment, analysis);
    }

    async persistSalesforceAnalysis(
        site: SalesforceSiteName,
        salesforcePost: SalesforcePost,
        analysis: AnalysisResult,
    ) {
        await this.persistAnalysis(site, salesforcePost, analysis);
    }

    async persistAllAboutCircuitsAnalysis(
        allAboutCircuitsPost: AllAboutCircuitsPost,
        analysis: AnalysisResult,
    ) {
        await this.persistAnalysis(
            "ALL_ABOUT_CIRCUITS",
            allAboutCircuitsPost,
            analysis,
        );
    }

    private async persistAnalysis(
        source: DataSource,
        item:
            | RedditPost
            | YoutubeVideo
            | RedditComment
            | YoutubeComment
            | SalesforcePost
            | AllAboutCircuitsPost,
        analysisResult: AnalysisResult,
    ) {
        const { id: sourceId } = item;
        const {
            sentiment,
            narratives,
            summary,
            relevance,
            relevanceReasoning,
            longSummary,
            isActionable,
            tipsAndActions,
            actionableReasoning,
        } = analysisResult;

        const narrativesToConnect: {
            id: number;
            sentiment: number;
            sentimentReasoning: string;
        }[] = [];

        for (const narrative of narratives) {
            const { aspect, topic, sentiment } = narrative;
            const [narrativeRecord] = await this.prisma.narrative.findMany({
                where: { aspect, topic: { name: topic } },
                select: { id: true },
            });
            if (!narrativeRecord) {
                this.logger.warn(
                    `Narrative aspect "${aspect}" for topic ${topic} not found. Skipping...`,
                );
                continue;
            }

            const isDuplicateNarrative = narrativesToConnect.some(
                ({ id }) => id === narrativeRecord.id,
            );
            if (isDuplicateNarrative) continue;

            narrativesToConnect.push({
                id: narrativeRecord.id,
                sentiment: sentiment?.score ?? -1,
                sentimentReasoning: sentiment?.reasoning,
            });
        }

        const analysis = {
            sentiment: sentiment?.score ?? -1,
            sentimentReasoning: sentiment?.reasoning,
            source,
            sourceId,
            summary,
            relevance,
            relevanceReasoning,
            longSummary,
            isActionable,
            tipsAndActions,
            actionableReasoning,
        };

        await this.prisma.$transaction(async (tx) => {
            // Upsert analysis record
            const analysisRecord = await tx.analysis.upsert({
                where: {
                    tenantId_sourceId: {
                        tenantId: this.cls.get("tenantId"),
                        sourceId,
                    },
                },
                update: analysis,
                create: {
                    ...analysis,
                    tenant: { connect: { id: this.cls.get("tenantId") } },
                },
            });

            // Delete existing narrative connections
            await tx.analysisToNarrative.deleteMany({
                where: { analysisId: analysisRecord.id },
            });

            // Create new narrative connections
            if (narrativesToConnect.length > 0) {
                await tx.analysisToNarrative.createMany({
                    data: narrativesToConnect.map(
                        ({ id, sentiment, sentimentReasoning }) => ({
                            analysisId: analysisRecord.id,
                            narrativeId: id,
                            sentiment,
                            sentimentReasoning,
                        }),
                    ),
                });
            }
        });
    }

    async persistRedditPostSearchableEntity(
        postId: string,
        embedding: number[],
        searchVector: string,
    ) {
        await this.persistEntity(
            "REDDIT_POST",
            postId,
            embedding,
            searchVector,
        );
    }

    async persistYoutubeVideoSearchableEntity(
        videoId: string,
        embedding: number[],
        searchVector: string,
    ) {
        await this.persistEntity(
            "YOUTUBE_VIDEO",
            videoId,
            embedding,
            searchVector,
        );
    }

    async persistRedditCommentSearchableEntity(
        commentId: string,
        embedding: number[],
        searchVector: string,
    ) {
        await this.persistEntity(
            "REDDIT_COMMENT",
            commentId,
            embedding,
            searchVector,
        );
    }

    async persistYoutubeCommentSearchableEntity(
        commentId: string,
        embedding: number[],
        searchVector: string,
    ) {
        await this.persistEntity(
            "YOUTUBE_COMMENT",
            commentId,
            embedding,
            searchVector,
        );
    }

    async persistSalesforcePostEmbedding(
        site: SalesforceSiteName,
        postId: string,
        embedding: number[],
        searchVector: string,
    ) {
        await this.persistEntity(site, postId, embedding, searchVector);
    }

    async persistAllAboutCircuitsEmbedding(
        postId: string,
        embedding: number[],
        searchVector: string,
    ) {
        await this.persistEntity(
            "ALL_ABOUT_CIRCUITS",
            postId,
            embedding,
            searchVector,
        );
    }

    private async persistEntity(
        source: DataSource,
        id: string,
        embedding: number[],
        searchVector: string,
    ) {
        await this.prismaRootService.$executeRaw`
                INSERT INTO "SearchableEntity" ("tenantId", source, "sourceId", embedding, "searchVector")
                VALUES (${this.cls.get("tenantId")}, ${source}::"DataSource", ${id}, ${embedding}::vector, ${searchVector}::tsvector)
                ON CONFLICT ("tenantId", source, "sourceId") DO UPDATE SET "embedding" = ${embedding}::vector, "searchVector" = ${searchVector}::tsvector
            `;
    }
}
