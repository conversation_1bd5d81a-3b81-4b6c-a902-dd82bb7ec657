import { ApiProperty } from "@nestjs/swagger";

import { Type } from "class-transformer";
import {
    IsArray,
    ValidateNested,
    IsString,
    IsOptional,
    IsNumber,
    IsBoolean,
    IsDefined,
} from "class-validator";

export class RedditPostAnalysisRequestDto {
    @ApiProperty({
        description: "ID of the Reddit post",
        example: "sn2o3f",
    })
    @IsString()
    id: string;

    @ApiProperty({
        description: "Title of the Reddit post",
        example: "NUL operator in XC8 Assembler",
    })
    @IsString()
    title: string;

    @ApiProperty({
        description: "Text content of the Reddit post",
        example: "I am trying to write a macro with an optional argument...",
    })
    @IsString()
    text: string;

    @ApiProperty({
        description: "Comments to include in the analysis",
        example: [
            {
                id: "hybiy0g",
                body: "Comment text here",
                parentId: null,
                postId: "sn2o3f",
            },
        ],
        isArray: true,
    })
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => CommentDto)
    comments: CommentDto[];

    @ApiProperty({
        description: "Topics to include in the analysis",
        example: [
            {
                name: "Programmers (MPLAB PM3, PICSTART Plus, PICkit 2, PICkit 3)",
                description:
                    "Programmers (MPLAB PM3, PICSTART Plus, PICkit 2, PICkit 3)",
            },
        ],
        isArray: true,
    })
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => TopicDto)
    topics: TopicDto[];
}

export class TopicDto {
    @ApiProperty({
        description: "Topic name",
        example: "Programmers (MPLAB PM3, PICSTART Plus, PICkit 2, PICkit 3)",
    })
    @IsString()
    name: string;

    @ApiProperty({
        description: "Topic description",
        example: "Programmers (MPLAB PM3, PICSTART Plus, PICkit 2, PICkit 3)",
        required: false,
    })
    @IsOptional()
    @IsString()
    description: string | null;
}

export class CommentDto {
    @ApiProperty({
        description: "Comment ID",
        example: "hybiy0g",
    })
    @IsString()
    id: string;

    @ApiProperty({
        description: "Comment body text",
        example: "Comment text here",
    })
    @IsString()
    body: string;

    @ApiProperty({
        description: "Parent comment ID (null for top-level)",
        example: "hyb0i6l",
        required: false,
    })
    @IsOptional()
    @IsString()
    parentId: string | null;

    @ApiProperty({
        description: "Post ID this comment belongs to",
        example: "sn2o3f",
    })
    @IsString()
    postId: string;
}

export class SentimentDto {
    @ApiProperty({
        description: "Reasoning for the sentiment score",
        example: "The post is neutral, focusing on technical troubleshooting.",
    })
    @IsString()
    reasoning: string;

    @ApiProperty({
        description: "Sentiment score",
        example: 0,
    })
    @IsNumber()
    score: number;
}

export class NarrativeDto {
    @ApiProperty({
        description: "Topic of the narrative",
        example: "MPLAB XC8",
    })
    @IsString()
    topic: string;

    @ApiProperty({
        description: "Aspect of the narrative",
        example: "FEATURE_REQUEST",
    })
    @IsString()
    aspect: string;

    @ApiProperty({
        description: "Sentiment analysis for this narrative",
        type: SentimentDto,
    })
    @ValidateNested()
    @Type(() => SentimentDto)
    sentiment: SentimentDto;
}

export class FullAnalysisDto {
    @ApiProperty({
        description: "Long summary of the analysis",
        example:
            "The post details a user's problem with XC8 assembler syntax...",
    })
    @IsString()
    longSummary: string;

    @ApiProperty({
        description: "Short summary of the analysis",
        example: "XC8 assembler macro syntax, NUL operator, PIC16F15344...",
    })
    @IsString()
    summary: string;

    @ApiProperty({
        description: "Extracted topics as JSON string",
        example: [
            "Programmers (MPLAB PM3, PICSTART Plus, PICkit 2, PICkit 3)",
            "MPLAB XC8",
        ],
        isArray: true,
    })
    @IsArray()
    topics: string[];

    @ApiProperty({
        description: "Microchip relevance assessment",
        example:
            "No, the post does not explicitly mention Microchip Technology Inc...",
    })
    @IsString()
    microchip: string;

    @ApiProperty({
        description: "Narrative analysis results",
        type: [NarrativeDto],
        isArray: true,
    })
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => NarrativeDto)
    narratives: NarrativeDto[];

    @ApiProperty({
        description: "Overall sentiment analysis",
        type: SentimentDto,
    })
    @ValidateNested()
    @Type(() => SentimentDto)
    sentiment: SentimentDto;

    @ApiProperty({
        description: "Relevance reasoning",
        example:
            "Discusses PIC assembler macro syntax issues, relevant to PIC microcontroller programming.",
    })
    @IsString()
    relevanceReasoning: string;

    @ApiProperty({
        description: "Relevance score (0-100)",
        example: 75,
    })
    @IsNumber()
    relevance: number;

    @ApiProperty({
        description: "Actionable reasoning",
        example:
            "Share this issue with Microchip support or community forums for troubleshooting.",
    })
    @IsString()
    actionableReasoning: string;

    @ApiProperty({
        description: "Whether the post is actionable",
        example: true,
    })
    @IsBoolean()
    isActionable: boolean;

    @ApiProperty({
        description: "Tips and actions",
        example:
            "- Share detailed error logs with Microchip support.\n- Update to latest XC8 version.",
    })
    @IsString()
    tipsAndActions: string;
}

export class RedditPostAnalysisResponseDto {
    @ApiProperty({
        description: "ID of the Reddit post",
        example: "sn2o3f",
    })
    @IsString()
    id: string;

    @ApiProperty({
        description: "Formatted input content (title + text + comments)",
        example:
            "# Post Title\n\nPost text content\n\n## Comments\n\nComment content...",
    })
    @IsString()
    input: string;

    @ApiProperty({
        description: "Full analysis results",
        type: FullAnalysisDto,
    })
    @IsDefined()
    @ValidateNested()
    @Type(() => FullAnalysisDto)
    analysis: FullAnalysisDto;
}
