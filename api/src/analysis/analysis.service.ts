import {
    BadGatewayException,
    Injectable,
    InternalServerErrorException,
    Logger,
} from "@nestjs/common";

import {
    NarrativeAspect,
    RedditComment,
    RedditPost,
    YoutubeComment,
    YoutubeVideo,
} from "@prisma/client";
import { htmlToText } from "html-to-text";
import { truncate } from "lodash";
import { ClsService } from "nestjs-cls";
import { zodResponseFormat } from "openai/helpers/zod";
import { ChatCompletion } from "openai/resources";
import { z, ZodSchema } from "zod";

import { OpenaiService } from "../openai/openai.service";
import { PrismaRootService } from "../prisma-root/prisma-root.service";
import { PrismaService } from "../prisma/prisma.service";
import { FORUM_POST_MAX_CHARS } from "./constants";
import { EmbeddingService } from "./embedding.service";
import { getPromptParams } from "./prompts/util";
import { commentsToMarkdown } from "./util/comment";

export type AnalysisResult = {
    sentiment?: {
        score?: number;
        reasoning?: string;
    };
    narratives?: {
        topic?: string;
        aspect?: NarrativeAspect;
        sentiment?: {
            score?: number;
            reasoning?: string;
        };
    }[];
    summary?: string;
    relevance?: number;
    relevanceReasoning?: string;
    longSummary?: string;
    isActionable?: boolean;
    actionableReasoning?: string;
    tipsAndActions?: string;
};

@Injectable()
export class AnalysisService {
    logger = new Logger("AnalysisService");

    private model = process.env.OPENAI_MODEL;

    constructor(
        private readonly openaiService: OpenaiService,
        private readonly prisma: PrismaService,
        private readonly prismaRootService: PrismaRootService,
        private readonly embeddingService: EmbeddingService,
        private readonly cls: ClsService,
    ) {}

    formattedRedditPostContent(
        redditPost: Partial<RedditPost>,
        comments: Partial<RedditComment>[] = [],
    ): string {
        const commentsContent =
            comments.length > 0
                ? `\n\n## Comments\n\n${commentsToMarkdown(comments)}`
                : "";

        return `# ${redditPost.title}\n\n${redditPost.text}${commentsContent}`;
    }

    async getRedditPostSentiment(
        redditPost: RedditPost,
        comments: Partial<RedditComment>[] = [],
    ): Promise<AnalysisResult> {
        const content = this.formattedRedditPostContent(redditPost, comments);

        return this.fetchAnalysisSemantic(
            "REDDIT_POST",
            redditPost.id,
            content,
        );
    }

    async getYoutubeVideoSentiment(
        youtubeVideo: YoutubeVideo,
        comments: Partial<YoutubeComment>[] = [],
    ): Promise<AnalysisResult> {
        // One hour of spoken English is 30,000 - 50,000 chars or 7,500 - 12,500 tokens
        const truncatedSubtitles = youtubeVideo.subtitles.slice(0, 50_000);
        const commentsContent =
            comments.length > 0
                ? `\n\n## Comments\n\n${commentsToMarkdown(comments)}`
                : "";

        return this.fetchAnalysisSemantic(
            "YOUTUBE_VIDEO",
            youtubeVideo.id,
            `# ${youtubeVideo.title}\n\n${truncatedSubtitles}${commentsContent}`,
        );
    }

    async getRedditCommentThreadSentiment(
        post: RedditPost,
        redditComments: Partial<RedditComment>[],
    ): Promise<AnalysisResult> {
        const [topLevelComment] = redditComments;
        const commentsContent = commentsToMarkdown(redditComments);
        const content = `# ${post.title}\n\n${post.text}\n\n## Comments\n\n${commentsContent}`;

        return this.fetchAnalysisSemantic(
            "REDDIT_COMMENT",
            topLevelComment.id,
            content,
        );
    }

    async getYoutubeCommentThreadSentiment(
        video: YoutubeVideo,
        comments: Partial<YoutubeComment>[],
    ): Promise<AnalysisResult> {
        const [topLevelComment] = comments;

        const commentsContent = commentsToMarkdown(comments);
        const content = `# ${video.title}\n\n## Comments\n\n${commentsContent}`;

        return this.fetchAnalysisSemantic(
            "YOUTUBE_COMMENT",
            topLevelComment.id,
            content,
        );
    }

    async getForumThreadSentiment(
        contentType: "AVR_FREAKS" | "MICROCHIP_CLASSIC" | "ALL_ABOUT_CIRCUITS",
        contentId: string,
        forumTitle: string,
        postTitle: string,
        postText: string,
        replies: { author: string; text: string }[],
    ): Promise<AnalysisResult> {
        const forumContent = `# ${forumTitle}`;
        const titleContent = `## ${postTitle}`;
        const postContent =
            postText &&
            `${truncate(postText, { length: FORUM_POST_MAX_CHARS })}`;

        const repliesContent =
            replies.length > 0 &&
            `## Replies\n\n${replies
                .map(
                    ({ author, text }) =>
                        `- **${author ?? "Anonymous"}:** ${truncate(htmlToText(text), { length: FORUM_POST_MAX_CHARS })}`,
                )
                .join("\n")}`;

        return this.fetchAnalysisSemantic(
            contentType,
            contentId,
            [forumContent, titleContent, postContent, repliesContent]
                .filter(Boolean)
                .join("\n\n"),
        );
    }

    public async fetchAnalysisSemantic(
        contentType:
            | "REDDIT_POST"
            | "REDDIT_COMMENT"
            | "YOUTUBE_VIDEO"
            | "YOUTUBE_COMMENT"
            | "AVR_FREAKS"
            | "MICROCHIP_CLASSIC"
            | "ALL_ABOUT_CIRCUITS",
        contentId: string,
        content: string,
    ): Promise<AnalysisResult> {
        const relevantTopics = await this.getSemanticTopicsForContentId(
            contentType,
            contentId,
        );

        // Filter by similarity threshold (lower = more similar)
        const threshold = 0.8; // Adjust this value
        const filteredTopics = relevantTopics.filter(
            (t) => t.similarity < threshold,
        );

        // Use the semantically relevant topics for analysis
        const topicNames = filteredTopics.map((t) => t.name);
        const schema = this.getSchema(topicNames);

        const tenantId = this.cls.get("tenantId");
        const { systemInstruction } = getPromptParams(tenantId);

        // Continue with your existing analysis logic...
        const result = await this.openaiService.getCompletion({
            messages: [
                {
                    role: "system",
                    content: systemInstruction,
                },
                {
                    role: "user",
                    content: [
                        "Here is the original post content:",
                        "<content>",
                        content,
                        "</content>",
                        "",
                        "And here is the first pass analysis:",
                        "<topics>",
                        `[${filteredTopics
                            .map((t) => `{"${t.name}": "${t.description}"}`)
                            .join(",")}]`,
                        "</topics>",
                    ].join("\n"),
                },
                {
                    role: "system",
                    content: [
                        "You are an assistant that checks that the topics are relevant to the content.",
                        "",
                        "Analyze the content and return the topic names that are relevant.",
                    ].join("\n"),
                },
            ],
            model: this.model,
            response_format: zodResponseFormat(schema, "second-pass-analysis"),
            ...(contentId && { metadata: { contentId } }),
        });

        try {
            return this.parseCompletionResult(result, schema);
        } catch (error) {
            this.logger.error(
                `Error parsing completion for contentId ${contentId}`,
            );
            throw error;
        }
    }

    private async getSemanticTopicsForContentId(
        contentType:
            | "REDDIT_POST"
            | "REDDIT_COMMENT"
            | "YOUTUBE_VIDEO"
            | "YOUTUBE_COMMENT"
            | "AVR_FREAKS"
            | "MICROCHIP_CLASSIC"
            | "ALL_ABOUT_CIRCUITS",
        contentId: string,
    ): Promise<
        Array<{ name: string; description: string; similarity: number }>
    > {
        // Ensure all topics have embeddings
        const [{ missingTopicEmbeddings }] = await this.prisma
            .$queryRawWithTenant<{ missingTopicEmbeddings: boolean }[]>`
            SELECT COUNT(*) > 0 AS "missingTopicEmbeddings"
            FROM "Topic"
            WHERE embedding IS NULL;
        `;

        if (missingTopicEmbeddings) await this.setTopicEmbeddings();

        // Get topics by embedding similarity
        const topics = await this.prisma.$queryRawWithTenant<
            Array<{ name: string; description: string; similarity: number }>
        >`
            WITH q AS (
                SELECT se.embedding AS qv
                FROM "SearchableEntity" se
                WHERE se."source" = ${contentType}::"DataSource"
                    AND se."sourceId" = ${contentId}
                    AND se.embedding IS NOT NULL
                LIMIT 1
            )
            SELECT
                t.name,
                t.description,
                (t.embedding <=> q.qv) AS similarity 
            FROM "Topic" t
            CROSS JOIN q
            WHERE t.embedding IS NOT NULL
            ORDER BY similarity ASC
            LIMIT 100;
        `;

        return topics;
    }

    private getSchema(topicNames: string[]) {
        const tenantId = this.cls.get("tenantId");
        const { schemaDescription } = getPromptParams(tenantId);

        return z.object({
            longSummary: z
                .string()
                .describe("Paragraph-long summary of the post."),
            summary: z
                .string()
                .describe(
                    "Compact, noun-based, summary of the opinions expressed in the post.",
                ),
            // Unused, but helps with reasoning
            topics: z
                .array(z.enum(topicNames as [string, ...string[]]))
                .min(0)
                .max(10)
                .transform((arr) => [...new Set(arr)])
                .describe(
                    `Topics that are relevant to the post. If none of the topics are relevant, return empty array.`,
                ),
            // Unused, but helps with reasoning
            tenant: z.string().describe(schemaDescription.tenant),
            narratives: z
                .array(
                    z.object({
                        topic: z
                            .enum(topicNames as [string, ...string[]])
                            .describe(
                                `A topic that is relevant to the post. If none of the topics are relevant, return empty array.`,
                            ),
                        aspect: z
                            .nativeEnum(NarrativeAspect)
                            .describe(
                                `What aspect of the topic is the post about. If it is not clear, return "UNKNOWN".`,
                            ),
                        sentiment: z.object({
                            reasoning: z
                                .string()
                                .describe(
                                    "Explain the sentiment expressed towards the topic and aspect. (max 150 characters).",
                                ),
                            score: z
                                .number()
                                .min(-1)
                                .max(100)
                                .describe(
                                    "What level of positive sentiment is expressed specifically about the topic and aspect. 100 for the most positive, 0 for the most negative, -1 if it is not clear.",
                                ),
                        }),
                    }),
                )
                .max(10)
                .describe(
                    [
                        "Analyze the post and return a list of topics and aspects that are clearly discussed.",
                        "Only include topics that are explicitly mentioned or strongly implied in the post.",
                        "Do NOT return any entry if the aspect is 'UNKNOWN' or if the topic is unclear.",
                        "Do NOT include entries just to fill the list.",
                        "Do not include duplicate topic–aspect pairs.",
                    ].join("\n"),
                ),
            sentiment: z.object({
                reasoning: z
                    .string()
                    .describe(schemaDescription.sentimentReasoning),
                score: z
                    .number()
                    .min(-1)
                    .max(100)
                    .describe(schemaDescription.sentiment),
            }),
            relevanceReasoning: z
                .string()
                .describe(schemaDescription.relevanceReasoning),
            relevance: z
                .number()
                .min(0)
                .max(100)
                .describe(schemaDescription.relevance),
            actionableReasoning: z
                .string()
                .describe(schemaDescription.actionableReasoning),
            isActionable: z.boolean().describe(schemaDescription.isActionable),
            tipsAndActions: z
                .string()
                .describe(schemaDescription.tipsAndActions),
        });
    }

    private parseCompletionResult<TSchema extends ZodSchema>(
        result: ChatCompletion,
        schema: TSchema,
    ): z.infer<TSchema> {
        const completion = result.choices[0];
        const completionContent = schema.safeParse(
            JSON.parse(completion.message.content),
        );
        if (!completionContent.success) {
            this.logger.error(JSON.stringify(completionContent.error.issues));
            throw new BadGatewayException("Failed to parse completion content");
        }

        return completionContent.data;
    }

    async getSummary(text: string) {
        const result = await this.openaiService.getCompletion({
            model: process.env.OPENAI_MODEL,
            messages: [
                {
                    role: "system",
                    content:
                        "You are a helpful assistant that creates concise, contextual summaries of text.",
                },
                { role: "user", content: text },
            ],
            max_completion_tokens: 500,
            temperature: 0.3,
        });

        return result.choices[0].message.content;
    }

    private async setTopicEmbeddings() {
        const topicsWithMissingEmbedding = await this.prisma
            .$queryRawWithTenant<
            { id: number; name: string; description: string }[]
        >`
            SELECT id, name, description FROM "Topic" WHERE embedding IS NULL
        `;

        for (const topic of topicsWithMissingEmbedding) {
            const embedding =
                await this.embeddingService.getTopicEmbedding(topic);

            const result = await this.prismaRootService.$executeRaw`
                UPDATE "Topic"
                SET embedding = ${embedding}::vector
                WHERE
                    id = ${topic.id};
            `;

            if (result === 0) {
                throw new InternalServerErrorException(
                    "Failed to set topic embedding",
                );
            }
        }
    }
}
