import { Injectable } from "@nestjs/common";

import { RedditPost, Topic } from "@prisma/client";
import { YoutubeVideo } from "@prisma/client";

import { EMBEDDING_MODEL } from "../openai/constants";
import { OpenaiService } from "../openai/openai.service";

@Injectable()
export class EmbeddingService {
    constructor(private readonly openaiService: OpenaiService) {}

    async getRedditPostEmbedding(summary: string, post: RedditPost) {
        return this.getEmbedding(`${summary}\n\n# ${post.title}\n${post.text}`);
    }

    async getYoutubeVideoEmbedding(summary: string, video: YoutubeVideo) {
        return this.getEmbedding(
            `${summary}\n\n# ${video.title}\n${video.subtitles}`,
        );
    }

    async getRedditCommentThreadEmbedding(
        summary: string,
        conversation: string,
    ) {
        return this.getEmbedding(`${summary}\n\n${conversation}`);
    }

    async getYoutubeCommentThreadEmbedding(
        summary: string,
        conversation: string,
    ) {
        return this.getEmbedding(`${summary}\n\n${conversation}`);
    }

    async getSalesforcePostEmbedding(conversation: string) {
        return this.getEmbedding(conversation);
    }

    async getForumThreadEmbedding(conversation: string) {
        return this.getEmbedding(conversation);
    }

    async getTopicEmbedding(topic: Pick<Topic, "name" | "description">) {
        const input = [`#${topic.name}`, topic.description]
            .filter(Boolean)
            .join("\n\n");

        return this.getEmbedding(input);
    }

    private async getEmbedding(input: string) {
        const embedding = await this.openaiService.getEmbedding({
            input: input,
            model: EMBEDDING_MODEL,
        });

        return embedding.data[0].embedding;
    }
}
