import { Inject<PERSON><PERSON>ue, Processor, WorkerHost } from "@nestjs/bullmq";
import { InternalServerErrorException } from "@nestjs/common";

import { NarrativeAspect } from "@prisma/client";
import { htmlToText } from "html-to-text";
import { truncate } from "lodash";

import { UseTenantCls } from "../generic/bullmq/processor";
import { TimelineService } from "../insights/timeline.service";
import { PrismaService } from "../prisma/prisma.service";
import { AI_ANALYSIS_QUEUE } from "../task/constants";
import {
    AIAnalysisAllAboutCircuitsJob,
    AIAnalysisJob,
    AIAnalysisQueue,
    AIAnalysisRedditJob,
    AIAnalysisSalesforceJob,
    AIAnalysisYoutubeJob,
} from "../task/types";
import { AnalysisWriterService } from "./analysis-writer.service";
import { AnalysisService } from "./analysis.service";
import { CommentLoaderService } from "./comment-loader.service";
import { SALESFORCE_POST_MAX_REPLIES } from "./constants";
import { EmbeddingService } from "./embedding.service";
import { KeywordService } from "./keyword.service";
import { cleanComment, commentsToMarkdown } from "./util/comment";
import { normalize } from "./util/topic";

@Processor(AI_ANALYSIS_QUEUE, { concurrency: 50 })
export class AnalysisProcessor extends WorkerHost {
    constructor(
        private readonly prisma: PrismaService,
        private readonly analysisService: AnalysisService,
        private readonly commentLoaderService: CommentLoaderService,
        private readonly analysisWriterService: AnalysisWriterService,
        private readonly embeddingService: EmbeddingService,
        private readonly keywordService: KeywordService,
        private readonly timelineService: TimelineService,
        @InjectQueue(AI_ANALYSIS_QUEUE)
        private readonly aiAnalysisQueue: AIAnalysisQueue,
    ) {
        super();
    }

    @UseTenantCls()
    async process(job: AIAnalysisJob) {
        const analysisType = job.data.type;
        switch (analysisType) {
            case "reddit":
                await this.processRedditJob(job as AIAnalysisRedditJob);
                break;
            case "youtube":
                await this.processYoutubeJob(job as AIAnalysisYoutubeJob);
                break;
            case "salesforce":
                await this.processSalesforceJob(job as AIAnalysisSalesforceJob);
                break;
            case "allAboutCircuits":
                await this.processAllAboutCircuitsJob(
                    job as AIAnalysisAllAboutCircuitsJob,
                );
                break;
            default:
                throw new InternalServerErrorException(
                    `Unsupported job type "${analysisType}"`,
                );
        }

        // Update TimelineItem materialized view
        const lastAnalysis = await this.prisma.analysis.findFirst({
            orderBy: { createdAt: "desc" },
        });

        const timeSinceLastViewRefresh = lastAnalysis
            ? Date.now() - lastAnalysis.createdAt.getTime()
            : 0;

        const pendingJobCount = await this.aiAnalysisQueue.count();

        // Refresh the timeline every 5 minutes, or when the queue is empty
        const shouldRefresh =
            pendingJobCount === 0 || timeSinceLastViewRefresh > 1000 * 60 * 5;

        if (shouldRefresh) {
            await this.timelineService.refreshMaterializedView();
        }
    }

    private async processYoutubeJob(job: AIAnalysisYoutubeJob) {
        // 1. Fetch
        // 2. Clean
        // 3. Embed
        // 4. Store Content / Embedding
        // 5. Analysis
        // 6. Store Analysis

        // 1. Fetch
        const { youtubeId } = job.data;

        const video = await this.prisma.youtubeVideo.findUniqueOrThrow({
            where: {
                tenantId_id: {
                    tenantId: job.data.tenantId,
                    id: youtubeId,
                },
            },
        });

        const topLevelComments =
            await this.commentLoaderService.getHighEngagementTopLevelYoutubeComments(
                youtubeId,
            );

        // 2. Clean
        const cleanComments = topLevelComments.map(cleanComment);

        job.log("Generating video summary");
        const summary = await this.analysisService.getSummary(video.subtitles);

        // 3. Embed
        job.log("Generating video embedding / keywords");
        const embedding = await this.embeddingService.getYoutubeVideoEmbedding(
            summary,
            video,
        );
        const searchVector =
            await this.keywordService.getYoutubeVideoSearchVector(
                summary,
                video,
            );

        // 4. Store Content / Embedding
        job.log("Persisting video transcript vector embedding / keywords");
        await this.analysisWriterService.persistYoutubeVideoSearchableEntity(
            youtubeId,
            embedding,
            searchVector,
        );

        // 5. Analysis
        job.log(
            `Analyzing video:\n\n# ${truncate(video.title, { length: 100 })}\n\n${truncate(video.subtitles, { length: 200 })}`,
        );
        const analysis = await this.analysisService.getYoutubeVideoSentiment(
            video,
            cleanComments,
        );

        // 6. Store Analysis
        job.log(
            `Persisting video analysis:\n"${JSON.stringify(analysis, null, 4)}"`,
        );
        await this.analysisWriterService.persistYoutubeVideoAnalysis(
            video,
            analysis,
        );

        job.log("Analyzing comment threads");

        job.log(`${topLevelComments.length} top level comments to analyze`);

        for (const topLevelComment of topLevelComments) {
            // 1. Fetch
            const recursiveCommentReplies =
                await this.commentLoaderService.getYoutubeCommentRepliesRecursive(
                    topLevelComment.id,
                );

            // 2. Clean
            const cleanComments = [
                topLevelComment,
                ...recursiveCommentReplies,
            ].map(cleanComment);

            const conversation = commentsToMarkdown(cleanComments);

            job.log("Generating comment thread summary");
            const summary = await this.analysisService.getSummary(conversation);

            // 3. Embed
            job.log("Generating comment thread embedding / keywords");
            const embedding =
                await this.embeddingService.getYoutubeCommentThreadEmbedding(
                    summary,
                    conversation,
                );
            const searchVector =
                await this.keywordService.getYoutubeCommentThreadSearchVector(
                    summary,
                    conversation,
                );

            // 4. Store Content / Embedding
            job.log("Persisting comment thread vector embedding");
            await this.analysisWriterService.persistYoutubeCommentSearchableEntity(
                topLevelComment.id,
                embedding,
                searchVector,
            );

            // 5. Analysis
            const analysis =
                await this.analysisService.getYoutubeCommentThreadSentiment(
                    video,
                    cleanComments,
                );

            // 6. Store Analysis
            job.log(conversation);
            job.log(
                `Persisting thread analysis:\n${JSON.stringify(analysis, null, 4)}`,
            );

            await this.analysisWriterService.persistYoutubeCommentAnalysis(
                topLevelComment,
                analysis,
            );
        }
    }

    private async processRedditJob(job: AIAnalysisRedditJob) {
        // 1. Fetch
        // 2. Clean
        // 3. Embed
        // 4. Store Content / Embedding
        // 5. Analysis
        // 6. Store Analysis

        // 1. Fetch
        const postId = job.data.postId;

        const post = await this.prisma.redditPost.findUniqueOrThrow({
            where: {
                tenantId_id: {
                    tenantId: job.data.tenantId,
                    id: postId,
                },
            },
        });

        job.log(
            `Analyzing post:\n\n# ${truncate(post.title, { length: 100 })}\n\n${truncate(post.text, { length: 200 })}`,
        );

        // 2. Clean
        job.log("Generating post summary");
        const summary = await this.analysisService.getSummary(post.text);

        // 3. Embed
        job.log("Generating post embedding / keywords");
        const embedding = await this.embeddingService.getRedditPostEmbedding(
            summary,
            post,
        );
        const searchVector =
            await this.keywordService.getRedditPostSearchVector(summary, post);

        // 4. Store Content / Embedding
        job.log("Persisting post text vector embedding / keywords");
        await this.analysisWriterService.persistRedditPostSearchableEntity(
            post.id,
            embedding,
            searchVector,
        );

        const topLevelComments =
            await this.commentLoaderService.getHighEngagementTopLevelRedditComments(
                postId,
            );

        const cleanComments = topLevelComments.map(cleanComment);

        // 5. Analysis
        const analysis = await this.analysisService.getRedditPostSentiment(
            post,
            cleanComments,
        );

        // 6. Store Analysis
        job.log(
            `Persisting post analysis:\n${JSON.stringify(analysis, null, 4)}`,
        );

        await this.analysisWriterService.persistRedditAnalysis(post, analysis);

        job.log("Analyzing comment threads");

        job.log(`${topLevelComments.length} top level comments to analyze`);

        for (const topLevelComment of topLevelComments) {
            // 1. Fetch
            const recursiveCommentReplies =
                await this.commentLoaderService.getRedditCommentRepliesRecursive(
                    topLevelComment.id,
                );

            // 2. Clean
            const cleanComments = [
                topLevelComment,
                ...recursiveCommentReplies,
            ].map(cleanComment);

            const conversation = commentsToMarkdown(cleanComments);
            job.log(conversation);

            // 3. Embed
            job.log("Generating comment thread summary");
            const summary = await this.analysisService.getSummary(conversation);

            job.log("Generating comment thread embedding / keywords");
            const embedding =
                await this.embeddingService.getRedditCommentThreadEmbedding(
                    summary,
                    conversation,
                );
            const searchVector =
                await this.keywordService.getRedditCommentThreadSearchVector(
                    summary,
                    conversation,
                );

            // 4. Store Content / Embedding
            job.log("Persisting comment thread vector embedding / keywords");
            await this.analysisWriterService.persistRedditCommentSearchableEntity(
                topLevelComment.id,
                embedding,
                searchVector,
            );

            // 5. Analysis
            const analysis =
                await this.analysisService.getRedditCommentThreadSentiment(
                    post,
                    cleanComments,
                );

            // 6. Store Analysis
            job.log(
                `Persisting thread analysis:\n${JSON.stringify(analysis, null, 4)}`,
            );
            await this.analysisWriterService.persistRedditCommentAnalysis(
                topLevelComment,
                analysis,
            );
        }
    }

    private async processSalesforceJob(job: AIAnalysisSalesforceJob) {
        // 1. Fetch
        // 2. Clean
        // 3. Embed
        // 4. Store Content / Embedding
        // 5. Analysis
        // 6. Store Analysis
        const { siteName, topicId } = job.data;

        // 1. Fetch
        const {
            forum: { parentForum, ...subforum },
            ...post
        } = await this.prisma.salesforcePost.findUniqueOrThrow({
            where: {
                id: topicId,
            },
            include: {
                forum: {
                    include: {
                        parentForum: true,
                    },
                },
            },
        });

        // Get replies from database
        const replyCount = await this.prisma.salesforcePostReply.count({
            where: {
                postId: topicId,
            },
        });

        if (replyCount <= SALESFORCE_POST_MAX_REPLIES) {
            job.log(`Analyzing ${replyCount} replies`);
        } else {
            job.log(
                `Post has ${replyCount} replies, analyzing only the first ${SALESFORCE_POST_MAX_REPLIES}`,
            );
        }

        const replies = await this.prisma.salesforcePostReply.findMany({
            where: {
                postId: topicId,
            },
            take: SALESFORCE_POST_MAX_REPLIES,
            orderBy: {
                publishedAt: "asc",
            },
        });

        // 3. Embed
        const searchVector = commentsToMarkdown([post, ...replies]);
        const embedding =
            await this.embeddingService.getSalesforcePostEmbedding(
                searchVector,
            );

        // 4. Store Content / Embedding
        await this.analysisWriterService.persistSalesforcePostEmbedding(
            siteName,
            topicId,
            embedding,
            searchVector,
        );

        // 5. Analysis
        job.log(`Analyzing post: "${truncate(post.title, { length: 100 })}"`);
        const analysis = await this.analysisService.getForumThreadSentiment(
            siteName,
            topicId,
            parentForum.name,
            post.title,
            post.text,
            replies.map(({ userScreenName, text }) => ({
                author: userScreenName ?? "Deleted User",
                text: htmlToText(text),
            })),
        );

        const narratives = [...analysis.narratives];

        // Ensure forum name is included as a topic
        const hasForumTopic = analysis.narratives.some(
            (narrative) =>
                normalize(narrative.topic) === normalize(parentForum.name),
        );

        const isForumNameTopic = !!(await this.prisma.topic.findUnique({
            where: {
                tenantId_name: {
                    tenantId: job.data.tenantId,
                    name: parentForum.name,
                },
            },
        }));

        if (!hasForumTopic && isForumNameTopic)
            narratives.push({
                topic: parentForum.name,
                aspect: NarrativeAspect.UNKNOWN,
                sentiment: {
                    score: -1,
                    reasoning: "",
                },
            });

        // Ensure subforum name is included as a topic
        const hasSubforumTopic = analysis.narratives.some(
            (narrative) =>
                normalize(narrative.topic) === normalize(subforum.name),
        );

        const isSubforumNameTopic = !!(await this.prisma.topic.findUnique({
            where: {
                tenantId_name: {
                    tenantId: job.data.tenantId,
                    name: subforum.name,
                },
            },
        }));

        if (!hasSubforumTopic && isSubforumNameTopic)
            narratives.push({
                topic: subforum.name,
                aspect: NarrativeAspect.UNKNOWN,
                sentiment: {
                    score: -1,
                    reasoning: "",
                },
            });

        // 6. Store Analysis
        await this.analysisWriterService.persistSalesforceAnalysis(
            siteName,
            post,
            {
                ...analysis,
                narratives,
            },
        );
    }

    private async processAllAboutCircuitsJob(
        job: AIAnalysisAllAboutCircuitsJob,
    ) {
        // 1. Fetch
        // 2. Clean
        // 3. Embed
        // 4. Store Content / Embedding
        // 5. Analysis
        // 6. Store Analysis

        // 1. Fetch
        const { postId } = job.data;

        const post = await this.prisma.allAboutCircuitsPost.findUniqueOrThrow({
            where: { id: postId },
            include: {
                forum: true,
            },
        });

        const replies = await this.prisma.allAboutCircuitsPostReply.findMany({
            where: {
                postId,
            },
            orderBy: {
                publishedAt: "asc",
            },
        });

        // 2. Clean (skipped)

        // 3. Embed
        const searchVector = commentsToMarkdown([post, ...replies]);
        const embedding =
            await this.embeddingService.getForumThreadEmbedding(searchVector);

        // 4. Store Content / Embedding
        await this.analysisWriterService.persistAllAboutCircuitsEmbedding(
            postId,
            embedding,
            searchVector,
        );

        // 5. Analysis
        job.log(`Analyzing post: "${truncate(post.title, { length: 100 })}"`);
        const analysis = await this.analysisService.getForumThreadSentiment(
            "ALL_ABOUT_CIRCUITS",
            postId,
            post.forum.name,
            post.title,
            post.text,
            replies,
        );

        // 6. Store Analysis
        await this.analysisWriterService.persistAllAboutCircuitsAnalysis(
            post,
            analysis,
        );
    }
}
