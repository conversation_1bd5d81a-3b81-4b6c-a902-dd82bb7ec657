import { Injectable } from "@nestjs/common";

import { Prisma, RedditComment, YoutubeComment } from "@prisma/client";

import { PrismaService } from "../prisma/prisma.service";

@Injectable()
export class CommentLoaderService {
    constructor(private readonly prisma: PrismaService) {}

    async getHighEngagementTopLevelRedditComments(postId: string, take = 3) {
        return this.prisma.redditComment.findMany({
            where: {
                postId,
                parentId: null,
            },
            orderBy: {
                score: "desc",
            },
            take,
        });
    }

    async getHighEngagementTopLevelYoutubeComments(videoId: string, take = 3) {
        return this.prisma.youtubeComment.findMany({
            where: {
                videoId,
                parentId: null,
            },
            orderBy: {
                score: "desc",
            },
            take,
        });
    }

    async getRedditCommentRepliesRecursive(commentId: string, depth = 3) {
        const replies = await this.getCommentRepliesRecursive<RedditComment>(
            "RedditComment",
            commentId,
            depth,
        );

        return replies;
    }

    async getYoutubeCommentRepliesRecursive(commentId: string, depth = 3) {
        const replies = await this.getCommentRepliesRecursive<YoutubeComment>(
            "YoutubeComment",
            commentId,
            depth,
        );

        return replies;
    }

    private async getCommentRepliesRecursive<T>(
        table: "RedditComment" | "YoutubeComment",
        topLevelCommentId: string,
        depth = 3,
    ) {
        const tableName = Prisma.raw(`"${table}"`);
        const replies: T[] = await this.prisma.$queryRawWithTenant`
            WITH RECURSIVE reply_tree AS (
                SELECT *, 1 AS depth
                FROM ${tableName}
                WHERE "parentId" = ${topLevelCommentId}

                UNION ALL

                SELECT c.*, rt.depth + 1
                FROM ${tableName} c
                INNER JOIN reply_tree rt ON c."parentId" = rt.id
                WHERE rt.depth < ${depth}
            ),
            ranked_comments AS (
                SELECT *,
                    ROW_NUMBER() OVER (PARTITION BY depth ORDER BY score DESC) as rank
                FROM reply_tree
            )
            SELECT *
            FROM ranked_comments
            WHERE rank = 1
            ORDER BY depth;
        `;

        return replies;
    }
}
