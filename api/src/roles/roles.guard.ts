import { Injectable, CanActivate, ExecutionContext } from "@nestjs/common";
import { Reflector } from "@nestjs/core";

import { Role } from "./role.enum";
import { RolesService } from "./roles.service";

@Injectable()
export class RolesGuard implements CanActivate {
    constructor(
        private reflector: Reflector,
        private rolesService: RolesService,
    ) {}

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const requiredRoles = this.reflector.getAllAndOverride<Role[]>(
            "roles",
            [context.getHandler(), context.getClass()],
        );

        if (!requiredRoles) return true;

        const { userId } = context.switchToHttp().getRequest().user;

        if (!userId) return false;

        const userRoles = await this.rolesService.getUserRoles(userId);
        const userRoleNames = userRoles.roles.map(
            (userRole) => userRole.role.name,
        );
        const userHasMatchingRole = requiredRoles.some((role) =>
            userRoleNames.includes(role),
        );

        return userHasMatchingRole;
    }
}
