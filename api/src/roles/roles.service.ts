import { Injectable } from "@nestjs/common";

import { PrismaService } from "../prisma/prisma.service";
import { Role } from "./role.enum";

@Injectable()
export class RolesService {
    constructor(private prisma: PrismaService) {}

    async attachRoleToUser(userId: string, roleName: Role) {
        const role = await this.prisma.role.findUniqueOrThrow({
            where: {
                name: roleName,
            },
        });

        if (await this.hasUserRole(userId, roleName)) return;

        await this.prisma.rolesOnUsers.create({
            data: {
                userId,
                roleId: role.id,
            },
        });
    }

    async removeRoleFromUser(userId: string, roleName: Role) {
        const role = await this.prisma.role.findUniqueOrThrow({
            where: {
                name: roleName,
            },
        });

        if (!(await this.hasUserRole(userId, roleName))) return;

        await this.prisma.rolesOnUsers.delete({
            where: {
                userId_roleId: {
                    userId,
                    roleId: role.id,
                },
            },
        });
    }

    async setUserRole(userId: string, roleName: Role, attached: boolean) {
        return attached
            ? this.attachRoleToUser(userId, roleName)
            : this.removeRoleFromUser(userId, roleName);
    }

    async getUserRoles(userId: string) {
        return this.prisma.user.findUniqueOrThrow({
            select: {
                roles: {
                    select: {
                        role: true,
                    },
                },
            },
            where: { id: userId },
        });
    }

    async hasUserRole(userId: string, roleName: Role): Promise<boolean> {
        const roles = (await this.getUserRoles(userId)) || { roles: [] };
        return !!roles.roles.find(({ role: { name } }) => roleName === name);
    }
}
