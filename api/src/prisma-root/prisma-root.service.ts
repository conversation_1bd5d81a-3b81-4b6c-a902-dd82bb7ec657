// Bypasses row-level security on tables when they need to be accessed without a specific tenant
/* eslint-disable @darraghor/nestjs-typed/injectable-should-be-provided */
import { Injectable } from "@nestjs/common";

import { PrismaClient, PrismaPromise } from "@prisma/client";
import { Sql } from "@prisma/client/runtime/library";

@Injectable()
export class PrismaRootService {
    private client: PrismaClient;

    constructor() {
        this.client = new PrismaClient({
            transactionOptions: {
                maxWait: 10000,
                timeout: 15000,
            },
        });
    }

    async onModuleDestroy() {
        await this.client.$disconnect();
    }

    $queryRaw<T = unknown>(
        query: TemplateStringsArray | Sql,
        ...values: unknown[]
    ): PrismaPromise<T> {
        return this.client.$queryRaw<T>(query, ...values);
    }

    $executeRaw(
        query: TemplateStringsArray | Sql,
        ...values: unknown[]
    ): PrismaPromise<number> {
        return this.client.$executeRaw(query, ...values);
    }

    get user() {
        return this.client.user;
    }

    get role() {
        return this.client.role;
    }

    get rolesOnUsers() {
        return this.client.rolesOnUsers;
    }

    get userVerification() {
        return this.client.userVerification;
    }

    get userSession() {
        return this.client.userSession;
    }

    get tenant() {
        return this.client.tenant;
    }
}
