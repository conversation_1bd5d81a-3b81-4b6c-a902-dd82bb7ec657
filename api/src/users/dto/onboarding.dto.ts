import { ApiProperty } from "@nestjs/swagger";

import { Transform } from "class-transformer";
import { IsEmail, IsIn, IsString, IsStrongPassword } from "class-validator";

import { sanitizeEmailAddress } from "../../common/util/email";

export class OnboardingResponseDto {
    @IsEmail()
    email: string;

    @IsString()
    signupCode: string;

    @IsString()
    firstName: string;

    @IsString()
    lastName: string;

    @IsIn(["PASSWORD_CREATION", "COMPLETE"])
    onboardingStep: string;
}

export class SetPasswordRequestDto {
    @IsEmail()
    @Transform(({ value }) => sanitizeEmailAddress(value))
    email: string;

    @IsString()
    code: string;

    @IsStrongPassword()
    @ApiProperty({ example: "Strongpassword!1" })
    password: string;
}
