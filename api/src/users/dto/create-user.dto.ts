import { ApiProperty } from "@nestjs/swagger";

import { Transform } from "class-transformer";
import {
    IsArray,
    IsBoolean,
    IsEmail,
    IsOptional,
    IsString,
} from "class-validator";
import { randomUUID } from "crypto";

import { sanitizeEmailAddress } from "../../common/util/email";

const exampleUser = `user-${randomUUID().slice(0, 8)}`;
const exampleEmail = `${exampleUser}@example.com`;

export class CreateUserRequestDto {
    @IsString()
    @ApiProperty({ example: "First" })
    firstName: string;

    @IsString()
    @IsOptional()
    @ApiProperty({ example: "Last" })
    lastName: string;

    @IsEmail()
    @ApiProperty({ example: exampleEmail })
    @Transform(({ value }) => sanitizeEmailAddress(value))
    email: string;

    @IsBoolean()
    @IsOptional()
    isAdmin: boolean;

    @IsArray()
    @IsOptional()
    @ApiProperty({
        isArray: true,
        type: [Number],
        default: [],
    })
    groups: number[];
}
