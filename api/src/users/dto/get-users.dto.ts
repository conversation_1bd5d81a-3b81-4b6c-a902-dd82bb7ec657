import { Transform } from "class-transformer";
import { IsEnum, IsOptional } from "class-validator";

import {
    PaginatedRequestDto,
    PaginatedResponseDto,
} from "../../generic/dto/paginated.dto";
import { Role } from "../../roles/role.enum";
import { UserDto } from "./user.dto";

export class GetAllUsersDto extends PaginatedRequestDto {
    @IsEnum(Role, { each: true })
    @Transform(({ value }) => (Array.isArray(value) ? value : [value]))
    @IsOptional()
    roles?: Role[];
}

export class GetAllUsersResponseDto extends PaginatedResponseDto<UserDto> {}
