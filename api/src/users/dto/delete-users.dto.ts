import { ApiProperty } from "@nestjs/swagger";

import { ArrayNotEmpty, IsArray, IsString } from "class-validator";

export class DeleteUsersRequestDto {
    @ApiProperty({
        isArray: true,
        type: [String],
        description: "Array of user IDs to delete",
        example: ["user1234", "user567"],
    })
    @IsArray()
    @ArrayNotEmpty()
    @IsString({ each: true })
    userIds: string[];
}
