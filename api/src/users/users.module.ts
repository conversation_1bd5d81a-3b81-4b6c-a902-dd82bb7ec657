import { <PERSON>du<PERSON> } from "@nestjs/common";
import { JwtModule, JwtService } from "@nestjs/jwt";

import { jwtConstants } from "../auth/constants";
import { EmailModule } from "../email/email.module";
import { GroupsModule } from "../groups/groups.module";
import { PrismaRootService } from "../prisma-root/prisma-root.service";
import { RolesModule } from "../roles/roles.module";
import { UserVerificationModule } from "../user-verification/user-verification.module";
import { UsersController } from "./users.controller";
import { UsersService } from "./users.service";

@Module({
    imports: [
        EmailModule,
        GroupsModule,
        JwtModule.register({
            secret: jwtConstants.secret,
            signOptions: { expiresIn: "60s" },
        }),
        JwtModule,
        RolesModule,
        UserVerificationModule,
    ],
    providers: [JwtService, PrismaRootService, UsersService],
    exports: [UsersService],
    controllers: [UsersController],
})
export class UsersModule {}
