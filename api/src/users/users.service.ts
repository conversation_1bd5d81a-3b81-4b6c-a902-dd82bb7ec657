import {
    BadRequestException,
    ConflictException,
    Injectable,
    NotFoundException,
} from "@nestjs/common";

import { OnboardingStep, Prisma } from "@prisma/client";
import { compare, hashSync } from "bcrypt";
import { ClsService } from "nestjs-cls";

import { sanitizeEmailAddress } from "../common/util/email";
import { GroupsService } from "../groups/groups.service";
import { PrismaRootService } from "../prisma-root/prisma-root.service";
import { PrismaService } from "../prisma/prisma.service";
import { Role } from "../roles/role.enum";
import { RolesService } from "../roles/roles.service";
import { UserVerificationService } from "../user-verification/user-verification.service";
import { CreateUserRequestDto } from "./dto/create-user.dto";
import { SetPasswordDto } from "./dto/update-onboarding.dto";
import {
    UpdatePasswordRequestDto,
    UpdateUserRequestDto,
} from "./dto/update-user.dto";

@Injectable()
export class UsersService {
    constructor(
        private prisma: PrismaService,
        private prismaRootService: PrismaRootService,
        private cls: ClsService,
        private userVerificationService: UserVerificationService,
        private rolesService: RolesService,
        private groupsService: GroupsService,
    ) {}

    async create(createUserDto: CreateUserRequestDto) {
        const { email, firstName, lastName } = createUserDto;
        const tenantId = this.cls.get("tenantId");

        // Check if user already exists within this tenant
        const conflictingUser = await this.prisma.user.findUnique({
            where: {
                email,
                tenants: {
                    some: {
                        id: tenantId,
                    },
                },
            },
        });
        if (conflictingUser) throw new ConflictException("User already exists");

        // Create new user or add them to this tenant
        const user = await this.prismaRootService.user.upsert({
            where: {
                email,
            },
            update: {
                tenants: {
                    connect: { id: tenantId },
                },
            },
            create: {
                username: email,
                firstName,
                lastName,
                passwordHash: "",
                email,
                onboardingStep: "PASSWORD_CREATION",
                tenants: {
                    connect: {
                        id: tenantId,
                    },
                },
            },
        });

        return user;
    }

    async findOne(usernameOrEmail: string) {
        const sanitizedUsernameOrEmail = sanitizeEmailAddress(usernameOrEmail);
        return this.prismaRootService.user.findFirst({
            where: {
                OR: [
                    { username: sanitizedUsernameOrEmail },
                    { email: sanitizedUsernameOrEmail },
                ],
            },
            include: {
                tenants: {
                    select: {
                        id: true,
                        name: true,
                    },
                    orderBy: {
                        createdAt: "asc",
                    },
                },
            },
        });
    }

    async findById(userId: string) {
        const user = await this.prisma.user.findUniqueOrThrow({
            select: {
                id: true,
                email: true,
                firstName: true,
                lastName: true,
                roles: {
                    include: {
                        role: true,
                    },
                },
                groups: {
                    include: {
                        group: true,
                    },
                },
                tenants: {
                    select: {
                        id: true,
                        name: true,
                    },
                },
            },
            where: {
                id: userId,
            },
        });

        return user;
    }

    async findAll(take: number, skip: number, matchingRoles: Role[] = []) {
        if (!matchingRoles.length)
            return this.findAllWithoutFilters(take, skip);

        const filters = {
            where: {
                roles: {
                    some: {
                        role: {
                            name: {
                                in: matchingRoles,
                            },
                        },
                    },
                },
            },
        };

        return this.findAllWithFilters(take, skip, filters);
    }

    private async findAllWithoutFilters(take: number, skip: number) {
        return this.findAllWithFilters(take, skip);
    }

    private async findAllWithFilters(
        take: number,
        skip: number,
        { where }: { where?: Prisma.UserWhereInput } = {},
    ) {
        const users = await this.prisma.user.findMany({
            select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
                roles: {
                    include: {
                        role: true,
                    },
                },
                groups: {
                    include: {
                        group: true,
                    },
                },
            },
            where,
            take,
            skip,
            orderBy: {
                createdAt: "desc",
            },
        });

        const totalUsers = await this.prisma.user.count({ where });

        return { users, totalUsers };
    }

    async updateOne(
        userId: string,
        updateUserRequestDto: UpdateUserRequestDto,
    ) {
        const { firstName, lastName, email, isAdmin } = updateUserRequestDto;

        await this.rolesService.setUserRole(userId, Role.Admin, isAdmin);

        await this.groupsService.setUserGroups(
            userId,
            updateUserRequestDto.groups,
        );

        return this.prisma.user.update({
            where: {
                id: userId,
            },
            data: { firstName, lastName, email },
            include: {
                groups: {
                    include: {
                        group: true,
                    },
                },
            },
        });
    }

    async setPassword(userId: string, password: string) {
        const passwordHash = hashSync(password, 10);
        await this.prismaRootService.user.update({
            where: { id: userId },
            data: { passwordHash },
        });
    }

    async updatePassword(
        userId: string,
        updatePasswordRequestDto: UpdatePasswordRequestDto,
    ) {
        const { currentPassword, newPassword } = updatePasswordRequestDto;

        const { passwordHash } = await this.prisma.user.findUniqueOrThrow({
            select: { passwordHash: true },
            where: { id: userId },
        });
        const isCurrentPasswordValid = await compare(
            currentPassword,
            passwordHash,
        );
        if (!isCurrentPasswordValid)
            throw new BadRequestException({
                message: [
                    {
                        field: "currentPassword",
                        error: "currentPassword is incorrect",
                    },
                ],
            });

        await this.setPassword(userId, newPassword);
    }

    async delete(userId: string) {
        const user = await this.prisma.user.findUniqueOrThrow({
            where: {
                id: userId,
            },
            include: {
                roles: {
                    include: {
                        role: true,
                    },
                },
                sessions: true,
                groups: {
                    include: {
                        group: true,
                    },
                },
            },
        });

        await this.prisma.deletedEntity.create({
            data: {
                model: "User",
                modelId: user.id,
                data: user,
            },
        });

        await this.prisma.user.delete({
            where: {
                id: userId,
            },
        });
    }

    async getOnboardingInfo(userId: string, signupCode: string) {
        const userVerification =
            await this.userVerificationService.getUserVerification(
                userId,
                signupCode,
            );

        const user = await this.prismaRootService.user.findUniqueOrThrow({
            where: {
                id: userId,
            },
        });

        return { user, userVerification };
    }

    async completeOnboarding(
        userId: string,
        signupCode: string,
        setPasswordDto: SetPasswordDto,
    ) {
        const userVerification =
            await this.userVerificationService.getUserVerification(
                userId,
                signupCode,
            );
        if (!userVerification) throw new NotFoundException();

        const passwordHash = hashSync(setPasswordDto.password, 10);

        await this.prismaRootService.user.update({
            where: {
                id: userId,
            },
            data: {
                passwordHash,
                onboardingStep: OnboardingStep.COMPLETE,
            },
        });

        await this.userVerificationService.consumeUserVerification(
            userId,
            signupCode,
        );
    }
}
