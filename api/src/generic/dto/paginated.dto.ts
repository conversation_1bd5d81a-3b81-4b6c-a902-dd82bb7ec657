import { ApiProperty } from "@nestjs/swagger";

import { Type } from "class-transformer";
import { IsInt, Min } from "class-validator";

export class PaginatedRequestDto {
    @Min(1)
    @IsInt()
    @Type(() => Number)
    @ApiProperty({
        required: false,
    })
    page: number = 1;

    @Min(1)
    @IsInt()
    @Type(() => Number)
    @ApiProperty({
        required: false,
    })
    size: number = 10;
}

export class PaginatedResponseDto<T> {
    @ApiProperty({ isArray: true })
    items: T[];
    total: number;
    page: number;
    size: number;
    totalPages: number;
}
