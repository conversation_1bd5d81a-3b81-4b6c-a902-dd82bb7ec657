import { ApiPropertyOptional } from "@nestjs/swagger";

import { IsOptional, IsTimeZone } from "class-validator";

export interface DateFilterParams {
    dateFrom?: string;
    dateTo?: string;
    timeZone?: string;
}

export class DateFilteredRequestDto implements DateFilterParams {
    @IsOptional()
    @ApiPropertyOptional({
        description: "Inclusive start date",
        pattern: "\\d\\d\\d\\d-\\d\\d-\\d\\d",
    })
    dateFrom?: string;

    @IsOptional()
    @ApiPropertyOptional({
        description: "Inclusive end date",
        pattern: "\\d\\d\\d\\d-\\d\\d-\\d\\d",
    })
    dateTo?: string;

    @IsOptional()
    @IsTimeZone()
    @ApiPropertyOptional({
        description: "Date filter timezone",
        example: "America/Phoenix",
    })
    timeZone?: string = "America/Phoenix";
}
