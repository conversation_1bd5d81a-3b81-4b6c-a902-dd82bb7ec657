import { Fetcher, paginated } from "./util";

describe("paginated", () => {
    it("returns expected properties", async () => {
        const fetcher: Fetcher<number> = () =>
            new Promise((resolve) =>
                resolve({ items: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9], total: 250 }),
            );
        const params = { page: 3, size: 10 };

        const result = await paginated(fetcher, params);

        expect(result).toMatchObject({
            items: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
            total: 250,
            page: 3,
            size: 10,
            totalPages: 25,
        });
    });
});
