import { PaginatedRequestDto } from "./dto/paginated.dto";

export type Fetcher<T> = (
    take: number,
    skip: number,
) => Promise<{ items: T[]; total: number }>;

export async function paginated<T>(
    fetcher: Fetcher<T>,
    params: PaginatedRequestDto,
) {
    const { page, size } = params;
    const take = size;
    const skip = (page - 1) * size;

    const { items, total } = await fetcher(take, skip);
    const totalPages = Math.ceil(total / size);

    return {
        items,
        total,
        page,
        size,
        totalPages,
    };
}
