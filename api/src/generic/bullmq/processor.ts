import { Job } from "bullmq";
import { ClsService } from "nestjs-cls";
import { UseCls } from "nestjs-cls";

// Extract tenantId from job data and add to continuation local storage for the life cycle of the job processor
export function UseTenantCls<T extends Job<{ tenantId?: string }>>() {
    return UseCls<[T]>({
        setup: (cls: ClsService, job) => {
            cls.set("tenantId", job.data.tenantId);
        },
    });
}
