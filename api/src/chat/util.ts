import {
    Response,
    ResponseInputItem,
} from "openai/resources/responses/responses";

import { InputMessageDto, OutputMessageDto } from "./dto/chat.dto";

export function inputMessageToOpenAIInputItem(
    message: InputMessageDto,
): ResponseInputItem {
    return {
        role: "user",
        content: [
            {
                type: "input_text",
                text: message.content,
            },
        ],
    };
}

export function openAIResponseToOutputMessage(
    response: Response,
): OutputMessageDto {
    return {
        id: response.id,
        type: "text",
        content: response.output_text,
    };
}

export function responseHasFunctionCalls(response: Response) {
    return response?.output?.some((output) => output.type === "function_call");
}
