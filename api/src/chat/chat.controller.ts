import { Body, Controller, Post } from "@nestjs/common";
import { ApiCreatedResponse, ApiTags } from "@nestjs/swagger";

import { ChatService } from "./chat.service";
import { ChatRequestDto, ChatResponseDto } from "./dto/chat.dto";

@Controller("chat")
@ApiTags("*** insights - chat")
export class ChatController {
    constructor(private readonly chatService: ChatService) {}

    @Post()
    @ApiCreatedResponse({
        type: ChatResponseDto,
    })
    async chat(@Body() body: ChatRequestDto): Promise<ChatResponseDto> {
        return this.chatService.chat(body);
    }
}
