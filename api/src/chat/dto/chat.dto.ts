import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";

import { DataSource } from "@prisma/client";
import { Type } from "class-transformer";
import {
    IsArray,
    ValidateNested,
    IsString,
    IsIn,
    IsOptional,
    IsEnum,
} from "class-validator";

export class InputMessageDto {
    @IsEnum({ ...DataSource, text: "text" })
    type: keyof typeof DataSource | "text";

    @ApiProperty({
        description: "Content of the input message",
        example: "Hello, world!",
    })
    @IsString()
    content: string;
}

export class OutputMessageDto {
    @IsString()
    id: string;

    @ApiProperty({
        enum: ["text"],
        description: "Type of the output message",
        example: "text",
    })
    @IsString()
    @IsIn(["text"])
    type: "text";

    @ApiProperty({
        description: "Content of the output message",
        example: "Hello yourself!",
    })
    @IsString()
    content: string;
}

export class ChatRequestDto {
    @ApiPropertyOptional({
        description:
            "ID of the previous response. Used to maintain conversation state.",
        example: null,
    })
    @IsString()
    @IsOptional()
    previousResponseId?: string;

    @ApiProperty({
        isArray: true,
        type: [InputMessageDto],
        description: "Array of input messages",
        example: [
            {
                type: "text",
                content: "What are some similar posts these?",
            },
            {
                type: "REDDIT_POST",
                content: "1kodtwv",
            },
            {
                type: "REDDIT_COMMENT",
                content: "msqobqj",
            },
            {
                type: "YOUTUBE_VIDEO",
                content: "gHC7uZBigmQ",
            },
            {
                type: "YOUTUBE_COMMENT",
                content: "Ugx2vD35pZHQDPBoARh4AaABAg",
            },
        ],
    })
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => InputMessageDto)
    messages: InputMessageDto[];
}

export class ChatResponseDto {
    @ApiProperty({
        isArray: true,
        type: [OutputMessageDto],
        description: "Array of output messages",
    })
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => OutputMessageDto)
    messages: OutputMessageDto[];
}
