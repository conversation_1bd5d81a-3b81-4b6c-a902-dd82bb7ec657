import { <PERSON>du<PERSON> } from "@nestjs/common";

import { InsightsModule } from "../insights/insights.module";
import { OpenaiModule } from "../openai/openai.module";
import { ChatController } from "./chat.controller";
import { ChatService } from "./chat.service";

@Module({
    imports: [InsightsModule, OpenaiModule],
    controllers: [ChatController],
    providers: [ChatService],
})
export class ChatModule {}
