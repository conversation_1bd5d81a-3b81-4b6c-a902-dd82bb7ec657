import { Injectable, Logger } from "@nestjs/common";

import OpenA<PERSON> from "openai";
import { ChatCompletionCreateParamsNonStreaming } from "openai/resources";
import { ResponseCreateParams } from "openai/resources/responses/responses";

import { OpenaiServiceInterface } from "./openai-service.interface";
import { EmbeddingCreateParamsWithStringInput } from "./types";
import { truncateEmbeddingInput } from "./util/embedding";

@Injectable()
export class OpenaiService implements OpenaiServiceInterface {
    client: OpenAI;
    logger = new Logger("OpenaiService");

    constructor() {
        this.client = new OpenAI({
            apiKey: process.env["OPENAI_API_KEY"],
        });
    }

    async getCompletion(body: ChatCompletionCreateParamsNonStreaming) {
        const environmentMetadata = { environment: process.env.APP_BASE_URL };
        const metadata = Object.assign({}, environmentMetadata, body.metadata);

        return this.safeRequest(() =>
            this.client.chat.completions.create({ ...body, metadata }),
        );
    }

    async getEmbedding(body: EmbeddingCreateParamsWithStringInput) {
        const truncatedInput = truncateEmbeddingInput(body.input as string);
        if (truncatedInput.length !== body.input.length) {
            this.logger.log(
                `Truncated input to ${body.input.length} characters (original: ${truncatedInput.length} characters)`,
            );
        }

        return this.safeRequest(() =>
            this.client.embeddings.create({
                ...body,
                input: truncatedInput,
            }),
        );
    }

    async createResponse(body: ResponseCreateParams) {
        return this.safeRequest(() => this.client.responses.parse(body));
    }

    private async safeRequest<T>(
        makeRequest: () => Promise<T>,
        initialDelay = 1,
        exponentialBase = 2,
        jitter = true,
        maxAttempts = 5,
        errors = [429],
    ): Promise<T> {
        let delay = initialDelay;
        let lastError: Error | null = null;

        for (let attempts = 1; attempts <= maxAttempts; attempts++) {
            try {
                const response = await makeRequest();
                return response;
            } catch (error) {
                lastError = error;
                if (errors.includes(error?.status)) {
                    const backoff =
                        initialDelay * Math.pow(exponentialBase, attempts);
                    const jitterFactor = jitter ? 1 + Math.random() : 1;
                    delay = Math.min(backoff * jitterFactor, 60);

                    this.logger.log(
                        `Handling rate limit error. Retrying in ${Math.round(delay * 1000)}ms`,
                    );
                    this.logger.log(error);
                    await new Promise((resolve) =>
                        setTimeout(resolve, delay * 1000),
                    );
                } else {
                    throw error;
                }
            }
        }

        throw lastError;
    }
}
