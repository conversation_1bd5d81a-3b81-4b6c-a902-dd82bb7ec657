import {
    Chat<PERSON><PERSON>ple<PERSON>,
    ChatCompletionCreateParamsNonStreaming,
    CreateEmbeddingResponse,
    EmbeddingCreateParams,
} from "openai/resources";
import {
    ParsedResponse,
    ResponseCreateParams,
} from "openai/resources/responses/responses";

export interface OpenaiServiceInterface {
    getCompletion(
        body: ChatCompletionCreateParamsNonStreaming,
    ): Promise<ChatCompletion>;

    getEmbedding(body: EmbeddingCreateParams): Promise<CreateEmbeddingResponse>;

    createResponse<T>(body: ResponseCreateParams): Promise<ParsedResponse<T>>;
}
