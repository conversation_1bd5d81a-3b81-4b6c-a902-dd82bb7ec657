import { encoding_for_model } from "tiktoken";

import { MAX_EMBEDDING_TOKENS, EMBEDDING_MODEL } from "../../openai/constants";

export function truncateEmbeddingInput(
    input: string,
    maxTokens = MAX_EMBEDDING_TOKENS,
    model = EMBEDDING_MODEL,
) {
    const encoder = encoding_for_model(model);
    const encoding = encoder.encode(input);

    const originalTokens = encoding.length;

    const truncatedInput =
        originalTokens > maxTokens
            ? new TextDecoder().decode(
                  encoder.decode(encoding.slice(0, maxTokens)),
              )
            : input;

    encoder.free();

    return truncatedInput;
}
