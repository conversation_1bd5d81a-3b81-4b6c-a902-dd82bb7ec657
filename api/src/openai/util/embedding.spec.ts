import { truncateEmbeddingInput } from "./embedding";

describe("truncateEmbeddingInput", () => {
    it("returns the input", () => {
        const input = "Hello world";
        const truncatedInput = truncateEmbeddingInput(input, 1_000);
        expect(truncatedInput).toEqual(input);
    });

    it("truncates the input if it is too long", () => {
        const input = "a".repeat(10_000);
        const truncatedInput = truncateEmbeddingInput(input, 1_000);
        expect(truncatedInput.length).toEqual(8_000);
    });
});
