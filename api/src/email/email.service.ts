import { Injectable, Logger } from "@nestjs/common";

import { SendEmailCommand, SESClient } from "@aws-sdk/client-ses";
import { htmlToText } from "html-to-text";

import { SOURCE } from "./constants";

@Injectable()
export class EmailService {
    private logger = new Logger("AppService");

    private sesClient: SESClient;

    constructor() {
        if (process.env.ENVIRONMENT === "test") {
            this.sesClient = new SESClient({
                region: "us-east-1",
                endpoint: process.env.SES_ENDPOINT || undefined,
                credentials: {
                    accessKeyId: "",
                    secretAccessKey: "",
                },
            });
        } else {
            this.sesClient = new SESClient({
                region: process.env.AWS_REGION || "us-east-2",
            });
        }
    }

    async sendEmail(
        toAddress: string,
        subject: string,
        content: string,
        bccAddresses: string[] = [],
    ) {
        const command = new SendEmailCommand({
            Destination: {
                ToAddresses: [toAddress],
                BccAddresses: bccAddresses,
            },
            Source: SOURCE,
            Message: {
                Body: {
                    Text: {
                        Charset: "UTF-8",
                        Data: content,
                    },
                },
                Subject: {
                    Charset: "UTF-8",
                    Data: subject,
                },
            },
        });
        await this.executeEmailCommand(command);
    }

    async sendHtmlEmail(
        toAddress: string,
        subject: string,
        htmlContent: string,
        bccAddresses: string[] = [],
    ) {
        const plaintTextContent = htmlToText(htmlContent);

        const command = new SendEmailCommand({
            Destination: {
                ToAddresses: [toAddress],
                BccAddresses: bccAddresses,
            },
            Source: SOURCE,
            Message: {
                Body: {
                    Html: {
                        Charset: "UTF-8",
                        Data: htmlContent,
                    },
                    Text: {
                        Charset: "UTF-8",
                        Data: plaintTextContent,
                    },
                },
                Subject: {
                    Charset: "UTF-8",
                    Data: subject,
                },
            },
        });
        await this.executeEmailCommand(command);
    }

    private async executeEmailCommand(emailCommand: SendEmailCommand) {
        try {
            const response = await this.sesClient.send(emailCommand);
            this.logger.log("✅ Email sent:", response.MessageId);
        } catch (err) {
            this.logger.error("❌ SES email send error:", err);
        }
    }
}
