import {
    BadRequestException,
    INestApplication,
    ValidationError,
    ValidationPipe,
} from "@nestjs/common";
import { HttpAdapterHost } from "@nestjs/core";
import { DocumentBuilder, SwaggerModule } from "@nestjs/swagger";

import * as bodyParser from "body-parser";
import * as cookieParser from "cookie-parser";
import { PrismaClientExceptionFilter } from "nestjs-prisma";

export async function setupApp(app: INestApplication) {
    app.setGlobalPrefix("api");

    // Swagger
    const config = new DocumentBuilder().setTitle("Truthkeep API").build();
    const documentFactory = () => SwaggerModule.createDocument(app, config);
    SwaggerModule.setup("api", app, documentFactory, {
        swaggerOptions: {
            tagsSorter: "alpha",
            docExpansion: "none",
        },
    });
    app.use(cookieParser());

    // Validation
    app.useGlobalPipes(
        new ValidationPipe({
            transform: true,
            forbidUnknownValues: true,
            whitelist: true,
            forbidNonWhitelisted: true,
            exceptionFactory: (validationErrors: ValidationError[] = []) => {
                return new BadRequestException(
                    validationErrors.map((error) => ({
                        field: error.property,
                        error: Object.values(error.constraints).join(", "),
                    })),
                );
            },
        }),
    );

    // Prisma error handling
    const { httpAdapter } = app.get(HttpAdapterHost);
    app.useGlobalFilters(new PrismaClientExceptionFilter(httpAdapter));

    // Endpoints that accept text in request body
    app.use(bodyParser.text({ type: "text/plain" }));

    await app.listen(process.env.PORT ?? 3000);
}
