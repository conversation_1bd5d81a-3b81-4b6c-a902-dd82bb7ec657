import Redis, { Cluster } from "ioredis";

export function getRedis(): Redis | Cluster {
    return process.env.ENVIRONMENT === "test"
        ? getRedisClient()
        : getRedisCluster();
}

function getRedisClient(): Redis {
    return new Redis({
        host: process.env.REDIS_HOST,
        port: process.env.REDIS_PORT,
    });
}

function getRedisCluster(): Cluster {
    return new Redis.Cluster(
        [
            {
                host: process.env.REDIS_HOST,
                port: process.env.REDIS_PORT,
            },
        ],
        {
            dnsLookup: (address, callback) => callback(null, address),
            redisOptions: {
                tls: {},
            },
        },
    );
}
