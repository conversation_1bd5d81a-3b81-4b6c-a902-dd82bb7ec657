import { ApiPropertyOptional } from "@nestjs/swagger";

import { Transform } from "class-transformer";
import { IsArray, IsBoolean, IsOptional } from "class-validator";

export type TopicFilteredParams = {
    topicIds?: number[];
};

export type GroupFilteredParams = {
    groupIds?: number[];
};

export type TagFilteredParams = {
    tagIds?: number[];
};

export type MyTeamsFilteredParams = {
    myTeamsOnly?: boolean;
};

export class TopicFilteredRequestDto implements TopicFilteredParams {
    @IsArray()
    @IsOptional()
    @ApiPropertyOptional({ isArray: true, type: Number })
    @Transform(({ value }) =>
        Array.isArray(value) ? value.map(Number) : [Number(value)],
    )
    topicIds?: number[];
}

export class GroupFilteredRequestDto implements GroupFilteredParams {
    @IsArray()
    @IsOptional()
    @ApiPropertyOptional({ isArray: true, type: Number })
    @Transform(({ value }) =>
        Array.isArray(value) ? value.map(Number) : [Number(value)],
    )
    groupIds?: number[];
}

export class TagFilteredRequestDto implements TagFilteredParams {
    @IsArray()
    @IsOptional()
    @ApiPropertyOptional({ isArray: true, type: Number })
    @Transform(({ value }) =>
        Array.isArray(value) ? value.map(Number) : [Number(value)],
    )
    tagIds?: number[];
}

export class MyTeamsFilteredRequestDto implements MyTeamsFilteredParams {
    @IsOptional()
    @IsBoolean()
    @Transform(({ value }) => value === "true" || value === true)
    @ApiPropertyOptional({ default: false })
    myTeamsOnly?: boolean;
}
