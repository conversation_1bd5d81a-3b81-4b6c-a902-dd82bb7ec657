import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";

import { NotificationCadence } from "@prisma/client";
import { Type } from "class-transformer";
import {
    IsArray,
    IsBoolean,
    IsEnum,
    IsNumber,
    IsObject,
    IsOptional,
    ValidateNested,
} from "class-validator";

export class PreferenceDto {
    @IsBoolean()
    @IsOptional()
    @ApiPropertyOptional({ example: false })
    isFavourite?: boolean;

    @IsEnum(NotificationCadence)
    @IsOptional()
    @ApiPropertyOptional({ example: "OFF" })
    notificationCadence?: NotificationCadence;

    @IsEnum(NotificationCadence)
    @IsOptional()
    @ApiPropertyOptional({ example: "OFF" })
    highRelevanceNotifications?: NotificationCadence;

    @IsEnum(NotificationCadence)
    @IsOptional()
    @ApiPropertyOptional({ example: "OFF" })
    negativeSentimentNotifications?: NotificationCadence;
}

class PatchPreferenceRequestItemDto {
    @Type(() => Number)
    @IsNumber()
    id: number;

    @IsObject()
    @IsOptional()
    @Type(() => PreferenceDto)
    patch: PreferenceDto;
}

export class BulkPatchPreferenceRequestDto {
    @ApiProperty({
        type: () => PatchPreferenceRequestItemDto,
        isArray: true,
    })
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => PatchPreferenceRequestItemDto)
    items: PatchPreferenceRequestItemDto[];
}
