import { ConsoleLogger } from "@nestjs/common";

import { Request, Response, NextFunction } from "express";

export function requestLoggerMiddleware(logger: ConsoleLogger) {
    return (req: Request, res: Response, next: NextFunction) => {
        const startTime = Date.now();
        //filter out healthcheck logs, creating noisy logs and eating costs
        res.on("finish", () => {
            const duration = Date.now() - startTime;
            logger.log(
                {
                    method: req.method,
                    url: req.originalUrl,
                    statusCode: res.statusCode,
                    duration: `${duration}ms`,
                    userAgent: req.get("user-agent") || "unknown",
                    ip: req.ip,
                },
                "HTTP Request",
            );
        });

        next();
    };
}
