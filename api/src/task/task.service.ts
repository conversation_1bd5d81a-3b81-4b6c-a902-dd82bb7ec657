import { InjectQueue } from "@nestjs/bullmq";
import { Injectable } from "@nestjs/common";

import { ClsService } from "nestjs-cls";

import { SalesforceSiteName } from "../salesforce/types";
import { TIMELINE_REFRESH_QUEUE } from "./constants";
import {
    SalesforceForumJobParams,
    TimelineRefreshTaskRequestDto,
} from "./dto/timeline-refresh-task.dto";
import { SourceService } from "./source.service";
import { TimelineRefreshQueue } from "./types";
import { getFlooredDate } from "./util";

@Injectable()
export class TaskService {
    constructor(
        private readonly sourceService: SourceService,
        private readonly cls: ClsService,
        @InjectQueue(TIMELINE_REFRESH_QUEUE)
        private timelineUpdateTaskQueue: TimelineRefreshQueue,
    ) {}

    async createTimelineUpdateTask(
        workflowKeyOverride?: string,
        task?: TimelineRefreshTaskRequestDto,
    ) {
        const tenantId = this.cls.get("tenantId");

        // With defaultWorkflowKey new tasks are created at most once per hour
        // (cron job triggers this method in all instances of the application)
        const defaultWorkflowKey = getFlooredDate().toUTCString();
        const workflowKey = workflowKeyOverride ?? defaultWorkflowKey;
        const jobId = `timeline-update:${tenantId} ${workflowKey}`;

        const { items: channels } =
            await this.sourceService.listYoutubeChannels();
        const { items: subreddits } = await this.sourceService.listSubreddits();
        const { items: salesforceSites } =
            await this.sourceService.getSalesforceSites();

        await this.timelineUpdateTaskQueue.add(
            `Timeline refresh ${workflowKey}`,
            {
                channelHandles: channels.map(
                    ({ channelHandle }) => channelHandle,
                ),
                subredditNames: subreddits.map(({ name }) => name),
                salesforceForums: salesforceSites.map(({ name, forums }) => ({
                    siteName: name,
                    forumNames: forums.flatMap((forum) =>
                        forum.subForums.map(({ name }) => name),
                    ),
                })),
                salesforceForumJobParams: {
                    ...this.getDefaultSalesforceForumJobParams(),
                    ...task?.salesforceForumJobParams,
                    tenantId,
                },
                tenantId,
                workflowKey,
            },
            { jobId },
        );
    }

    async createYoutubeTask(channelHandles: string[]) {
        await this.timelineUpdateTaskQueue.add(
            "Timeline - one off youtube update",
            {
                channelHandles,
                tenantId: this.cls.get("tenantId"),
            },
        );
    }

    async createRedditTask(subredditNames: string[]): Promise<void> {
        await this.timelineUpdateTaskQueue.add(
            "Timeline - one off reddit update",
            {
                subredditNames,
                tenantId: this.cls.get("tenantId"),
            },
        );
    }

    async createSalesforceTask(
        siteName: SalesforceSiteName,
        forumNames: string[],
        limit?: number,
    ) {
        await this.timelineUpdateTaskQueue.add(
            "Timeline - one off salesforce update",
            {
                salesforceForums: [
                    {
                        siteName,
                        forumNames,
                    },
                ],
                salesforceForumJobParams: {
                    limit,
                    tenantId: this.cls.get("tenantId"),
                },
                tenantId: this.cls.get("tenantId"),
            },
        );
    }

    private getDefaultSalesforceForumJobParams(): Partial<SalesforceForumJobParams> {
        // By default scrape posts that are exactly 7 days old
        const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
            .toISOString()
            .slice(0, 10);

        return {
            dateAfter: sevenDaysAgo,
            dateBefore: sevenDaysAgo,
        };
    }
}
