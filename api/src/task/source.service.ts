import {
    BadGatewayException,
    BadRequestException,
    Injectable,
} from "@nestjs/common";

import { ClsService } from "nestjs-cls";

import { PrismaService } from "../prisma/prisma.service";
import { SalesforceService } from "../salesforce/salesforce.service";
import { SalesforceSiteName } from "../salesforce/types";
import { YoutubeApiService } from "../youtube-api/youtube-api.service";
import { RedditService } from "./../reddit/reddit.service";
import {
    CreateForumRequestDto,
    CreateSalesforceSourceRequestDto,
    CreateSubforumRequestDto,
} from "./dto/salesforce-source.dto";

@Injectable()
export class SourceService {
    constructor(
        private readonly prisma: PrismaService,
        private readonly cls: ClsService,
        private readonly redditService: RedditService,
        private readonly salesforceService: SalesforceService,
        private readonly youtubeApiService: YoutubeApiService,
    ) {}

    async addYoutubeChannel(channelHandle: string) {
        const channelsResponse =
            await this.youtubeApiService.searchChannels(channelHandle);

        const [channel] = channelsResponse.data.items || [];

        if (!channel)
            throw new BadRequestException(
                `No channel matching "${channelHandle}"`,
            );

        const uploadsPlaylistId =
            channel.contentDetails.relatedPlaylists.uploads;

        return this.prisma.youtubeChannel.create({
            data: {
                id: channel.id,
                channelHandle,
                uploadsPlaylistId,
            },
        });
    }

    async addSubreddit(name: string) {
        if (name.includes(" "))
            throw new BadRequestException(
                "Subreddit name cannot contain spaces",
            );

        const [isExistingSubreddit]: boolean[] = await this.prisma
            .$queryRawWithTenant`
            SELECT 1
            FROM "Subreddit"
            WHERE LOWER(name) = LOWER(${name})
        `;
        if (isExistingSubreddit)
            throw new BadRequestException("Subreddit already exists");

        try {
            await this.redditService.getPosts(name, "top", "all", 1);
        } catch {
            throw new BadGatewayException(
                `Unable to verify that subreddit "${name}" exists`,
            );
        }

        await this.prisma.subreddit.create({
            data: {
                name,
            },
        });
    }

    async addSalesforceSite(siteRequestDto: CreateSalesforceSourceRequestDto) {
        const { siteName } = siteRequestDto;
        const salesforceSite = await this.prisma.salesforceSite.create({
            data: {
                name: siteName,
            },
        });
        return salesforceSite;
    }

    async *pullSalesforceForums(siteName: SalesforceSiteName) {
        // Get existing site record
        const salesforceSite =
            await this.prisma.salesforceSite.findUniqueOrThrow({
                where: {
                    tenantId_name: {
                        tenantId: this.cls.get("tenantId"),
                        name: siteName,
                    },
                },
            });

        // Fetch top-level forums
        const forums = await this.salesforceService
            .getTopLevelForums(siteName)
            .then(({ records }) =>
                // Double check no sub-forums were returned
                records.filter(({ forumLevel }) => forumLevel === "Forum"),
            );

        // Persist top-level forums
        for (const forum of forums) {
            yield await this.prisma.salesforceForum.upsert({
                where: {
                    salesforceSiteId_name: {
                        salesforceSiteId: salesforceSite.id,
                        name: forum.forumName,
                    },
                },
                update: {},
                create: {
                    name: forum.forumName,
                    site: {
                        connect: {
                            id: salesforceSite.id,
                        },
                    },
                },
            });
        }
    }

    async pullSalesforceForumsAndSubforums(siteName: SalesforceSiteName) {
        for await (const forum of this.pullSalesforceForums(siteName)) {
            await this.pullSalesforceSubforums(siteName, forum.id);
        }
    }

    async addSalesforceTopLevelForum(
        siteName: SalesforceSiteName,
        createForumRequestDto: CreateForumRequestDto,
    ) {
        return this.addSalesforceForum(
            siteName,
            createForumRequestDto.forumName,
        );
    }

    async addSalesforceSubforum(
        siteName: SalesforceSiteName,
        parentForumId: number,
        createSubforumRequestDto: CreateSubforumRequestDto,
    ) {
        return this.addSalesforceForum(
            siteName,
            createSubforumRequestDto.subforumName,
            parentForumId,
        );
    }

    private async addSalesforceForum(
        siteName: SalesforceSiteName,
        name: string,
        parentForumId?: number,
    ) {
        return this.prisma.salesforceForum.create({
            data: {
                name,
                site: {
                    connect: {
                        tenantId_name: {
                            tenantId: this.cls.get("tenantId"),
                            name: siteName,
                        },
                    },
                },
                parentForum: parentForumId && {
                    connect: {
                        id: parentForumId,
                    },
                },
            },
        });
    }

    async pullSalesforceSubforums(
        siteName: SalesforceSiteName,
        parentForumId: number,
    ) {
        // Get existing site record
        const salesforceSite =
            await this.prisma.salesforceSite.findUniqueOrThrow({
                where: {
                    tenantId_name: {
                        tenantId: this.cls.get("tenantId"),
                        name: siteName,
                    },
                },
            });

        // Get existing parent forum record
        const parentForum = await this.prisma.salesforceForum.findUniqueOrThrow(
            {
                where: {
                    salesforceSiteId: salesforceSite.id,
                    id: parentForumId,
                },
            },
        );

        // Fetch subforums
        const subforums = await this.salesforceService
            .getSubForums(siteName)
            .then(({ records }) =>
                records.filter(
                    ({ forumParentName }) =>
                        forumParentName === parentForum.name,
                ),
            );

        // Persist subforums
        for (const subforum of subforums) {
            await this.prisma.salesforceForum.upsert({
                where: {
                    salesforceSiteId_name: {
                        salesforceSiteId: salesforceSite.id,
                        name: subforum.forumName,
                    },
                },
                update: {},
                create: {
                    name: subforum.forumName,
                    site: {
                        connect: {
                            id: salesforceSite.id,
                        },
                    },
                    parentForum: {
                        connect: {
                            id: parentForumId,
                        },
                    },
                },
            });
        }
    }

    async listYoutubeChannels(take?: number, skip?: number) {
        const items = await this.prisma.youtubeChannel.findMany({
            orderBy: {
                createdAt: "desc",
            },
            take,
            skip,
        });

        const total = await this.prisma.youtubeChannel.count();

        return { items, total };
    }

    async listSubreddits(take?: number, skip?: number) {
        const items = await this.prisma.subreddit.findMany({
            orderBy: {
                createdAt: "desc",
            },
            take,
            skip,
        });

        const total = await this.prisma.subreddit.count();

        return { items, total };
    }

    async getSalesforceSites(take?: number) {
        const sites = await this.prisma.salesforceSite.findMany({
            select: {
                id: true,
                name: true,
                forums: {
                    where: {
                        parentForumId: {
                            equals: null,
                        },
                    },
                    select: {
                        id: true,
                        name: true,
                        subForums: {
                            select: {
                                id: true,
                                name: true,
                            },
                            take,
                        },
                    },
                    take,
                },
            },
            take,
        });

        const items = sites.map(({ name, ...site }) => ({
            name: name as SalesforceSiteName,
            ...site,
        }));

        const total = await this.prisma.salesforceSite.count();

        return { items, total };
    }

    async getSalesforceForums(
        siteName: SalesforceSiteName,
        take?: number,
        skip?: number,
    ) {
        const items = await this.prisma.salesforceForum.findMany({
            where: {
                parentForumId: {
                    equals: null,
                },
                site: {
                    name: siteName,
                },
            },
            take,
            skip,
        });

        const total = await this.prisma.salesforceForum.count({
            where: { site: { name: siteName } },
        });

        return {
            items,
            total,
        };
    }

    async getSalesforceSubforums(
        siteName: SalesforceSiteName,
        forumId: number,
        take?: number,
        skip?: number,
    ) {
        const items = await this.prisma.salesforceForum.findMany({
            where: {
                parentForumId: {
                    equals: forumId,
                },
                site: {
                    name: siteName,
                },
            },
            take,
            skip,
        });

        const total = await this.prisma.salesforceForum.count({
            where: { site: { name: siteName } },
        });

        return {
            items,
            total,
        };
    }

    async deleteYoutubeChannel(id: string) {
        await this.prisma.youtubeChannel.delete({
            where: {
                tenantId_id: {
                    id,
                    tenantId: this.cls.get("tenantId"),
                },
            },
        });
    }

    async deleteSubreddit(name: string) {
        await this.prisma.subreddit.delete({
            where: {
                tenantId_name: {
                    tenantId: this.cls.get("tenantId"),
                    name,
                },
            },
        });
    }

    async deleteSalesforceSite(siteName: string) {
        await this.prisma.salesforceSite.delete({
            where: {
                tenantId_name: {
                    tenantId: this.cls.get("tenantId"),
                    name: siteName,
                },
            },
        });
    }

    async deleteSalesforceForum(siteName: string, forumId: number) {
        const site = await this.prisma.salesforceSite.findUniqueOrThrow({
            where: {
                tenantId_name: {
                    tenantId: this.cls.get("tenantId"),
                    name: siteName,
                },
            },
        });

        await this.prisma.salesforceForum.delete({
            where: {
                id: forumId,
                parentForumId: {
                    equals: null,
                },
                salesforceSiteId: site.id,
            },
        });
    }

    async deleteSalesforceSubforum(
        siteName: string,
        forumId: number,
        subforumId: number,
    ) {
        const site = await this.prisma.salesforceSite.findUniqueOrThrow({
            where: {
                tenantId_name: {
                    tenantId: this.cls.get("tenantId"),
                    name: siteName,
                },
            },
        });

        await this.prisma.salesforceForum.delete({
            where: {
                id: subforumId,
                parentForumId: forumId,
                salesforceSiteId: site.id,
            },
        });
    }
}
