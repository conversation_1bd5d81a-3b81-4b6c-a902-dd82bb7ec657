import { Body, Controller, Headers, <PERSON><PERSON>, <PERSON><PERSON>, Post } from "@nestjs/common";
import { <PERSON>ron, CronExpression } from "@nestjs/schedule";
import { Api<PERSON>eader, ApiParam, ApiResponse, ApiTags } from "@nestjs/swagger";

import { Role } from "../roles/role.enum";
import { Roles } from "../roles/roles.decorator";
import { SalesforceSiteName } from "../salesforce/types";
import { TenantsService } from "../tenants/tenants.service";
import { RedditTaskRequestDto } from "./dto/reddit-task.dto";
import { SalesforceTaskRequestDto } from "./dto/salesforce-task.dto";
import { TimelineRefreshTaskRequestDto } from "./dto/timeline-refresh-task.dto";
import { YoutubeTaskRequestDto } from "./dto/youtube-task.dto";
import { TaskService } from "./task.service";

@Controller("task")
@Roles(Role.Admin)
@ApiTags("background jobs - tasks")
export class TaskController {
    logger = new Logger("TaskController");
    constructor(
        private readonly taskService: TaskService,
        private readonly tenantService: TenantsService,
    ) {}

    @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT, { timeZone: "America/Phoenix" })
    async timelineRefreshCron(): Promise<void> {
        this.logger.log("timelineRefreshCron");

        await this.tenantService.runForAllTenants(() =>
            this.taskService.createTimelineUpdateTask(),
        );
    }

    @Post("timeline-refresh")
    @ApiResponse({ status: 201 })
    @ApiHeader({
        name: "x-workflow-key",
        required: false,
        description:
            "Set the workflow key directly if you need to run this more than once per hour",
    })
    async timelineRefreshPost(
        @Headers("x-workflow-key") workflowKeyOverride: string,
        @Body() task: TimelineRefreshTaskRequestDto,
    ): Promise<void> {
        await this.taskService.createTimelineUpdateTask(
            workflowKeyOverride,
            task,
        );
    }

    @Post("youtube")
    @ApiResponse({ status: 201 })
    async createYoutubeTask(
        @Body() task: YoutubeTaskRequestDto,
    ): Promise<void> {
        await this.taskService.createYoutubeTask(task.channelHandles);
    }

    @Post("reddit")
    @ApiResponse({ status: 201 })
    async createRedditTask(@Body() task: RedditTaskRequestDto): Promise<void> {
        await this.taskService.createRedditTask(task.subredditNames);
    }

    @Post("salesforce/:siteName")
    @ApiParam({
        name: "siteName",
        enum: SalesforceSiteName,
        examples: {
            "AVR Freaks": {
                value: "AVR_FREAKS",
            },
            Microchip: {
                value: "MICROCHIP_CLASSIC",
            },
        },
    })
    @ApiResponse({ status: 201 })
    async createSalesforceTask(
        @Param("siteName") siteName: SalesforceSiteName,
        @Body() task: SalesforceTaskRequestDto,
    ): Promise<void> {
        await this.taskService.createSalesforceTask(
            siteName,
            task.forumNames,
            task.limit,
        );
    }
}
