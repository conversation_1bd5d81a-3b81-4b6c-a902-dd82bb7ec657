import { BullModule } from "@nestjs/bullmq";
import { Module } from "@nestjs/common";
import { ScheduleModule } from "@nestjs/schedule";

import { BullMQAdapter } from "@bull-board/api/bullMQAdapter";
import { BullBoardModule } from "@bull-board/nestjs";

import { CacheModule } from "../cache/cache.module";
import { RedditModule } from "../reddit/reddit.module";
import { SalesforceModule } from "../salesforce/salesforce.module";
import { TenantsModule } from "../tenants/tenants.module";
import { YoutubeApiModule } from "../youtube-api/youtube-api.module";
import {
    NARRATIVE_GENERATION_QUEUE,
    REDDIT_REFRESH_QUEUE,
    REDDIT_POST_REFRESH_QUEUE,
    REDDIT_SUBREDDIT_QUEUE,
    TIMELINE_REFRESH_QUEUE,
    YOUTUBE_CHANNEL_QUEUE,
    YOUTUBE_REFRESH_QUEUE,
    SALESFORCE_FORUM_QUEUE,
    SALESFORCE_REFRESH_QUEUE,
    AAC_FORUM_QUEUE,
} from "./constants";
import { SourceController } from "./source.controller";
import { SourceService } from "./source.service";
import { TaskController } from "./task.controller";
import { TimelineUpdateProcessor } from "./task.processor";
import { TaskService } from "./task.service";

@Module({
    imports: [
        BullBoardModule.forFeature({
            name: TIMELINE_REFRESH_QUEUE,
            adapter: BullMQAdapter,
        }),
        BullModule.registerQueue(
            { name: TIMELINE_REFRESH_QUEUE },
            { name: YOUTUBE_CHANNEL_QUEUE },
            { name: YOUTUBE_REFRESH_QUEUE },
            { name: REDDIT_SUBREDDIT_QUEUE },
            { name: REDDIT_REFRESH_QUEUE },
            { name: REDDIT_POST_REFRESH_QUEUE },
            { name: SALESFORCE_FORUM_QUEUE },
            { name: SALESFORCE_REFRESH_QUEUE },
            { name: AAC_FORUM_QUEUE },
            { name: NARRATIVE_GENERATION_QUEUE },
        ),
        CacheModule,
        RedditModule,
        SalesforceModule,
        ScheduleModule.forRoot(),
        TenantsModule,
        YoutubeApiModule,
    ],
    controllers: [SourceController, TaskController],
    providers: [SourceService, TaskService, TimelineUpdateProcessor],
})
export class TaskModule {}
