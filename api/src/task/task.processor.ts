import { InjectQueue, Processor, WorkerHost } from "@nestjs/bullmq";

import {
    NARRATIVE_GENERATION_QUEUE,
    REDDIT_REFRESH_QUEUE,
    REDDIT_SUBREDDIT_QUEUE,
    SALESFORCE_FORUM_QUEUE,
    TIMELINE_REFRESH_QUEUE,
    YOUTUBE_CHANNEL_QUEUE,
    YOUTUBE_REFRESH_QUEUE,
    SALESFORCE_REFRESH_QUEUE,
    AAC_FORUM_QUEUE,
} from "./constants";
import {
    AllAboutCircuitsQueue,
    NarrativeGenerationQueue,
    RedditRefreshQueue,
    RedditSubredditQueue,
    SalesforceForumQueue,
    SalesforceRefreshQueue,
    TimelineRefreshJob,
    YoutubeChannelQueue,
    YoutubeRefreshQueue,
} from "./types";

@Processor(TIMELINE_REFRESH_QUEUE)
export class TimelineUpdateProcessor extends WorkerHost {
    constructor(
        @InjectQueue(YOUTUBE_CHANNEL_QUEUE)
        private readonly youtubeChannelQueue: YoutubeChannelQueue,
        @InjectQueue(YOUTUBE_REFRESH_QUEUE)
        private readonly youtubeRefreshQueue: YoutubeRefreshQueue,
        @InjectQueue(REDDIT_SUBREDDIT_QUEUE)
        private readonly redditSubredditQueue: RedditSubredditQueue,
        @InjectQueue(REDDIT_REFRESH_QUEUE)
        private readonly redditRefreshQueue: RedditRefreshQueue,
        @InjectQueue(SALESFORCE_FORUM_QUEUE)
        private readonly salesforceForumQueue: SalesforceForumQueue,
        @InjectQueue(SALESFORCE_REFRESH_QUEUE)
        private readonly salesforceRefreshQueue: SalesforceRefreshQueue,
        @InjectQueue(AAC_FORUM_QUEUE)
        private readonly aacForumQueue: AllAboutCircuitsQueue,
        @InjectQueue(NARRATIVE_GENERATION_QUEUE)
        private readonly narrativeGenerationQueue: NarrativeGenerationQueue,
    ) {
        super();
    }

    async process(job: TimelineRefreshJob) {
        const {
            workflowKey,
            channelHandles = [],
            subredditNames = [],
            salesforceForums = [],
            subredditJobParams,
            youtubeChannelJobParams,
            salesforceForumJobParams,
            allAboutCircuitsJobParams,
        } = job.data;

        await Promise.all([
            ...channelHandles.map(async (channelHandle) => {
                await this.youtubeChannelQueue.add(
                    `${channelHandle} - ${workflowKey}`,
                    {
                        channelHandle,
                        ...(youtubeChannelJobParams || {}),
                        tenantId: job.data.tenantId,
                    },
                );
            }),
            ...subredditNames.map(async (subredditName) => {
                await this.redditSubredditQueue.add(
                    `${subredditName} - ${workflowKey}`,
                    {
                        subredditName,
                        sort: "new",
                        limit: 1000,
                        ...(subredditJobParams || {}),
                        tenantId: job.data.tenantId,
                    },
                );
            }),
            ...salesforceForums.map(async ({ siteName, forumNames }) => {
                forumNames.map(async (forumName) => {
                    await this.salesforceForumQueue.add(
                        `${forumName} - ${workflowKey}`,
                        {
                            siteName,
                            forumName,
                            ...(salesforceForumJobParams || {}),
                            tenantId: job.data.tenantId,
                        },
                    );
                });
            }),
            this.aacForumQueue.add(`All About Circuits - ${workflowKey}`, {
                ...(allAboutCircuitsJobParams || {}),
                tenantId: job.data.tenantId,
            }),
            this.youtubeRefreshQueue.add(
                `Refresh video statistics - ${workflowKey}`,
                { tenantId: job.data.tenantId },
            ),
            this.redditRefreshQueue.add(
                `Refresh post statistics - ${workflowKey}`,
                { tenantId: job.data.tenantId },
            ),
            this.salesforceRefreshQueue.add(
                `Refresh post statistics - ${workflowKey}`,
                { tenantId: job.data.tenantId },
            ),
            this.narrativeGenerationQueue.add(
                `Generate narratives - ${workflowKey}`,
                { tenantId: job.data.tenantId },
            ),
        ]);
    }
}
