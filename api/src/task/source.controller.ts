import {
    Body,
    Controller,
    Delete,
    Get,
    Param,
    ParseEnumPipe,
    Post,
    Query,
    Res,
} from "@nestjs/common";
import {
    ApiNoContentResponse,
    ApiOkResponse,
    ApiOperation,
    ApiParam,
    ApiResponse,
    ApiTags,
} from "@nestjs/swagger";

import { SalesforceForum, SalesforceSite } from "@prisma/client";
import { Response } from "express";

import {
    PaginatedRequestDto,
    PaginatedResponseDto,
} from "../generic/dto/paginated.dto";
import { paginated } from "../generic/util";
import { Role } from "../roles/role.enum";
import { Roles } from "../roles/roles.decorator";
import { SalesforceSiteName } from "../salesforce/types";
import {
    CreateRedditSourceRequestDto,
    RedditSourceDto,
} from "./dto/reddit-source.dto";
import {
    CreateForumRequestDto,
    CreateSalesforceSourceRequestDto,
    CreateSubforumRequestDto,
    SalesforceForumDto,
    SalesforceSiteDto,
} from "./dto/salesforce-source.dto";
import {
    CreateYoutubeSourceRequestDto,
    YoutubeSourceDto,
} from "./dto/youtube-source.dto";
import { CreateYoutubeSourceResponseDto } from "./dto/youtube-source.dto";
import { SourceService } from "./source.service";

@Controller("sources")
@ApiTags("background jobs - data sources")
export class SourceController {
    constructor(private readonly sourceService: SourceService) {}

    @Post("youtube")
    @Roles(Role.Admin)
    @ApiResponse({ status: 201 })
    async addYoutubeChannel(
        @Body() createYoutubeSourceRequestDto: CreateYoutubeSourceRequestDto,
    ): Promise<CreateYoutubeSourceResponseDto> {
        return this.sourceService.addYoutubeChannel(
            createYoutubeSourceRequestDto.channelHandle,
        );
    }

    @Post("youtube/bulk")
    @Roles(Role.Admin)
    @ApiResponse({ status: 201 })
    async addYoutubeChannels(@Body() channelHandles: string[]): Promise<void> {
        for (const channelHandle of channelHandles) {
            await this.sourceService.addYoutubeChannel(channelHandle);
        }
    }

    @Post("reddit")
    @Roles(Role.Admin)
    @ApiResponse({ status: 201 })
    async addSubreddit(
        @Body() createRedditSourceRequestDto: CreateRedditSourceRequestDto,
    ): Promise<void> {
        await this.sourceService.addSubreddit(
            createRedditSourceRequestDto.name,
        );
    }

    @Post("reddit/bulk")
    @Roles(Role.Admin)
    @ApiResponse({ status: 201 })
    async addSubreddits(@Body() subredditNames: string[]): Promise<void> {
        for (const subredditName of subredditNames) {
            await this.sourceService.addSubreddit(subredditName);
        }
    }

    @Post("salesforce/sites")
    @Roles(Role.Admin)
    @ApiResponse({ status: 201 })
    async addSalesforceSite(
        @Body()
        createSalesforceSourceRequestDto: CreateSalesforceSourceRequestDto,
    ): Promise<SalesforceSite> {
        return this.sourceService.addSalesforceSite(
            createSalesforceSourceRequestDto,
        );
    }

    @Post("salesforce/sites/:siteName/pull")
    @ApiOperation({
        summary: "Pull all forums and subforums for the specified site",
    })
    @Roles(Role.Admin)
    @ApiParam({
        name: "siteName",
        enum: SalesforceSiteName,
        required: true,
        examples: {
            "AVR Freaks": {
                value: SalesforceSiteName.AVR_FREAKS,
            },
            "Microchip Classic": {
                value: SalesforceSiteName.MICROCHIP_CLASSIC,
            },
        },
    })
    @ApiResponse({ status: 201 })
    async addAllSalesforceSiteForums(
        @Param("siteName", new ParseEnumPipe(SalesforceSiteName))
        siteName: SalesforceSiteName,
    ): Promise<void> {
        await this.sourceService.pullSalesforceForumsAndSubforums(siteName);
    }

    @Post("salesforce/sites/:siteName/forums")
    @Roles(Role.Admin)
    @ApiParam({
        name: "siteName",
        enum: SalesforceSiteName,
        required: true,
        examples: {
            "AVR Freaks": {
                value: SalesforceSiteName.AVR_FREAKS,
            },
            "Microchip Classic": {
                value: SalesforceSiteName.MICROCHIP_CLASSIC,
            },
        },
    })
    @ApiResponse({ status: 201 })
    async adSalesforceForum(
        @Param("siteName", new ParseEnumPipe(SalesforceSiteName))
        siteName: SalesforceSiteName,
        @Body()
        createForumRequestDto: CreateForumRequestDto,
    ): Promise<SalesforceForum> {
        return this.sourceService.addSalesforceTopLevelForum(
            siteName,
            createForumRequestDto,
        );
    }

    @Post("salesforce/sites/:siteName/forums/:forumId/subforums")
    @Roles(Role.Admin)
    @ApiParam({
        name: "siteName",
        enum: SalesforceSiteName,
        required: true,
        examples: {
            "AVR Freaks": {
                value: SalesforceSiteName.AVR_FREAKS,
            },
            "Microchip Classic": {
                value: SalesforceSiteName.MICROCHIP_CLASSIC,
            },
        },
    })
    @ApiResponse({ status: 201 })
    async adSalesforceSubforum(
        @Param("siteName", new ParseEnumPipe(SalesforceSiteName))
        siteName: SalesforceSiteName,
        @Param("forumId") forumId: string,
        @Body()
        createSubforumRequestDto: CreateSubforumRequestDto,
    ): Promise<SalesforceForum> {
        return this.sourceService.addSalesforceSubforum(
            siteName,
            Number(forumId),
            createSubforumRequestDto,
        );
    }

    @Get("youtube")
    @ApiOkResponse()
    async listYoutubeChannels(
        @Query() params: PaginatedRequestDto,
    ): Promise<PaginatedResponseDto<YoutubeSourceDto>> {
        return paginated(
            (...args) => this.sourceService.listYoutubeChannels(...args),
            params,
        );
    }

    @Get("reddit")
    @ApiOkResponse()
    async listSubreddits(
        @Query() params: PaginatedRequestDto,
    ): Promise<PaginatedResponseDto<RedditSourceDto>> {
        return paginated(
            (...args) => this.sourceService.listSubreddits(...args),
            params,
        );
    }

    @Get("salesforce/sites")
    @ApiOkResponse()
    async listSalesforceSites(
        @Query() params: PaginatedRequestDto,
    ): Promise<PaginatedResponseDto<SalesforceSiteDto>> {
        return paginated(
            (take) => this.sourceService.getSalesforceSites(take),
            params,
        );
    }

    @Get("salesforce/sites/:siteName/forums")
    @ApiOkResponse()
    async listSalesforceForums(
        @Param("siteName", new ParseEnumPipe(SalesforceSiteName))
        siteName: SalesforceSiteName,
        @Query()
        params: PaginatedRequestDto,
    ): Promise<PaginatedResponseDto<SalesforceForumDto>> {
        return paginated(
            (...args) =>
                this.sourceService.getSalesforceForums(siteName, ...args),
            params,
        );
    }

    @Post("salesforce/sites/:siteName/forums/pull")
    @ApiOperation({
        summary: "Pull top-level forums for the specified site",
    })
    @ApiOkResponse()
    async pullSalesforceForums(
        @Param("siteName", new ParseEnumPipe(SalesforceSiteName))
        siteName: SalesforceSiteName,
    ): Promise<void> {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        for await (const _ of this.sourceService.pullSalesforceForums(
            siteName,
        )) {
            // Do nothing
        }
    }

    @Get("salesforce/sites/:siteName/forums/:forumId/subforums")
    @ApiOkResponse()
    async listSalesforceSubForums(
        @Param("siteName", new ParseEnumPipe(SalesforceSiteName))
        siteName: SalesforceSiteName,
        @Param("forumId") forumId: number,
        @Query()
        params: PaginatedRequestDto,
    ): Promise<PaginatedResponseDto<SalesforceForumDto>> {
        return paginated(
            (...args) =>
                this.sourceService.getSalesforceSubforums(
                    siteName,
                    forumId,
                    ...args,
                ),
            params,
        );
    }

    @Post("salesforce/sites/:siteName/forums/:forumId/subforums/pull")
    @ApiOperation({ summary: "Pull subforums for the specified forum" })
    @ApiOkResponse()
    async pullSalesforceSubForums(
        @Param("siteName", new ParseEnumPipe(SalesforceSiteName))
        siteName: SalesforceSiteName,
        @Param("forumId") forumId: number,
    ): Promise<void> {
        await this.sourceService.pullSalesforceSubforums(siteName, forumId);
    }

    @Delete("youtube/:id")
    @Roles(Role.Admin)
    @ApiNoContentResponse()
    async deleteYoutubeChannel(
        @Param("id") userId: string,
        @Res() res: Response,
    ): Promise<void> {
        await this.sourceService.deleteYoutubeChannel(userId);
        res.sendStatus(204);
    }

    @Delete("reddit/:name")
    @Roles(Role.Admin)
    @ApiNoContentResponse()
    async deleteSubreddit(
        @Param("name") name: string,
        @Res() res: Response,
    ): Promise<void> {
        await this.sourceService.deleteSubreddit(name);
        res.sendStatus(204);
    }

    @Delete("salesforce/sites/:siteName")
    @Roles(Role.Admin)
    @ApiNoContentResponse()
    @ApiParam({
        name: "siteName",
        enum: SalesforceSiteName,
        required: true,
        examples: {
            "AVR Freaks": {
                value: SalesforceSiteName.AVR_FREAKS,
            },
            "Microchip Classic": {
                value: SalesforceSiteName.MICROCHIP_CLASSIC,
            },
        },
    })
    async deleteSalesforceSite(
        @Param("siteName") siteName: string,
        @Res() res: Response,
    ): Promise<void> {
        await this.sourceService.deleteSalesforceSite(siteName);
        res.sendStatus(204);
    }

    @Delete("salesforce/sites/:siteName/forums/:forumId")
    @Roles(Role.Admin)
    @ApiNoContentResponse()
    @ApiParam({
        name: "siteName",
        enum: SalesforceSiteName,
        required: true,
        examples: {
            "AVR Freaks": {
                value: SalesforceSiteName.AVR_FREAKS,
            },
            "Microchip Classic": {
                value: SalesforceSiteName.MICROCHIP_CLASSIC,
            },
        },
    })
    async deleteSalesforceForum(
        @Param("siteName") siteName: string,
        @Param("forumId") forumId: string,
        @Res() res: Response,
    ): Promise<void> {
        await this.sourceService.deleteSalesforceForum(
            siteName,
            Number(forumId),
        );
        res.sendStatus(204);
    }

    @Delete("salesforce/sites/:siteName/forums/:forumId/subforums/:subforumId")
    @Roles(Role.Admin)
    @ApiNoContentResponse()
    @ApiParam({
        name: "siteName",
        enum: SalesforceSiteName,
        required: true,
        examples: {
            "AVR Freaks": {
                value: SalesforceSiteName.AVR_FREAKS,
            },
            "Microchip Classic": {
                value: SalesforceSiteName.MICROCHIP_CLASSIC,
            },
        },
    })
    async deleteSalesforceSubforum(
        @Param("siteName") siteName: string,
        @Param("forumId") forumId: string,
        @Param("subforumId") subforumId: string,
        @Res() res: Response,
    ): Promise<void> {
        await this.sourceService.deleteSalesforceSubforum(
            siteName,
            Number(forumId),
            Number(subforumId),
        );
        res.sendStatus(204);
    }
}
