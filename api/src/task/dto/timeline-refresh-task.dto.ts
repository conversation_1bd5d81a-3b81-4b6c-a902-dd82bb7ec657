import { ApiPropertyOptional } from "@nestjs/swagger";

import { Type } from "class-transformer";
import { IsNumber, IsOptional, ValidateNested } from "class-validator";

export class SalesforceForumJobParams {
    @IsOptional()
    @IsNumber()
    @ApiPropertyOptional({
        type: Number,
        description: "Posts to fetch for each subforum",
        example: 10,
    })
    limit?: number;

    @IsOptional()
    @ApiPropertyOptional({
        description: "Inclusive start date",
        pattern: "\\d\\d\\d\\d-\\d\\d-\\d\\d",
        example: "2025-01-01",
    })
    dateAfter?: string;

    @IsOptional()
    @ApiPropertyOptional({
        description: "Inclusive end date",
        pattern: "\\d\\d\\d\\d-\\d\\d-\\d\\d",
        example: "2025-01-07",
    })
    dateBefore?: string;
}

export class TimelineRefreshTaskRequestDto {
    @ApiPropertyOptional({
        type: SalesforceForumJobParams,
        description: "Parameters for the Salesforce forum job",
    })
    @IsOptional()
    @ValidateNested()
    @Type(() => SalesforceForumJobParams)
    salesforceForumJobParams?: SalesforceForumJobParams;
}
