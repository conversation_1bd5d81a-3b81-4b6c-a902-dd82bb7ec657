import { ApiProperty } from "@nestjs/swagger";

import { IsEnum, IsString } from "class-validator";

import { SalesforceSiteName } from "../../salesforce/types";

export class CreateSalesforceSourceRequestDto {
    @IsEnum(SalesforceSiteName)
    @ApiProperty({
        enum: Object.values(SalesforceSiteName),
        example: "AVR_FREAKS",
    })
    siteName: keyof typeof SalesforceSiteName;
}

export class CreateForumRequestDto {
    @IsString()
    @ApiProperty({
        example: "AVR Tools Miscellaneous",
    })
    forumName: string;
}

export class CreateSubforumRequestDto {
    @IsString()
    @ApiProperty({
        example: "Off Topic",
    })
    subforumName: string;
}

export class SalesforceForumDto {
    id: number;
    name: string;
}

export class SalesforceSiteDto {
    id: number;
    name: string;
    forums: SalesforceForumDto[];
}
