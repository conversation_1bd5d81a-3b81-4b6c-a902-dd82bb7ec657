import { ApiProperty } from "@nestjs/swagger";

import { IsString } from "class-validator";

export class CreateYoutubeSourceRequestDto {
    @IsString()
    @ApiProperty({
        example: "@RobertFeranec",
    })
    channelHandle: string;
}

export class CreateYoutubeSourceResponseDto {
    @IsString()
    id: string;

    @IsString()
    channelHandle: string;
}

export class YoutubeSourceDto {
    @ApiProperty({
        example: "@RobertFeranec",
    })
    channelHandle: string;
}
