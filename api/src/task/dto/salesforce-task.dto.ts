import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";

import { IsArray, IsN<PERSON>ber, IsOptional } from "class-validator";

export class SalesforceTaskRequestDto {
    @IsArray()
    @ApiProperty({
        type: [String],
        isArray: true,
        description: "Array of forum names",
        example: ["Compilers and General Programming"],
    })
    forumNames: string[];

    @IsOptional()
    @IsNumber()
    @ApiPropertyOptional({
        type: Number,
        description: "Posts to fetch for each subforum",
        example: 10,
    })
    limit?: number;
}
