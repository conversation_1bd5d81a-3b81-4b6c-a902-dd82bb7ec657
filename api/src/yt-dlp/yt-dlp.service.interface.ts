import { CacheEntry } from "@epic-web/cachified";

export interface YtDlpServiceInterface {
    setProxyUrl(proxyUrl: string): Promise<void>;

    clearProxyUrl(): Promise<void>;

    getProxyUrl(): Promise<string | null>;

    setCookies(cookies: string): Promise<void>;

    clearCookies(): Promise<void>;

    getCookies(): Promise<CacheEntry<string> | null>;

    getSubtitleURL(
        videoUrl: string,
        withCookies: boolean,
        proxyUrl?: string,
    ): Promise<string>;
}
