import { Injectable, Logger } from "@nestjs/common";

import { CacheEntry, isExpired } from "@epic-web/cachified";
import { spawn } from "child_process";

import { CacheService } from "./../cache/cache.service";
import { YtDlpServiceInterface } from "./yt-dlp.service.interface";

const PROXY_URL_KEY = "yt-dlp:proxy-url";
const COOKIES_KEY = "yt-dlp:cookies";
@Injectable()
export class YtDlpService implements YtDlpServiceInterface {
    private logger = new Logger("YtDlpService");

    constructor(private readonly cacheService: CacheService) {}

    async setProxyUrl(proxyUrl: string): Promise<void> {
        await this.cacheService.cache.set(PROXY_URL_KEY, {
            value: proxyUrl,
            metadata: {
                createdTime: Date.now(),
                ttl: 60 * 60 * 24 * 1000,
            },
        });
    }

    async clearProxyUrl(): Promise<void> {
        await this.cacheService.cache.delete(PROXY_URL_KEY);
    }

    async getProxyUrl(): Promise<string | null> {
        const cacheEntry = await this.cacheService.cache.get(PROXY_URL_KEY);
        if (cacheEntry && isExpired(cacheEntry?.metadata)) {
            await this.cacheService.cache.delete(PROXY_URL_KEY);
            return null;
        }
        return cacheEntry?.value;
    }

    async setCookies(cookies: string): Promise<void> {
        await this.cacheService.cache.set(COOKIES_KEY, {
            value: cookies,
            metadata: {
                createdTime: Date.now(),
                ttl: Infinity,
            },
        });
    }

    async clearCookies(): Promise<void> {
        await this.cacheService.cache.delete(COOKIES_KEY);
    }

    async getCookies(): Promise<CacheEntry<string> | null> {
        const cacheEntry = await this.cacheService.cache.get(COOKIES_KEY);
        if (cacheEntry && isExpired(cacheEntry?.metadata)) {
            await this.cacheService.cache.delete(COOKIES_KEY);
            return null;
        }
        return cacheEntry;
    }

    async getSubtitleURL(
        videoUrl: string,
        withCookies: boolean,
        proxyUrl?: string,
    ): Promise<string> {
        this.logger.log(`Fetching subtitle URL for video ${videoUrl}`);

        const ytDlp = spawn("yt-dlp", [
            "--skip-download",
            ...(withCookies ? ["--cookies", "cookies.txt"] : []),
            ...(proxyUrl ? ["--proxy", proxyUrl] : []),
            "--write-auto-sub",
            "--print",
            "requested_subtitles.en.url",
            videoUrl,
        ]);

        let subtitleUrlBuffer = "";
        let errorBuffer = "";

        ytDlp.stdout.on("data", (data) => {
            subtitleUrlBuffer += data.toString();
        });

        ytDlp.stderr.on("data", (data) => {
            errorBuffer += data.toString();
        });

        return new Promise<string>((resolve, reject) => {
            ytDlp.on("close", (code) => {
                if (code === 0) {
                    resolve(subtitleUrlBuffer.trim());
                } else {
                    reject(new Error(errorBuffer.trim()));
                }
            });
        });
    }
}
