import { BadGatewayException } from "@nestjs/common";

export async function safeFetch(
    url: string | URL | globalThis.Request,
    options?: RequestInit,
    maxAttempts = 3,
    defaultDelaySeconds = 1,
): Promise<ReturnType<typeof fetch>> {
    let delay = defaultDelaySeconds;
    let lastError: Error | null = null;

    for (let attempts = 1; attempts <= maxAttempts; attempts++) {
        const response = await fetch(url, options);
        if (response.ok) return response;

        if (response.status === 429 || response.status === 503) {
            const errorMessage = `${response.status}: ${response.statusText}`;
            lastError = new BadGatewayException(errorMessage);
            delay =
                Number(response.headers.get("X-Ratelimit-Reset")) ||
                defaultDelaySeconds;
        } else {
            const errorMessage = `${response.status}: ${response.statusText}`;
            throw new BadGatewayException(errorMessage);
        }

        await new Promise((resolve) => setTimeout(resolve, delay * 1000));
    }
    throw lastError;
}
