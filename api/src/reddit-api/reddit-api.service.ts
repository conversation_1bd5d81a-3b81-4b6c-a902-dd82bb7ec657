import { BadGatewayException, Injectable, Logger } from "@nestjs/common";

import { CommaSeparatedRedditPostFullnames, RedditPostFullname } from "./types";
import { safeFetch } from "./util";

@Injectable()
export class RedditApiService {
    private logger = new Logger("RedditApiService");

    async getAccessToken() {
        this.logger.log(`Authenticating with Reddit API`);

        const credentials = Buffer.from(
            `${process.env.REDDIT_CLIENT_ID}:${process.env.REDDIT_CLIENT_SECRET}`,
        ).toString("base64");

        const response = await fetch(
            "https://www.reddit.com/api/v1/access_token",
            {
                method: "POST",
                headers: {
                    Authorization: `Basic ${credentials}`,
                    "Content-Type": "application/x-www-form-urlencoded",
                    "User-Agent": "will-app/1.0.0",
                },
                body: "grant_type=client_credentials",
            },
        );

        if (!response.ok) {
            this.logger.error(
                `Reddit API authentication failed with status code ${response.status}`,
            );
            const errorMessage = await response.text();
            this.logger.error(errorMessage);
            throw new BadGatewayException();
        }

        return response;
    }

    async getPosts(
        accessToken: string,
        subredditName: string,
        sort?: "new" | "top",
        after?: RedditPostFullname,
        t?: string,
    ) {
        const url = new URL(
            `https://oauth.reddit.com/r/${subredditName}/${sort || "new"}`,
        );
        url.searchParams.set("limit", "100");
        if (t) url.searchParams.set("t", t);
        if (after) url.searchParams.set("after", after);

        const urlString = url.toString();

        this.logger.log(`Fetching ${urlString}`);

        return safeFetch(urlString, {
            headers: {
                Authorization: `bearer ${accessToken}`,
            },
        });
    }

    async getComments(
        accessToken: string,
        subredditName: string,
        postId: string,
        count = 100,
    ) {
        this.logger.log(
            `Fetching comments for post r/${subredditName}/${postId}`,
        );

        return safeFetch(
            `https://oauth.reddit.com/r/${subredditName}/comments/${postId}?limit=${count}`,
            {
                headers: {
                    Authorization: `bearer ${accessToken}`,
                },
            },
        );
    }

    async getPostInfo(
        accessToken: string,
        idList: CommaSeparatedRedditPostFullnames,
    ) {
        this.logger.log(`Fetching post info for posts ${idList}`);

        return safeFetch(
            `https://oauth.reddit.com/api/info.json?id=${idList}`,
            {
                headers: {
                    Authorization: `bearer ${accessToken}`,
                },
            },
        );
    }
}
