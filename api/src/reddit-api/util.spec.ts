import { safeFetch } from "./util";

describe("RedditApiService utils", () => {
    let fetchMock: jest.Mock;

    beforeEach(() => {
        fetchMock = jest.fn();
        global.fetch = fetchMock;
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    it("handles 200 response", async () => {
        fetchMock.mockResolvedValue(
            new Response(JSON.stringify({ success: true, data: "test" }), {
                status: 200,
            }),
        );
        const result = await safeFetch("https://example.com");
        expect(result).toMatchObject({
            ok: true,
            status: 200,
        });
    });
    it("handles 404 response", async () => {
        fetchMock.mockResolvedValue(
            new Response(JSON.stringify({ success: false }), {
                status: 404,
                statusText: "Not Found",
            }),
        );

        const error = await safeFetch("https://example.com", {}, 3, 0).catch(
            (error) => error,
        );

        expect(error).toMatchObject({
            message: "404: Not Found",
        });

        expect(fetchMock).toHaveBeenCalledTimes(1);
    });

    it("handles failed fetch", async () => {
        fetchMock.mockRejectedValue(new Error("Failed to fetch"));

        const error = await safeFetch("https://example.com", {}, 3, 0).catch(
            (error) => error,
        );

        expect(error).toMatchObject({
            message: "Failed to fetch",
        });
        expect(fetchMock).toHaveBeenCalledTimes(1);
    });

    describe("Handles 429 response", () => {
        it("calls fetch 3 times before throwing an error", async () => {
            fetchMock.mockResolvedValue(
                new Response(JSON.stringify({ success: false }), {
                    status: 429,
                    statusText: "Too Many Requests",
                }),
            );

            const error = await safeFetch(
                "https://example.com",
                {},
                3,
                0,
            ).catch((error) => error);

            expect(error).toMatchObject({
                message: "429: Too Many Requests",
            });

            expect(fetchMock).toHaveBeenCalledTimes(3);
        });

        describe("when the rate limit reset header is present", () => {
            beforeEach(() => {
                jest.useFakeTimers();
                jest.spyOn(global, "setTimeout");
            });

            afterEach(() => {
                jest.useRealTimers();
            });

            it("uses the reset time to determine the delay", async () => {
                fetchMock.mockResolvedValue(
                    new Response(JSON.stringify({ success: false }), {
                        status: 429,
                        statusText: "Too Many Requests",
                        headers: {
                            "X-Ratelimit-Reset": "10",
                        },
                    }),
                );

                const errorPromise = safeFetch(
                    "https://example.com",
                    {},
                    1,
                    10,
                ).catch((error) => error);

                await jest.runOnlyPendingTimersAsync();

                const error = await errorPromise;

                expect(error).toMatchObject({
                    message: "429: Too Many Requests",
                });

                expect(setTimeout).toHaveBeenCalledWith(
                    expect.any(Function),
                    10_000,
                );
                expect(fetchMock).toHaveBeenCalledTimes(1);
            });
        });
    });
});
