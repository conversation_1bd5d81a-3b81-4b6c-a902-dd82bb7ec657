import { Module } from "@nestjs/common";

import { S3Module } from "nestjs-s3";

import { AllAboutCircuitsS3Service } from "./all-about-circuits-s3.service";

@Module({
    imports: [
        S3Module.forRoot({
            // Region and credentials inferred in production.
            config: {},
        }),
    ],
    providers: [AllAboutCircuitsS3Service],
    exports: [AllAboutCircuitsS3Service],
})
export class AllAboutCircuitsS3Module {}
