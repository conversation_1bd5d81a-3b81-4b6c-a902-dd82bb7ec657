import { Injectable } from "@nestjs/common";

import { InjectS3, S3 } from "nestjs-s3";

import { AllAboutCircuitsS3ServiceInterface } from "./all-about-circuits-s3-service.interface";

@Injectable()
export class AllAboutCircuitsS3Service
    implements AllAboutCircuitsS3ServiceInterface
{
    constructor(@InjectS3() private readonly s3: S3) {}

    async getForumsLatestPost() {
        return this.getObject("forums_latest_post.json");
    }

    async getForums() {
        return this.getObject("forums.json");
    }

    async getThreadsMetadata(key: string) {
        return this.getObject(key);
    }

    async getThread(key: string) {
        return this.getObject(key);
    }

    private async getObject(key: string): Promise<string> {
        const commandOutput = await this.s3.getObject({
            Bucket: "allaboutcircuits-scraper",
            Key: key,
        });
        return commandOutput.Body.transformToString();
    }
}
