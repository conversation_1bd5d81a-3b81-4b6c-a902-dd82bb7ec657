import { Injectable, Logger } from "@nestjs/common";

import { AppServiceInterface } from "./app-service.interface";
import { getRedis } from "./common/redis";
import { PrismaRootService } from "./prisma-root/prisma-root.service";

@Injectable()
export class AppService implements AppServiceInterface {
    private logger = new Logger("AppService");

    constructor(private readonly prisma: PrismaRootService) {}

    async isDatabaseConnectionHealthy(): Promise<boolean> {
        try {
            await this.prisma.$queryRaw`SELECT 1`;
            return true;
        } catch (err) {
            this.logger.error("❌ Database connection error:", err.message);
            return false;
        }
    }

    async isRedisConnectionHealthy(): Promise<boolean> {
        const client = getRedis();
        try {
            await client.ping();
            return true;
        } catch (error) {
            this.logger.error("❌ Redis connection error:", error.message);
            return false;
        } finally {
            client.disconnect();
        }
    }
}
