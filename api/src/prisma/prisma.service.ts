/* eslint-disable @darraghor/nestjs-typed/injectable-should-be-provided */
import { Injectable } from "@nestjs/common";

import { Prisma } from "@prisma/client";
import { Sql } from "@prisma/client/runtime/library";
import { ClsService } from "nestjs-cls";
import { PrismaService as NestPrismaService } from "nestjs-prisma";

@Injectable()
export class PrismaService extends NestPrismaService {
    constructor(private readonly cls: ClsService) {
        super({
            explicitConnect: true,
            prismaOptions: {
                datasources: {
                    db: {
                        url: process.env.DATABASE_URL_APP_USER,
                    },
                },
                transactionOptions: {
                    maxWait: 10000,
                    timeout: 15000,
                },
            },
        });
    }

    $queryRawWithTenant<T = unknown>(
        query: TemplateStringsArray | Sql,
        ...values: unknown[]
    ): Prisma.PrismaPromise<T> {
        const tenantId = this.cls.get("tenantId") || "-1";

        return super.$transaction(async (tx: PrismaService) => {
            await tx.$executeRaw`SELECT set_config('app.tenant_id', ${tenantId}, TRUE);`;
            return tx.queryRawNonRecursive<T>(query, ...values);
        }) as Prisma.PrismaPromise<T>;
    }

    private queryRawNonRecursive<T = unknown>(
        query: TemplateStringsArray | Sql,
        ...values: unknown[]
    ): Prisma.PrismaPromise<T> {
        return super.$queryRaw<T>(query, ...values);
    }

    async onModuleDestroy() {
        await this.$disconnect();
    }
}
