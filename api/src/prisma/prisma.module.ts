import { Global, Module } from "@nestjs/common";

import * as jwt from "jsonwebtoken";
import { ClsModule, ClsService } from "nestjs-cls";

import { PrismaService } from "./prisma.service";

@Global()
@Module({
    imports: [
        // Add continuation local storage to track tenantId from request header throughout request lifecycle
        ClsModule.forRoot({
            global: true,
            middleware: {
                mount: true,
                setup: (cls, req) => {
                    // Take tenantId from auth token without verifying (token is verified subsequently in JwtAuthGuard)
                    const authToken = req.cookies?.["auth"];
                    const decoded = jwt.decode(authToken);
                    const tenantId = decoded?.["tenantId"];
                    cls.set("tenantId", tenantId);
                },
            },
        }),
    ],
    providers: [
        {
            provide: PrismaService,
            inject: [ClsService],
            // Extend PrismaService to access tables with tenant isolation policies
            useFactory: async (cls: ClsService) => {
                const prisma = new PrismaService(cls);

                return prisma.$extends({
                    query: {
                        $allModels: {
                            $allOperations: async ({ args, query }) => {
                                const tenantId = cls.get("tenantId");

                                const [, result] = await prisma.$transaction([
                                    prisma.$executeRaw`SELECT set_config('app.tenant_id', ${`${tenantId || "-1"}`}, TRUE);`,
                                    query(args),
                                ]);

                                return result;
                            },
                        },
                    },
                });
            },
        },
    ],
    exports: [PrismaService],
})
export class PrismaModule {}
