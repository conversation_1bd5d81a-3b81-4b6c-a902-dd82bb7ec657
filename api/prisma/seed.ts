import { PrismaClient, DataSource } from "@prisma/client";
import { hashSync } from "bcrypt";
import { parseArgs } from "util";
import { isStrongPassword } from "validator";

import { MICROCHIP_TOPIC_NAME, SEMI_INDUSTRY_TOPIC_NAME } from "./constants";

const prisma = new PrismaClient();

const options = {
    environment: { type: "string" as const },
};

async function main(args) {
    const {
        values: { environment },
    } = parseArgs({ args, options, allowPositionals: true });
    if (environment !== "test" && !!environment) {
        console.error(`Unrecognized seed environment option "${environment}"`);
        return;
    }

    // Add default tenant
    const defaultTenant = await prisma.tenant.upsert({
        where: {
            id: "DEFAULT",
        },
        update: {
            name: "Microchip",
        },
        create: { id: "DEFAULT", name: "Microchip" },
    });
    console.log({ defaultTenant });

    // Create admin user
    if (!isStrongPassword(process.env["ADMIN_PASS"])) {
        throw new Error(`
            ADMIN_PASS must be a strong password with at least
                * 8 characters
                * 1 lowercase letter
                * 1 uppercase letter
                * 1 number
                * 1 symbol
            See "isStrongPassword" on the page https://www.npmjs.com/package/validator for details.
            `);
    }

    const adminUser = await prisma.user.upsert({
        where: {
            username: "admin",
        },
        update: {
            username: "admin",
            email: process.env["ADMIN_EMAIL"],
            passwordHash: hashSync(process.env["ADMIN_PASS"], 10),
            tenants: {
                connect: {
                    id: defaultTenant.id,
                },
            },
        },
        create: {
            username: "admin",
            email: process.env["ADMIN_EMAIL"],
            passwordHash: hashSync(process.env["ADMIN_PASS"], 10),
            tenants: {
                connect: {
                    id: defaultTenant.id,
                },
            },
        },
    });
    console.log({ adminUser });

    // Attach admin roles to admin user
    for (const adminRole of ["admin", "super-admin"]) {
        const role = await prisma.role.upsert({
            where: { name: adminRole },
            update: {},
            create: { name: adminRole },
        });
        await prisma.rolesOnUsers.upsert({
            where: { userId_roleId: { userId: adminUser.id, roleId: role.id } },
            update: {},
            create: { userId: adminUser.id, roleId: role.id },
        });
    }

    // Add "Microchip Technology Inc" topic
    const microchipTopic = await prisma.topic.upsert({
        where: {
            tenantId_name: {
                tenantId: defaultTenant.id,
                name: MICROCHIP_TOPIC_NAME,
            },
        },
        update: {},
        create: {
            name: MICROCHIP_TOPIC_NAME,
            tenantId: defaultTenant.id,
        },
    });
    console.log({ microchipTopic });

    // Add "Semiconductor Industry" topic
    const industryTopic = await prisma.topic.upsert({
        where: {
            tenantId_name: {
                tenantId: defaultTenant.id,
                name: SEMI_INDUSTRY_TOPIC_NAME,
            },
        },
        update: {
            tenantId: defaultTenant.id,
        },
        create: {
            name: SEMI_INDUSTRY_TOPIC_NAME,
            tenantId: defaultTenant.id,
        },
    });
    console.log({ industryTopic });

    if (environment !== "test") return;

    console.log("\nAdding additional test data\n");

    // add an extra test user
    const testuser = await prisma.user.upsert({
        where: {
            username: "test-user",
        },
        update: {},
        create: {
            firstName: "testFirstName",
            lastName: "testLastName",
            username: "test-user",
            email: "<EMAIL>",
            passwordHash: hashSync(process.env["ADMIN_PASS"], 10),
            tenants: {
                connect: {
                    id: defaultTenant.id,
                },
            },
        },
    });

    const passwordResetTestUser = await prisma.user.upsert({
        where: {
            username: "password-test-user",
        },
        update: {},
        create: {
            firstName: "pwResetFirstName",
            lastName: "pwResetLastName",
            username: "password-test-user",
            email: "<EMAIL>",
            passwordHash: hashSync(process.env["ADMIN_PASS"], 10),
            tenants: {
                connect: {
                    id: defaultTenant.id,
                },
            },
        },
    });

    console.log("test user", { testuser });
    console.log("password reset test user", { passwordResetTestUser });

    // Extra tenant
    await prisma.user.update({
        where: { username: "admin" },
        data: {
            tenants: {
                connectOrCreate: {
                    where: { name: "extra-tenant" },
                    create: { name: "extra-tenant" },
                },
            },
        },
    });

    // Add test Topic Group Data
    for (let i = 1; i <= 3; i++) {
        const topicGroup = await prisma.tag.create({
            data: {
                name: `sample-topic-group-${i}`,
                description: `topic-group-description-${i}`,
                tenant: { connect: { id: defaultTenant.id } },
                createdBy: { connect: { id: adminUser.id } },
                isPersonal: i === 2,
            },
        });
        console.log("topic-group", { topicGroup });
    }

    // Add test Group data
    for (let i = 1; i <= 3; i++) {
        const group = await prisma.group.upsert({
            where: {
                tenantId_name: {
                    tenantId: defaultTenant.id,
                    name: `sample-group-${i}`,
                },
            },
            update: {
                name: `sample-group-${i}`,
                description: `group-description-${i}`,
            },
            create: {
                name: `sample-group-${i}`,
                description: `group-description-${i}`,
                tenant: { connect: { id: defaultTenant.id } },
            },
        });
        console.log("group", { group });
    }

    // Add test Topic data
    for (let i = 1; i <= 3; i++) {
        const topic = await prisma.topic.upsert({
            where: {
                tenantId_name: {
                    tenantId: defaultTenant.id,
                    name: `sample-topic-${i}`,
                },
            },
            update: {},
            create: {
                name: `sample-topic-${i}`,
                description: `topic-description-${i}`,
                tenantId: defaultTenant.id,
            },
        });

        await prisma.topic.update({
            where: {
                tenantId_name: {
                    tenantId: defaultTenant.id,
                    name: `sample-topic-${i}`,
                },
            },
            data: {
                // connect all three tags
                tags: {
                    connect: [{ id: i }],
                },
                // connect one group by its auto-incremented ID
                groups: {
                    connect: [
                        {
                            tenantId_name: {
                                tenantId: defaultTenant.id,
                                name: `sample-group-${i}`,
                            },
                        },
                    ],
                },
                // set the admin user as the creator
                createdBy: {
                    connect: { username: "admin" },
                },
            },
        });

        console.log("topic", { topic });
    }
    // Add test Reddit data

    const sevenMonthsAgo = new Date();
    sevenMonthsAgo.setMonth(sevenMonthsAgo.getMonth() - 7);

    const redditPost = await prisma.redditPost.upsert({
        where: {
            tenantId_id: {
                tenantId: defaultTenant.id,
                id: "test-id1",
            },
        },
        update: {},
        create: {
            id: "test-id1",
            title: "Microchip frustrations",
            text: "Am I the only one that feels like microchip's development tools are poorly polished turds? My department has decided to go with them for our next gen platform and 6 months later I am seriously concerned for the future of the project. Does anyone know of better tools that are available?",
            voteScore: 42,
            commentCount: 7,
            subreddit: {
                connectOrCreate: {
                    where: {
                        tenantId_name: {
                            tenantId: defaultTenant.id,
                            name: "r/microcontrollers",
                        },
                    },
                    create: {
                        name: "r/microcontrollers",
                        tenantId: defaultTenant.id,
                    },
                },
            },
            publishedAt: sevenMonthsAgo.toISOString(),
        },
    });

    console.log({ redditPost });

    // Add test YouTube data

    const today = new Date();

    const youtubeVideo = await prisma.youtubeVideo.upsert({
        where: {
            tenantId_id: {
                tenantId: defaultTenant.id,
                id: "test-id2",
            },
        },
        update: {},
        create: {
            id: "test-id2",
            title: "HACKED!: Microwave Transformer becomes a High Current Transformer",
            channel: {
                connectOrCreate: {
                    where: {
                        tenantId: defaultTenant.id,
                        channelHandle: "greatscottlab",
                    },
                    create: {
                        id: "UC6mIxFTvXkWQVEHPsEdflzQ",
                        channelHandle: "greatscottlab",
                        uploadsPlaylistId: "UU6mIxFTvXkWQVEHPsEdflzQ",
                        tenantId: defaultTenant.id,
                    },
                },
            },
            commentCount: 13,
            likeCount: 128,
            subtitles: "",
            publishedAt: today.toISOString(),
        },
    });

    console.log({ youtubeVideo });

    const sources = [
        { record: redditPost, source: DataSource.REDDIT_POST },
        { record: youtubeVideo, source: DataSource.YOUTUBE_VIDEO },
    ];

    // Add test Analysis data
    for (let i = 0; i < 2; i++) {
        const { record, source } = sources[i % sources.length];
        const analysis = await prisma.analysis.upsert({
            where: {
                tenantId_sourceId: {
                    tenantId: defaultTenant.id,
                    sourceId: record.id,
                },
            },
            update: {},
            create: {
                sentiment: i * 10,
                summary: `Seeded ${source} summary #${i + 1}`,
                relevance: 90,

                source,
                sourceId: record.id,

                bookmarkedBy:
                    i === 2
                        ? { connect: { username: "test-user" } }
                        : undefined,
                tenant: { connect: { id: defaultTenant.id } },
            },
        });

        console.log("  •  Analysis id =", analysis.id, source);
    }

    // Add test Narrative data
    for (let i = 0; i < 2; i++) {
        const analysis = await prisma.analysis.findFirst({
            where: { sourceId: sources[i % sources.length].record.id },
        });

        const narrative = await prisma.narrative.create({
            data: {
                aspect: "BUG",
                summary: `seeded narrative #${i + 1}`,
                topic: {
                    connect: {
                        tenantId_name: {
                            tenantId: defaultTenant.id,
                            name: `sample-topic-${(i % 3) + 1}`,
                        },
                    },
                },
                tenant: { connect: { id: defaultTenant.id } },
            },
        });
        console.log("  •  Narrative id =", narrative.id, narrative.summary);

        await prisma.analysisToNarrative.create({
            data: {
                analysisId: analysis.id,
                narrativeId: narrative.id,
                sentiment: i * 10,
                sentimentReasoning: `Seeded ${sources[i % sources.length].source} sentiment reasoning #${i + 1}`,
            },
        });
    }
}

main(process.argv)
    .then(async () => {
        await prisma.$disconnect();
    })
    .catch(async (e) => {
        console.error(e);
        await prisma.$disconnect();
        process.exit(1);
    });
