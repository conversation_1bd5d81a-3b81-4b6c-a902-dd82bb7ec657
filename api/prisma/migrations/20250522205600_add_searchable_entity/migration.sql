-- CreateExtension
CREATE EXTENSION IF NOT EXISTS "vector";

-- CreateEnum
CREATE TYPE "DataSource" AS ENUM ('REDDIT_POST', 'YOUTUBE_VIDEO', 'REDDIT_COMMENT', 'YOUTUBE_COMMENT');

-- CreateTable
CREATE TABLE "SearchableEntity" (
    "id" SERIAL NOT NULL,
    "source" "DataSource" NOT NULL,
    "sourceId" TEXT NOT NULL,
    "embedding" vector(1536) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "SearchableEntity_pkey" PRIMARY KEY ("id")
);
