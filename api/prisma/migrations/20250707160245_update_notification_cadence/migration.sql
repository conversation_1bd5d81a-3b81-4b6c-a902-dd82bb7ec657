UPDATE "UserTopicPreference"
SET "notificationCadence" = 'WEEKLY'
WHERE "notificationCadence" IN ('ON_GROUP_SHARE');

-- AlterEnum
BEGIN;
CREATE TYPE "NotificationCadence_new" AS ENUM ('WEEKLY', 'OFF');
ALTER TABLE "UserTopicPreference" ALTER COLUMN "notificationCadence" DROP DEFAULT;
ALTER TABLE "UserTopicPreference" ALTER COLUMN "notificationCadence" TYPE "NotificationCadence_new" USING ("notificationCadence"::text::"NotificationCadence_new");
ALTER TYPE "NotificationCadence" RENAME TO "NotificationCadence_old";
ALTER TYPE "NotificationCadence_new" RENAME TO "NotificationCadence";
DROP TYPE "NotificationCadence_old";
ALTER TABLE "UserTopicPreference" ALTER COLUMN "notificationCadence" SET DEFAULT 'WEEKLY';
COMMIT;
