/*
  Warnings:

  - A unique constraint covering the columns `[youtubeCommentId]` on the table `Analysis` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterTable
ALTER TABLE "Analysis" ADD COLUMN     "youtubeCommentId" TEXT;

-- AlterTable
ALTER TABLE "YoutubeComment" ADD COLUMN     "analysisId" INTEGER;

-- CreateIndex
CREATE UNIQUE INDEX "Analysis_youtubeCommentId_key" ON "Analysis"("youtubeCommentId");

-- AddForeignKey
ALTER TABLE "Analysis" ADD CONSTRAINT "Analysis_youtubeCommentId_fkey" FOREIGN KEY ("youtubeCommentId") REFERENCES "YoutubeComment"("id") ON DELETE SET NULL ON UPDATE CASCADE;
