/*
  Warnings:

  - You are about to drop the column `analysisId` on the `RedditComment` table. All the data in the column will be lost.
  - You are about to drop the column `analysisId` on the `RedditPost` table. All the data in the column will be lost.
  - You are about to drop the column `analysisId` on the `YoutubeComment` table. All the data in the column will be lost.
  - You are about to drop the column `analysisId` on the `YoutubeVideo` table. All the data in the column will be lost.
  - You are about to drop the `Analysis` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `_AnalysisToTopic` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `_AnalysisToUser` table. If the table is not empty, all the data it contains will be lost.

*/
-- AlterEnum
ALTER TYPE "NarrativeAspect" ADD VALUE 'UNKNOWN';

-- DropForeignKey
ALTER TABLE "Analysis" DROP CONSTRAINT "Analysis_redditCommentId_fkey";

-- DropForeignKey
ALTER TABLE "Analysis" DROP CONSTRAINT "Analysis_redditPostId_fkey";

-- DropForeignKey
ALTER TABLE "Analysis" DROP CONSTRAINT "Analysis_youtubeCommentId_fkey";

-- DropForeignKey
ALTER TABLE "Analysis" DROP CONSTRAINT "Analysis_youtubeVideoId_fkey";

-- DropForeignKey
ALTER TABLE "_AnalysisToTopic" DROP CONSTRAINT "_AnalysisToTopic_A_fkey";

-- DropForeignKey
ALTER TABLE "_AnalysisToTopic" DROP CONSTRAINT "_AnalysisToTopic_B_fkey";

-- DropForeignKey
ALTER TABLE "_AnalysisToUser" DROP CONSTRAINT "_AnalysisToUser_A_fkey";

-- DropForeignKey
ALTER TABLE "_AnalysisToUser" DROP CONSTRAINT "_AnalysisToUser_B_fkey";

-- AlterTable
ALTER TABLE "RedditComment" DROP COLUMN "analysisId";

-- AlterTable
ALTER TABLE "RedditPost" DROP COLUMN "analysisId";

-- AlterTable
ALTER TABLE "SentimentAnalysis" ALTER COLUMN "topicId" DROP NOT NULL;

-- AlterTable
ALTER TABLE "YoutubeComment" DROP COLUMN "analysisId";

-- AlterTable
ALTER TABLE "YoutubeVideo" DROP COLUMN "analysisId";

-- DropTable
DROP TABLE "Analysis";

-- DropTable
DROP TABLE "_AnalysisToTopic";

-- DropTable
DROP TABLE "_AnalysisToUser";
