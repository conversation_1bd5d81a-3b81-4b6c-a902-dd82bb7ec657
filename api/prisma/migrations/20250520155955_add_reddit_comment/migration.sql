/*
  Warnings:

  - A unique constraint covering the columns `[redditCommentId]` on the table `Analysis` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterTable
ALTER TABLE "Analysis" ADD COLUMN     "redditCommentId" TEXT;

-- AlterTable
ALTER TABLE "RedditComment" ADD COLUMN     "analysisId" INTEGER;

-- CreateIndex
CREATE UNIQUE INDEX "Analysis_redditCommentId_key" ON "Analysis"("redditCommentId");

-- AddForeignKey
ALTER TABLE "Analysis" ADD CONSTRAINT "Analysis_redditCommentId_fkey" FOREIGN KEY ("redditCommentId") REFERENCES "RedditComment"("id") ON DELETE SET NULL ON UPDATE CASCADE;
