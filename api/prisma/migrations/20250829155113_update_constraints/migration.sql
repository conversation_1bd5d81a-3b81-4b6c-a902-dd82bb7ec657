/*
  Warnings:

  - The primary key for the `RedditPost` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `YoutubeChannel` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `YoutubeComment` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `YoutubeVideo` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - A unique constraint covering the columns `[tenantId,sourceId]` on the table `Analysis` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[tenantId,name]` on the table `Group` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[tenantId,id]` on the table `RedditComment` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[tenantId,id]` on the table `RedditPost` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[tenantId,name]` on the table `SalesforceSite` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[tenantId,source,sourceId]` on the table `SearchableEntity` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[tenantId,name]` on the table `Subreddit` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[tenantId,name]` on the table `Tag` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[name]` on the table `Tenant` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[tenantId,name]` on the table `Topic` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[tenantId,id]` on the table `YoutubeChannel` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[tenantId,id]` on the table `YoutubeComment` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[tenantId,id]` on the table `YoutubeVideo` will be added. If there are existing duplicate values, this will fail.

*/
-- DropForeignKey
ALTER TABLE "public"."RedditComment" DROP CONSTRAINT "RedditComment_postId_fkey";

-- DropForeignKey
ALTER TABLE "public"."RedditPost" DROP CONSTRAINT "RedditPost_subredditName_fkey";

-- DropForeignKey
ALTER TABLE "public"."YoutubeComment" DROP CONSTRAINT "YoutubeComment_parentId_fkey";

-- DropForeignKey
ALTER TABLE "public"."YoutubeComment" DROP CONSTRAINT "YoutubeComment_videoId_fkey";

-- DropForeignKey
ALTER TABLE "public"."YoutubeVideo" DROP CONSTRAINT "YoutubeVideo_channelId_fkey";

-- DropIndex
DROP INDEX "public"."Analysis_sourceId_key";

-- DropIndex
DROP INDEX "public"."Group_name_key";

-- DropIndex
DROP INDEX "public"."SalesforceSite_name_key";

-- DropIndex
DROP INDEX "public"."SearchableEntity_source_sourceId_key";

-- DropIndex
DROP INDEX "public"."Subreddit_name_key";

-- DropIndex
DROP INDEX "public"."Tag_name_key";

-- DropIndex
DROP INDEX "public"."Topic_name_key";

-- AlterTable
ALTER TABLE "public"."AllAboutCircuitsForum" ALTER COLUMN "tenantId" SET DEFAULT current_setting('app.tenant_id');

-- AlterTable
ALTER TABLE "public"."Analysis" ALTER COLUMN "tenantId" SET DEFAULT current_setting('app.tenant_id');

-- AlterTable
ALTER TABLE "public"."Group" ALTER COLUMN "tenantId" SET DEFAULT current_setting('app.tenant_id');

-- AlterTable
ALTER TABLE "public"."Narrative" ALTER COLUMN "tenantId" SET DEFAULT current_setting('app.tenant_id');

-- AlterTable
ALTER TABLE "public"."RedditComment" ALTER COLUMN "tenantId" SET DEFAULT current_setting('app.tenant_id');

-- AlterTable
ALTER TABLE "public"."RedditPost" DROP CONSTRAINT "RedditPost_pkey",
ALTER COLUMN "tenantId" SET DEFAULT current_setting('app.tenant_id');

-- AlterTable
ALTER TABLE "public"."SalesforceSite" ALTER COLUMN "tenantId" SET DEFAULT current_setting('app.tenant_id');

-- AlterTable
ALTER TABLE "public"."SearchableEntity" ALTER COLUMN "tenantId" SET DEFAULT current_setting('app.tenant_id');

-- AlterTable
ALTER TABLE "public"."Subreddit" ALTER COLUMN "tenantId" SET DEFAULT current_setting('app.tenant_id');

-- AlterTable
ALTER TABLE "public"."Tag" ALTER COLUMN "tenantId" SET DEFAULT current_setting('app.tenant_id');

-- AlterTable
ALTER TABLE "public"."Topic" ALTER COLUMN "tenantId" DROP NOT NULL,
ALTER COLUMN "tenantId" SET DEFAULT current_setting('app.tenant_id');

-- AlterTable
ALTER TABLE "public"."YoutubeChannel" DROP CONSTRAINT "YoutubeChannel_pkey",
ALTER COLUMN "tenantId" SET DEFAULT current_setting('app.tenant_id');

-- AlterTable
ALTER TABLE "public"."YoutubeComment" DROP CONSTRAINT "YoutubeComment_pkey",
ALTER COLUMN "tenantId" SET DEFAULT current_setting('app.tenant_id');

-- AlterTable
ALTER TABLE "public"."YoutubeVideo" DROP CONSTRAINT "YoutubeVideo_pkey",
ALTER COLUMN "tenantId" SET DEFAULT current_setting('app.tenant_id');

-- CreateIndex
CREATE UNIQUE INDEX "Analysis_tenantId_sourceId_key" ON "public"."Analysis"("tenantId", "sourceId");

-- CreateIndex
CREATE UNIQUE INDEX "Group_tenantId_name_key" ON "public"."Group"("tenantId", "name");

-- CreateIndex
CREATE UNIQUE INDEX "RedditComment_tenantId_id_key" ON "public"."RedditComment"("tenantId", "id");

-- CreateIndex
CREATE UNIQUE INDEX "RedditPost_tenantId_id_key" ON "public"."RedditPost"("tenantId", "id");

-- CreateIndex
CREATE UNIQUE INDEX "SalesforceSite_tenantId_name_key" ON "public"."SalesforceSite"("tenantId", "name");

-- CreateIndex
CREATE UNIQUE INDEX "SearchableEntity_tenantId_source_sourceId_key" ON "public"."SearchableEntity"("tenantId", "source", "sourceId");

-- CreateIndex
CREATE UNIQUE INDEX "Subreddit_tenantId_name_key" ON "public"."Subreddit"("tenantId", "name");

-- CreateIndex
CREATE UNIQUE INDEX "Tag_tenantId_name_key" ON "public"."Tag"("tenantId", "name");

-- CreateIndex
CREATE UNIQUE INDEX "Tenant_name_key" ON "public"."Tenant"("name");

-- CreateIndex
CREATE UNIQUE INDEX "Topic_tenantId_name_key" ON "public"."Topic"("tenantId", "name");

-- CreateIndex
CREATE UNIQUE INDEX "YoutubeChannel_tenantId_id_key" ON "public"."YoutubeChannel"("tenantId", "id");

-- CreateIndex
CREATE UNIQUE INDEX "YoutubeComment_tenantId_id_key" ON "public"."YoutubeComment"("tenantId", "id");

-- CreateIndex
CREATE UNIQUE INDEX "YoutubeVideo_tenantId_id_key" ON "public"."YoutubeVideo"("tenantId", "id");

-- CreateIndex
CREATE INDEX "_TenantToUser_B_index" ON "public"."_TenantToUser"("B");

-- AddForeignKey
ALTER TABLE "public"."Group" ADD CONSTRAINT "Group_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "public"."Tenant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."YoutubeVideo" ADD CONSTRAINT "YoutubeVideo_tenantId_channelId_fkey" FOREIGN KEY ("tenantId", "channelId") REFERENCES "public"."YoutubeChannel"("tenantId", "id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."YoutubeVideo" ADD CONSTRAINT "YoutubeVideo_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "public"."Tenant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."RedditPost" ADD CONSTRAINT "RedditPost_tenantId_subredditName_fkey" FOREIGN KEY ("tenantId", "subredditName") REFERENCES "public"."Subreddit"("tenantId", "name") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."RedditPost" ADD CONSTRAINT "RedditPost_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "public"."Tenant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Analysis" ADD CONSTRAINT "Analysis_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "public"."Tenant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Tag" ADD CONSTRAINT "Tag_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "public"."Tenant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Topic" ADD CONSTRAINT "Topic_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "public"."Tenant"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."YoutubeChannel" ADD CONSTRAINT "YoutubeChannel_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "public"."Tenant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Subreddit" ADD CONSTRAINT "Subreddit_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "public"."Tenant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."SalesforceSite" ADD CONSTRAINT "SalesforceSite_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "public"."Tenant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."AllAboutCircuitsForum" ADD CONSTRAINT "AllAboutCircuitsForum_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "public"."Tenant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."RedditComment" ADD CONSTRAINT "RedditComment_tenantId_postId_fkey" FOREIGN KEY ("tenantId", "postId") REFERENCES "public"."RedditPost"("tenantId", "id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."RedditComment" ADD CONSTRAINT "RedditComment_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "public"."Tenant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."YoutubeComment" ADD CONSTRAINT "YoutubeComment_tenantId_parentId_fkey" FOREIGN KEY ("tenantId", "parentId") REFERENCES "public"."YoutubeComment"("tenantId", "id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."YoutubeComment" ADD CONSTRAINT "YoutubeComment_tenantId_videoId_fkey" FOREIGN KEY ("tenantId", "videoId") REFERENCES "public"."YoutubeVideo"("tenantId", "id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."YoutubeComment" ADD CONSTRAINT "YoutubeComment_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "public"."Tenant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."SearchableEntity" ADD CONSTRAINT "SearchableEntity_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "public"."Tenant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Narrative" ADD CONSTRAINT "Narrative_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "public"."Tenant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."_TenantToUser" ADD CONSTRAINT "_TenantToUser_A_fkey" FOREIGN KEY ("A") REFERENCES "public"."Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."_TenantToUser" ADD CONSTRAINT "_TenantToUser_B_fkey" FOREIGN KEY ("B") REFERENCES "public"."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
