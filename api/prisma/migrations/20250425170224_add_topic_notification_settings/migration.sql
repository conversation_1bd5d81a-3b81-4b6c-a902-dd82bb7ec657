/*
  Warnings:

  - Added the required column `notificationCadence` to the `Topic` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "NotificationCadence" AS ENUM ('DAILY', 'WEEKLY', 'MONTHLY', 'ON_GROUP_SHARE');

-- AlterTable
ALTER TABLE "Topic" ADD COLUMN     "negativeSentimentNotifications" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "notificationCadence" "NotificationCadence" NOT NULL;
