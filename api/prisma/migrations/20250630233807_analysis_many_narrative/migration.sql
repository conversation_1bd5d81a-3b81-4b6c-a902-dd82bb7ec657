/*
  Warnings:

  - You are about to drop the column `aspect` on the `Analysis` table. All the data in the column will be lost.
  - You are about to drop the column `narrativeId` on the `Analysis` table. All the data in the column will be lost.
  - You are about to drop the `_AnalysisToTopic` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "Analysis" DROP CONSTRAINT "Analysis_narrativeId_fkey";

-- DropForeignKey
ALTER TABLE "_AnalysisToTopic" DROP CONSTRAINT "_AnalysisToTopic_A_fkey";

-- DropForeignKey
ALTER TABLE "_AnalysisToTopic" DROP CONSTRAINT "_AnalysisToTopic_B_fkey";

-- AlterTable
ALTER TABLE "Analysis" DROP COLUMN "aspect",
DROP COLUMN "narrativeId";

-- DropTable
DROP TABLE "_AnalysisToTopic";

-- CreateTable
CREATE TABLE "AnalysisToNarrative" (
    "analysisId" INTEGER NOT NULL,
    "narrativeId" INTEGER NOT NULL,
    "sentiment" INTEGER NOT NULL,
    "sentimentReasoning" TEXT,

    CONSTRAINT "AnalysisToNarrative_pkey" PRIMARY KEY ("analysisId","narrativeId")
);

-- AddForeignKey
ALTER TABLE "AnalysisToNarrative" ADD CONSTRAINT "AnalysisToNarrative_analysisId_fkey" FOREIGN KEY ("analysisId") REFERENCES "Analysis"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AnalysisToNarrative" ADD CONSTRAINT "AnalysisToNarrative_narrativeId_fkey" FOREIGN KEY ("narrativeId") REFERENCES "Narrative"("id") ON DELETE CASCADE ON UPDATE CASCADE;
