/*
  Warnings:

  - You are about to drop the column `sentimentAnalysisId` on the `User` table. All the data in the column will be lost.

*/
-- DropForeignKey
ALTER TABLE "User" DROP CONSTRAINT "User_sentimentAnalysisId_fkey";

-- AlterTable
ALTER TABLE "User" DROP COLUMN "sentimentAnalysisId";

-- CreateTable
CREATE TABLE "_SentimentAnalysisToUser" (
    "A" INTEGER NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_SentimentAnalysisToUser_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateIndex
CREATE INDEX "_SentimentAnalysisToUser_B_index" ON "_SentimentAnalysisToUser"("B");

-- AddForeignKey
ALTER TABLE "_SentimentAnalysisToUser" ADD CONSTRAINT "_SentimentAnalysisToUser_A_fkey" FOREIGN KEY ("A") REFERENCES "SentimentAnalysis"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_SentimentAnalysisToUser" ADD CONSTRAINT "_SentimentAnalysisToUser_B_fkey" FOREIGN KEY ("B") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
