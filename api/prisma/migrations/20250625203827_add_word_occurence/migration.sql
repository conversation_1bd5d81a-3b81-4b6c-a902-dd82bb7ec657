-- CreateTable
CREATE TABLE "WordOccurence" (
    "id" SERIAL NOT NULL,
    "word" TEXT NOT NULL,
    "source" "DataSource" NOT NULL,
    "sourceId" TEXT NOT NULL,
    "count" INTEGER NOT NULL DEFAULT 1,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "WordOccurence_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "WordOccurence_word_source_sourceId_key" ON "WordOccurence"("word", "source", "sourceId");
