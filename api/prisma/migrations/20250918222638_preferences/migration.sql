-- CreateTable
CREATE TABLE "public"."UserGroupPreference" (
    "userId" TEXT NOT NULL,
    "groupId" INTEGER NOT NULL,
    "notificationCadence" "public"."NotificationCadence" NOT NULL DEFAULT 'WEEKLY',
    "highRelevanceNotifications" "public"."NotificationCadence" NOT NULL DEFAULT 'OFF',
    "negativeSentimentNotifications" "public"."NotificationCadence" NOT NULL DEFAULT 'OFF'
);

-- CreateTable
CREATE TABLE "public"."UserTagPreference" (
    "userId" TEXT NOT NULL,
    "tagId" INTEGER NOT NULL,
    "notificationCadence" "public"."NotificationCadence" NOT NULL DEFAULT 'WEEKLY',
    "highRelevanceNotifications" "public"."NotificationCadence" NOT NULL DEFAULT 'OFF',
    "negativeSentimentNotifications" "public"."NotificationCadence" NOT NULL DEFAULT 'OFF'
);

-- CreateIndex
CREATE UNIQUE INDEX "UserGroupPreference_groupId_userId_key" ON "public"."UserGroupPreference"("groupId", "userId");

-- CreateIndex
CREATE UNIQUE INDEX "UserTagPreference_tagId_userId_key" ON "public"."UserTagPreference"("tagId", "userId");

-- AddForeignKey
ALTER TABLE "public"."UserGroupPreference" ADD CONSTRAINT "UserGroupPreference_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."UserGroupPreference" ADD CONSTRAINT "UserGroupPreference_groupId_fkey" FOREIGN KEY ("groupId") REFERENCES "public"."Group"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."UserTagPreference" ADD CONSTRAINT "UserTagPreference_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."UserTagPreference" ADD CONSTRAINT "UserTagPreference_tagId_fkey" FOREIGN KEY ("tagId") REFERENCES "public"."Tag"("id") ON DELETE CASCADE ON UPDATE CASCADE;
