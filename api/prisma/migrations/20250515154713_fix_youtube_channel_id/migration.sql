/*
  Warnings:

  - You are about to drop the column `channelid` on the `YoutubeVideo` table. All the data in the column will be lost.

*/
-- DropForeignKey
ALTER TABLE "YoutubeVideo" DROP CONSTRAINT "YoutubeVideo_channelid_fkey";

-- AlterTable
ALTER TABLE "YoutubeVideo" DROP COLUMN "channelid";

-- AddForeignKey
ALTER TABLE "YoutubeVideo" ADD CONSTRAINT "YoutubeVideo_channelId_fkey" FOREIGN KEY ("channelId") REFERENCES "YoutubeChannel"("id") ON DELETE SET NULL ON UPDATE CASCADE;
