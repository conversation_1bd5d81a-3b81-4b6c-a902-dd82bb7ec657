/*
  Warnings:

  - You are about to drop the column `rawCommentsJSON` on the `RedditPost` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE "RedditPost" DROP COLUMN "rawCommentsJSON";

-- CreateTable
CREATE TABLE "RedditComment" (
    "id" TEXT NOT NULL,
    "body" TEXT NOT NULL,
    "score" INTEGER,
    "parentId" TEXT,
    "postId" TEXT NOT NULL,

    CONSTRAINT "RedditComment_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "RedditComment" ADD CONSTRAINT "RedditComment_parentId_fkey" FOREIGN KEY ("parentId") REFERENCES "RedditComment"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RedditComment" ADD CONSTRAINT "RedditComment_postId_fkey" FOREIGN KEY ("postId") REFERENCES "RedditPost"("id") ON DELETE CASCADE ON UPDATE CASCADE;
