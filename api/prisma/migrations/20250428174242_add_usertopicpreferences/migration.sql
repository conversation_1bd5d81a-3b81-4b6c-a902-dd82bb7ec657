/*
  Warnings:

  - You are about to drop the column `negativeSentimentNotifications` on the `Topic` table. All the data in the column will be lost.
  - You are about to drop the column `notificationCadence` on the `Topic` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE "Topic" DROP COLUMN "negativeSentimentNotifications",
DROP COLUMN "notificationCadence";

-- CreateTable
CREATE TABLE "UserTopicPreference" (
    "topicId" INTEGER NOT NULL,
    "userId" TEXT NOT NULL,
    "negativeSentimentNotifications" BOOLEAN NOT NULL DEFAULT false,
    "notificationCadence" "NotificationCadence" NOT NULL DEFAULT 'MONTHLY',
    "isFavourite" BOOLEAN NOT NULL DEFAULT false
);

-- CreateIndex
CREATE UNIQUE INDEX "UserTopicPreference_topicId_userId_key" ON "UserTopicPreference"("topicId", "userId");

-- AddF<PERSON><PERSON><PERSON>ey
ALTER TABLE "UserTopicPreference" ADD CONSTRAINT "UserTopicPreference_topicId_fkey" FOREIGN KEY ("topicId") REFERENCES "Topic"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserTopicPreference" ADD CONSTRAINT "UserTopicPreference_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
