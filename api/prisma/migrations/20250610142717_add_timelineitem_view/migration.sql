CREATE VIEW "TimelineItem" AS
SELECT 
    "sentimentAnalysisId",
    "source",
    "sourceId",
    "likeCount",
    "publishedAt"
FROM
(
	SELECT
        a.id AS "sentimentAnalysisId",
        source,
        v.id AS "sourceId",
        "likeCount",
        "publishedAt"
	FROM "SentimentAnalysis" a
	JOIN "YoutubeVideo" v
		ON a."sourceId" = v.id
	WHERE a.source::text = 'YOUTUBE_VIDEO'
)
UNION
(
	SELECT
        a.id AS "sentimentAnalysisId",
        source,
        p.id AS "sourceId",
        "voteScore",
        "publishedAt"
	FROM "SentimentAnalysis" a
	JOIN "RedditPost" p
		ON a."sourceId" = p.id
	WHERE a.source::text = 'REDDIT_POST'
)
UNION
(
	SELECT
        a.id AS "sentimentAnalysisId",
        source,
        rc.id AS "sourceId",
        score AS "voteScore",
        "publishedAt"
	FROM "SentimentAnalysis" a
	JOIN "RedditComment" rc
		ON a."sourceId" = rc.id
	WHERE a.source::text = 'REDDIT_COMMENT'
)
UNION
(
	SELECT
        a.id AS "sentimentAnalysisId",
        source,
        ytc.id AS "sourceId",
        score AS "likeCount",
        "publishedAt"
	FROM "SentimentAnalysis" a
	JOIN "YoutubeComment" ytc
		ON a."sourceId" = ytc.id
	WHERE a.source::text = 'YOUTUBE_COMMENT'
)
;