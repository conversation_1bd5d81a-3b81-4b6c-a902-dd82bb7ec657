/*
  Warnings:

  - You are about to drop the column `topics` on the `Analysis` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE "Analysis" DROP COLUMN "topics";

-- CreateTable
CREATE TABLE "TopicsOnAnalysis" (
    "analysisId" INTEGER NOT NULL,
    "topicId" INTEGER NOT NULL,

    CONSTRAINT "TopicsOnAnalysis_pkey" PRIMARY KEY ("analysisId","topicId")
);

-- AddForeignKey
ALTER TABLE "TopicsOnAnalysis" ADD CONSTRAINT "TopicsOnAnalysis_analysisId_fkey" FOREIGN KEY ("analysisId") REFERENCES "Analysis"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TopicsOnAnalysis" ADD CONSTRAINT "TopicsOnAnalysis_topicId_fkey" FOREIGN KEY ("topicId") REFERENCES "Topic"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
