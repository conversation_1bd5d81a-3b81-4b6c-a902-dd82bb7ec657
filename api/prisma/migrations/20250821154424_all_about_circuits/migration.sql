-- AlterEnum
ALTER TYPE "public"."DataSource" ADD VALUE 'ALL_ABOUT_CIRCUITS';

-- CreateTable
CREATE TABLE "public"."AllAboutCircuitsForum" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "slug" TEXT NOT NULL,
    "url" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "AllAboutCircuitsForum_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."AllAboutCircuitsPost" (
    "id" TEXT NOT NULL,
    "author" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "text" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMES<PERSON>MP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "publishedAt" TIMESTAMP(3) NOT NULL,
    "viewCount" INTEGER NOT NULL DEFAULT 0,
    "url" TEXT NOT NULL,
    "forumId" INTEGER NOT NULL,

    CONSTRAINT "AllAboutCircuitsPost_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."AllAboutCircuitsPostReply" (
    "id" TEXT NOT NULL,
    "author" TEXT NOT NULL,
    "text" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "publishedAt" TIMESTAMP(3) NOT NULL,
    "postId" TEXT NOT NULL,
    "url" TEXT NOT NULL,

    CONSTRAINT "AllAboutCircuitsPostReply_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "AllAboutCircuitsForum_slug_key" ON "public"."AllAboutCircuitsForum"("slug");

-- AddForeignKey
ALTER TABLE "public"."AllAboutCircuitsPost" ADD CONSTRAINT "AllAboutCircuitsPost_forumId_fkey" FOREIGN KEY ("forumId") REFERENCES "public"."AllAboutCircuitsForum"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."AllAboutCircuitsPostReply" ADD CONSTRAINT "AllAboutCircuitsPostReply_postId_fkey" FOREIGN KEY ("postId") REFERENCES "public"."AllAboutCircuitsPost"("id") ON DELETE CASCADE ON UPDATE CASCADE;
