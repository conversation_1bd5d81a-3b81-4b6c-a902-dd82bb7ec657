/*
  Warnings:

  - The primary key for the `RedditComment` table will be changed. If it partially fails, the table could be left without primary key constraint.

*/
-- DropForeignKey
ALTER TABLE "public"."RedditComment" DROP CONSTRAINT "RedditComment_parentId_fkey";

-- DropIndex
DROP INDEX "public"."RedditComment_tenantId_id_key";

-- AlterTable
ALTER TABLE "public"."RedditComment" DROP CONSTRAINT "RedditComment_pkey",
ADD CONSTRAINT "RedditComment_pkey" PRIMARY KEY ("tenantId", "id");

-- AddForeignKey
ALTER TABLE "public"."RedditComment" ADD CONSTRAINT "RedditComment_tenantId_parentId_fkey" FOREIGN KEY ("tenantId", "parentId") REFERENCES "public"."RedditComment"("tenantId", "id") ON DELETE RESTRICT ON UPDATE CASCADE;
