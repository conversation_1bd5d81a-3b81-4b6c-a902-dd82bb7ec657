/*
  Warnings:

  - You are about to drop the `TopicsOnAnalysis` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "TopicsOnAnalysis" DROP CONSTRAINT "TopicsOnAnalysis_analysisId_fkey";

-- DropForeignKey
ALTER TABLE "TopicsOnAnalysis" DROP CONSTRAINT "TopicsOnAnalysis_topicId_fkey";

-- DropTable
DROP TABLE "TopicsOnAnalysis";

-- CreateTable
CREATE TABLE "_AnalysisToTopic" (
    "A" INTEGER NOT NULL,
    "B" INTEGER NOT NULL,

    CONSTRAINT "_AnalysisToTopic_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateIndex
CREATE INDEX "_AnalysisToTopic_B_index" ON "_AnalysisToTopic"("B");

-- AddForeignKey
ALTER TABLE "_AnalysisToTopic" ADD CONSTRAINT "_AnalysisToTopic_A_fkey" FOREIGN KEY ("A") REFERENCES "Analysis"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_AnalysisToTopic" ADD CONSTRAINT "_AnalysisToTopic_B_fkey" FOREIGN KEY ("B") REFERENCES "Topic"("id") ON DELETE CASCADE ON UPDATE CASCADE;
