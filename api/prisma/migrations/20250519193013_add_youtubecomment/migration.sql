/*
  Warnings:

  - You are about to drop the column `rawCommentsJSON` on the `YoutubeVideo` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE "YoutubeVideo" DROP COLUMN "rawCommentsJSON";

-- CreateTable
CREATE TABLE "YoutubeComment" (
    "id" TEXT NOT NULL,
    "body" TEXT NOT NULL,
    "score" INTEGER,
    "parentId" TEXT,
    "videoId" TEXT NOT NULL,

    CONSTRAINT "YoutubeComment_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "YoutubeComment" ADD CONSTRAINT "YoutubeComment_parentId_fkey" FOREIGN KEY ("parentId") REFERENCES "YoutubeComment"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "YoutubeComment" ADD CONSTRAINT "YoutubeComment_videoId_fkey" FOREIGN KEY ("videoId") REFERENCES "YoutubeVideo"("id") ON DELETE CASCADE ON UPDATE CASCADE;
