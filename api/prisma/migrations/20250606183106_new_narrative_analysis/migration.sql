-- CreateEnum
CREATE TYPE "NarrativeAspect" AS ENUM ('LEARNING_CURVE', 'EASE_OF_DEBUGGING', 'EASE_OF_USE', 'EASE_OF_INTEGRATION', 'DOCUMENTATION_QUALITY', 'PRICING', 'PERFORMANCE', 'CUSTOMER_SUPPORT', 'BUSINESS_PRACTICES', 'FEATURE_REQUEST', 'BUG');

-- AlterTable
ALTER TABLE "User" ADD COLUMN     "sentimentAnalysisId" INTEGER;

-- CreateTable
CREATE TABLE "SentimentAnalysis" (
    "id" SERIAL NOT NULL,
    "aspect" "NarrativeAspect" NOT NULL,
    "sentiment" INTEGER NOT NULL,
    "summary" TEXT NOT NULL,
    "publishedAt" TIMESTAMP(3) NOT NULL,
    "source" "DataSource" NOT NULL,
    "sourceId" TEXT NOT NULL,
    "topicId" INTEGER NOT NULL,

    CONSTRAINT "SentimentAnalysis_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "User" ADD CONSTRAINT "User_sentimentAnalysisId_fkey" FOREIGN KEY ("sentimentAnalysisId") REFERENCES "SentimentAnalysis"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SentimentAnalysis" ADD CONSTRAINT "SentimentAnalysis_topicId_fkey" FOREIGN KEY ("topicId") REFERENCES "Topic"("id") ON DELETE CASCADE ON UPDATE CASCADE;
