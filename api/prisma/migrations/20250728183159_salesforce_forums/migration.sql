/*
  Warnings:

  - You are about to drop the `_SalesforcePostToSalesforcePostReply` table. If the table is not empty, all the data it contains will be lost.
  - Added the required column `forumId` to the `SalesforcePost` table without a default value. This is not possible if the table is not empty.
  - Added the required column `url` to the `SalesforcePost` table without a default value. This is not possible if the table is not empty.
  - Added the required column `name` to the `SalesforcePostReply` table without a default value. This is not possible if the table is not empty.
  - Added the required column `postId` to the `SalesforcePostReply` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "SalesforceForum" DROP CONSTRAINT "SalesforceForum_salesforceSiteId_fkey";

-- DropForeignKey
ALTER TABLE "_SalesforcePostToSalesforcePostReply" DROP CONSTRAINT "_SalesforcePostToSalesforcePostReply_A_fkey";

-- DropForeignKey
ALTER TABLE "_SalesforcePostToSalesforcePostReply" DROP CONSTRAINT "_SalesforcePostToSalesforcePostReply_B_fkey";

-- AlterTable
ALTER TABLE "SalesforcePost" ADD COLUMN     "forumId" INTEGER NOT NULL,
ADD COLUMN     "guestDislikeCount" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "guestLikeCount" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "memberDislikeCount" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "memberLikeCount" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "url" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "SalesforcePostReply" ADD COLUMN     "name" TEXT NOT NULL,
ADD COLUMN     "postId" TEXT NOT NULL;

-- DropTable
DROP TABLE "_SalesforcePostToSalesforcePostReply";

-- AddForeignKey
ALTER TABLE "SalesforceForum" ADD CONSTRAINT "SalesforceForum_salesforceSiteId_fkey" FOREIGN KEY ("salesforceSiteId") REFERENCES "SalesforceSite"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SalesforcePost" ADD CONSTRAINT "SalesforcePost_forumId_fkey" FOREIGN KEY ("forumId") REFERENCES "SalesforceForum"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SalesforcePostReply" ADD CONSTRAINT "SalesforcePostReply_postId_fkey" FOREIGN KEY ("postId") REFERENCES "SalesforcePost"("id") ON DELETE CASCADE ON UPDATE CASCADE;
