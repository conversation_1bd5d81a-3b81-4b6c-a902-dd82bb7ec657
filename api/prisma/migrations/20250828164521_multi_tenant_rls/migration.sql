-- Create tenant table
CREATE TABLE "public"."Tenant" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Tenant_pkey" PRIMARY KEY ("id")
);

-- Add a default tenant
INSERT INTO "public"."Tenant" ("id", "name", "createdAt", "updatedAt") VALUES ('DEFAULT', 'Default tenant', now(), now());

-- Add _TenantToUser table
CREATE TABLE "public"."_TenantToUser" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_TenantToUser_AB_pkey" PRIMARY KEY ("A","B")
);

-- Add all existing users to default tenant
INSERT INTO "_TenantToUser" ("A", "B")
SELECT 'DEFAULT', id FROM "User";

-- Create new "app_user" role with restricted access
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_roles WHERE rolname = 'app_user') THEN
    CREATE ROLE app_user;
  END IF;
END
$$;

GRANT CONNECT ON DATABASE postgres TO app_user;

GRANT USAGE ON SCHEMA public TO app_user;

GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO app_user;
GRANT USAGE, SELECT, UPDATE ON ALL SEQUENCES IN SCHEMA public TO app_user;
GRANT MAINTAIN ON "TimelineItem" TO app_user;

ALTER DEFAULT PRIVILEGES IN SCHEMA public
  GRANT SELECT, INSERT, UPDATE, DELETE, MAINTAIN ON TABLES TO app_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public
GRANT USAGE, SELECT, UPDATE ON SEQUENCES TO app_user;

-- Add tenantId columns 
ALTER TABLE "public"."Topic"                   ADD COLUMN     "tenantId" TEXT;
ALTER TABLE "public"."Tag"                     ADD COLUMN     "tenantId" TEXT;
ALTER TABLE "public"."Group"                   ADD COLUMN     "tenantId" TEXT;
ALTER TABLE "public"."Analysis"                ADD COLUMN     "tenantId" TEXT;
ALTER TABLE "public"."Narrative"               ADD COLUMN     "tenantId" TEXT;
ALTER TABLE "public"."SearchableEntity"        ADD COLUMN     "tenantId" TEXT;
ALTER TABLE "public"."Subreddit"               ADD COLUMN     "tenantId" TEXT;
ALTER TABLE "public"."RedditPost"              ADD COLUMN     "tenantId" TEXT;
ALTER TABLE "public"."RedditComment"           ADD COLUMN     "tenantId" TEXT;
ALTER TABLE "public"."YoutubeChannel"          ADD COLUMN     "tenantId" TEXT;
ALTER TABLE "public"."YoutubeVideo"            ADD COLUMN     "tenantId" TEXT;
ALTER TABLE "public"."YoutubeComment"          ADD COLUMN     "tenantId" TEXT;
ALTER TABLE "public"."SalesforceSite"          ADD COLUMN     "tenantId" TEXT;
ALTER TABLE "public"."AllAboutCircuitsForum"   ADD COLUMN     "tenantId" TEXT;

-- Add existing records to DEFAULT tenant
UPDATE "public"."Topic"                   SET "tenantId" = 'DEFAULT';
UPDATE "public"."Tag"                     SET "tenantId" = 'DEFAULT';
UPDATE "public"."Group"                   SET "tenantId" = 'DEFAULT';
UPDATE "public"."Analysis"                SET "tenantId" = 'DEFAULT';
UPDATE "public"."Narrative"               SET "tenantId" = 'DEFAULT';
UPDATE "public"."SearchableEntity"        SET "tenantId" = 'DEFAULT';
UPDATE "public"."Subreddit"               SET "tenantId" = 'DEFAULT';
UPDATE "public"."RedditPost"              SET "tenantId" = 'DEFAULT';
UPDATE "public"."RedditComment"           SET "tenantId" = 'DEFAULT';
UPDATE "public"."YoutubeChannel"          SET "tenantId" = 'DEFAULT';
UPDATE "public"."YoutubeVideo"            SET "tenantId" = 'DEFAULT';
UPDATE "public"."YoutubeComment"          SET "tenantId" = 'DEFAULT';
UPDATE "public"."SalesforceSite"          SET "tenantId" = 'DEFAULT';
UPDATE "public"."AllAboutCircuitsForum"   SET "tenantId" = 'DEFAULT';

-- Make tenantId non-nullable for new records
ALTER TABLE "public"."Topic"                   ALTER COLUMN "tenantId" SET NOT NULL;
ALTER TABLE "public"."Tag"                     ALTER COLUMN "tenantId" SET NOT NULL;
ALTER TABLE "public"."Group"                   ALTER COLUMN "tenantId" SET NOT NULL;
ALTER TABLE "public"."Analysis"                ALTER COLUMN "tenantId" SET NOT NULL;
ALTER TABLE "public"."Narrative"               ALTER COLUMN "tenantId" SET NOT NULL;
ALTER TABLE "public"."SearchableEntity"        ALTER COLUMN "tenantId" SET NOT NULL;
ALTER TABLE "public"."Subreddit"               ALTER COLUMN "tenantId" SET NOT NULL;
ALTER TABLE "public"."RedditPost"              ALTER COLUMN "tenantId" SET NOT NULL;
ALTER TABLE "public"."RedditComment"           ALTER COLUMN "tenantId" SET NOT NULL;
ALTER TABLE "public"."YoutubeChannel"          ALTER COLUMN "tenantId" SET NOT NULL;
ALTER TABLE "public"."YoutubeVideo"            ALTER COLUMN "tenantId" SET NOT NULL;
ALTER TABLE "public"."YoutubeComment"          ALTER COLUMN "tenantId" SET NOT NULL;
ALTER TABLE "public"."SalesforceSite"          ALTER COLUMN "tenantId" SET NOT NULL;
ALTER TABLE "public"."AllAboutCircuitsForum"   ALTER COLUMN "tenantId" SET NOT NULL;

-- Enable RLS
ALTER TABLE "public"."User"                    ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."Topic"                   ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."Tag"                     ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."Group"                   ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."Analysis"                ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."Narrative"               ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."SearchableEntity"        ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."Subreddit"               ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."RedditPost"              ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."RedditComment"           ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."YoutubeChannel"          ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."YoutubeVideo"            ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."YoutubeComment"          ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."SalesforceSite"          ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."AllAboutCircuitsForum"   ENABLE ROW LEVEL SECURITY;

-- Add RLS policies
CREATE POLICY tenant_isolation ON "Topic"                 USING ("tenantId" = current_setting('app.tenant_id'));
CREATE POLICY tenant_isolation ON "Tag"                   USING ("tenantId" = current_setting('app.tenant_id'));
CREATE POLICY tenant_isolation ON "Group"                 USING ("tenantId" = current_setting('app.tenant_id'));
CREATE POLICY tenant_isolation ON "Analysis"              USING ("tenantId" = current_setting('app.tenant_id'));
CREATE POLICY tenant_isolation ON "Narrative"             USING ("tenantId" = current_setting('app.tenant_id'));
CREATE POLICY tenant_isolation ON "SearchableEntity"      USING ("tenantId" = current_setting('app.tenant_id'));
CREATE POLICY tenant_isolation ON "Subreddit"             USING ("tenantId" = current_setting('app.tenant_id'));
CREATE POLICY tenant_isolation ON "RedditPost"            USING ("tenantId" = current_setting('app.tenant_id'));
CREATE POLICY tenant_isolation ON "RedditComment"         USING ("tenantId" = current_setting('app.tenant_id'));
CREATE POLICY tenant_isolation ON "YoutubeChannel"        USING ("tenantId" = current_setting('app.tenant_id'));
CREATE POLICY tenant_isolation ON "YoutubeVideo"          USING ("tenantId" = current_setting('app.tenant_id'));
CREATE POLICY tenant_isolation ON "YoutubeComment"        USING ("tenantId" = current_setting('app.tenant_id'));
CREATE POLICY tenant_isolation ON "SalesforceSite"        USING ("tenantId" = current_setting('app.tenant_id'));
CREATE POLICY tenant_isolation ON "AllAboutCircuitsForum" USING ("tenantId" = current_setting('app.tenant_id'));
CREATE POLICY tenant_isolation ON "User"
USING (
  EXISTS (
    SELECT 1 FROM "_TenantToUser"
    WHERE "A" = current_setting('app.tenant_id') AND "B" = "User".id
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM "_TenantToUser"
    WHERE "A" = current_setting('app.tenant_id') AND "B" = "User".id
  )
);

-- Set default tenantId to current_setting('app.tenant_id')
ALTER TABLE "public"."Topic"                   ALTER COLUMN "tenantId" SET DEFAULT current_setting('app.tenant_id');
ALTER TABLE "public"."Tag"                     ALTER COLUMN "tenantId" SET DEFAULT current_setting('app.tenant_id');
ALTER TABLE "public"."Group"                   ALTER COLUMN "tenantId" SET DEFAULT current_setting('app.tenant_id');
ALTER TABLE "public"."Analysis"                ALTER COLUMN "tenantId" SET DEFAULT current_setting('app.tenant_id');
ALTER TABLE "public"."Narrative"               ALTER COLUMN "tenantId" SET DEFAULT current_setting('app.tenant_id');
ALTER TABLE "public"."SearchableEntity"        ALTER COLUMN "tenantId" SET DEFAULT current_setting('app.tenant_id');
ALTER TABLE "public"."Subreddit"               ALTER COLUMN "tenantId" SET DEFAULT current_setting('app.tenant_id');
ALTER TABLE "public"."RedditPost"              ALTER COLUMN "tenantId" SET DEFAULT current_setting('app.tenant_id');
ALTER TABLE "public"."RedditComment"           ALTER COLUMN "tenantId" SET DEFAULT current_setting('app.tenant_id');
ALTER TABLE "public"."YoutubeChannel"          ALTER COLUMN "tenantId" SET DEFAULT current_setting('app.tenant_id');
ALTER TABLE "public"."YoutubeVideo"            ALTER COLUMN "tenantId" SET DEFAULT current_setting('app.tenant_id');
ALTER TABLE "public"."YoutubeComment"          ALTER COLUMN "tenantId" SET DEFAULT current_setting('app.tenant_id');
ALTER TABLE "public"."SalesforceSite"          ALTER COLUMN "tenantId" SET DEFAULT current_setting('app.tenant_id');
ALTER TABLE "public"."AllAboutCircuitsForum"   ALTER COLUMN "tenantId" SET DEFAULT current_setting('app.tenant_id');

-- Drop "Tag" table "isActive" column
ALTER TABLE "public"."Tag" DROP COLUMN "isActive";

-- Update TimelineItem view
DROP MATERIALIZED VIEW "TimelineItem";

CREATE MATERIALIZED VIEW "TimelineItem" AS
SELECT 
    "tenantId",
    "analysisId",
    "source",
    "sourceId",
    "likeCount",
    "commentCount",
    "publishedAt"
FROM
(
	SELECT
        a."tenantId" AS "tenantId",
        a.id AS "analysisId",
        source,
        v.id AS "sourceId",
        "likeCount",
        "commentCount",
        "publishedAt"
	FROM "Analysis" a
	JOIN "YoutubeVideo" v
		ON a."sourceId" = v.id
	WHERE a.source::text = 'YOUTUBE_VIDEO'
)
UNION
(
	SELECT
        a."tenantId" AS "tenantId",
        a.id AS "analysisId",
        source,
        p.id AS "sourceId",
        "voteScore",
        "commentCount",
        "publishedAt"
	FROM "Analysis" a
	JOIN "RedditPost" p
		ON a."sourceId" = p.id
	WHERE a.source::text = 'REDDIT_POST'
)
UNION
(
	SELECT
        a."tenantId" AS "tenantId",
        a.id AS "analysisId",
        source,
        rc.id AS "sourceId",
        score AS "voteScore",
        0 as "commentCount",
        "publishedAt"
	FROM "Analysis" a
	JOIN "RedditComment" rc
		ON a."sourceId" = rc.id
	WHERE a.source::text = 'REDDIT_COMMENT'
)
UNION
(
	SELECT
        a."tenantId" AS "tenantId",
        a.id AS "analysisId",
        source,
        ytc.id AS "sourceId",
        score AS "likeCount",
        0 as "commentCount",
        "publishedAt"
	FROM "Analysis" a
	JOIN "YoutubeComment" ytc
		ON a."sourceId" = ytc.id
	WHERE a.source::text = 'YOUTUBE_COMMENT'
)
UNION
(
	SELECT
        a."tenantId" AS "tenantId",
        a.id AS "analysisId",
        source,
        sfp.id AS "sourceId",
        0 AS "likeCount",
        0 AS"commentCount",
        "publishedAt"
	FROM "Analysis" a
	JOIN "SalesforcePost" sfp
		ON a."sourceId" = sfp.id
	WHERE a.source = 'AVR_FREAKS' or a.source = 'MICROCHIP_CLASSIC'
)
UNION
(
	SELECT
        a."tenantId" AS "tenantId",
        a.id AS "analysisId",
        source,
        aacp.id AS "sourceId",
        0 AS "likeCount",
        "replyCount" AS "commentCount",
        "publishedAt"
	FROM "Analysis" a
	JOIN "AllAboutCircuitsPost" aacp
		ON a."sourceId" = aacp.id
	WHERE a.source = 'ALL_ABOUT_CIRCUITS'
)
;