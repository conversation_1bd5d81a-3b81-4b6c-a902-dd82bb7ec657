/*
  Warnings:

  - You are about to drop the column `topicId` on the `SentimentAnalysis` table. All the data in the column will be lost.

*/
-- DropForeignKey
ALTER TABLE "SentimentAnalysis" DROP CONSTRAINT "SentimentAnalysis_topicId_fkey";

-- AlterTable
ALTER TABLE "SentimentAnalysis" DROP COLUMN "topicId";

-- CreateTable
CREATE TABLE "_SentimentAnalysisToTopic" (
    "A" INTEGER NOT NULL,
    "B" INTEGER NOT NULL,

    CONSTRAINT "_SentimentAnalysisToTopic_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateIndex
CREATE INDEX "_SentimentAnalysisToTopic_B_index" ON "_SentimentAnalysisToTopic"("B");

-- AddForeignKey
ALTER TABLE "_SentimentAnalysisToTopic" ADD CONSTRAINT "_SentimentAnalysisToTopic_A_fkey" FOREIGN KEY ("A") REFERENCES "SentimentAnalysis"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_SentimentAnalysisToTopic" ADD CONSTRAINT "_SentimentAnalysisToTopic_B_fkey" FOREIGN KEY ("B") REFERENCES "Topic"("id") ON DELETE CASCADE ON UPDATE CASCADE;
