/*
  Warnings:

  - A unique constraint covering the columns `[source,sourceId]` on the table `SearchableEntity` will be added. If there are existing duplicate values, this will fail.

  This migration will first remove any duplicate rows, keeping only the row with the highest id for each (source, sourceId) pair.
*/

-- Remove duplicates, keeping only the row with the highest id for each (source, sourceId)
DELETE FROM "SearchableEntity"
WHERE id NOT IN (
  SELECT MAX(id)
  FROM "SearchableEntity"
  GROUP BY source, "sourceId"
);

-- CreateIndex
CREATE UNIQUE INDEX "SearchableEntity_source_sourceId_key" ON "SearchableEntity"("source", "sourceId");
