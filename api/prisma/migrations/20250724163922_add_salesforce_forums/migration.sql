-- AlterEnum
-- This migration adds more than one value to an enum.
-- With PostgreSQL versions 11 and earlier, this is not possible
-- in a single migration. This can be worked around by creating
-- multiple migrations, each migration adding only one value to
-- the enum.


ALTER TYPE "DataSource" ADD VALUE 'AVR_FREAKS';
ALTER TYPE "DataSource" ADD VALUE 'MICROCHIP_CLASSIC';

-- CreateTable
CREATE TABLE "SalesforceSite" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "SalesforceSite_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SalesforceForum" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "salesforceSiteId" INTEGER NOT NULL,

    CONSTRAINT "SalesforceForum_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SalesforcePost" (
    "id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "text" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "publishedAt" TIMESTAMP(3) NOT NULL,
    "viewCount" INTEGER NOT NULL DEFAULT 0,

    CONSTRAINT "SalesforcePost_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SalesforcePostReply" (
    "id" TEXT NOT NULL,
    "text" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "publishedAt" TIMESTAMP(3) NOT NULL,
    "memberLikeCount" INTEGER NOT NULL DEFAULT 0,
    "memberDislikeCount" INTEGER NOT NULL DEFAULT 0,
    "guestLikeCount" INTEGER NOT NULL DEFAULT 0,
    "guestDislikeCount" INTEGER NOT NULL DEFAULT 0,

    CONSTRAINT "SalesforcePostReply_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "_SalesforcePostToSalesforcePostReply" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_SalesforcePostToSalesforcePostReply_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateIndex
CREATE UNIQUE INDEX "SalesforceSite_name_key" ON "SalesforceSite"("name");

-- CreateIndex
CREATE UNIQUE INDEX "SalesforceForum_salesforceSiteId_name_key" ON "SalesforceForum"("salesforceSiteId", "name");

-- CreateIndex
CREATE INDEX "_SalesforcePostToSalesforcePostReply_B_index" ON "_SalesforcePostToSalesforcePostReply"("B");

-- AddForeignKey
ALTER TABLE "SalesforceForum" ADD CONSTRAINT "SalesforceForum_salesforceSiteId_fkey" FOREIGN KEY ("salesforceSiteId") REFERENCES "SalesforceSite"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_SalesforcePostToSalesforcePostReply" ADD CONSTRAINT "_SalesforcePostToSalesforcePostReply_A_fkey" FOREIGN KEY ("A") REFERENCES "SalesforcePost"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_SalesforcePostToSalesforcePostReply" ADD CONSTRAINT "_SalesforcePostToSalesforcePostReply_B_fkey" FOREIGN KEY ("B") REFERENCES "SalesforcePostReply"("id") ON DELETE CASCADE ON UPDATE CASCADE;
