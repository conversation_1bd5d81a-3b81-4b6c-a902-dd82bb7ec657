-- CreateEnum
CREATE TYPE "Sentiment" AS ENUM ('POSITIVE', 'NEGATIVE');

-- AlterTable
ALTER TABLE "RedditPost" ADD COLUMN     "analysisId" INTEGER;

-- AlterTable
ALTER TABLE "YoutubeVideo" ADD COLUMN     "analysisId" INTEGER;

-- CreateTable
CREATE TABLE "Analysis" (
    "id" SERIAL NOT NULL,
    "sentiment" "Sentiment" NOT NULL,
    "youtubeVideoId" TEXT,
    "redditPostId" TEXT,

    CONSTRAINT "Analysis_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Analysis_youtubeVideoId_key" ON "Analysis"("youtubeVideoId");

-- CreateIndex
CREATE UNIQUE INDEX "Analysis_redditPostId_key" ON "Analysis"("redditPostId");

-- AddForeignKey
ALTER TABLE "Analysis" ADD CONSTRAINT "Analysis_youtubeVideoId_fkey" FOREIGN KEY ("youtubeVideoId") REFERENCES "YoutubeVideo"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Analysis" ADD CONSTRAINT "Analysis_redditPostId_fkey" FOREIGN KEY ("redditPostId") REFERENCES "RedditPost"("id") ON DELETE SET NULL ON UPDATE CASCADE;
