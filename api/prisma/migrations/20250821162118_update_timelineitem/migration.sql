DROP MATERIALIZED VIEW "TimelineItem";

CREATE MATERIALIZED VIEW "TimelineItem" AS
SELECT 
    "analysisId",
    "source",
    "sourceId",
    "likeCount",
    "publishedAt"
FROM
(
	SELECT
        a.id AS "analysisId",
        source,
        v.id AS "sourceId",
        "likeCount",
        "publishedAt"
	FROM "Analysis" a
	JOIN "YoutubeVideo" v
		ON a."sourceId" = v.id
	WHERE a.source::text = 'YOUTUBE_VIDEO'
)
UNION
(
	SELECT
        a.id AS "analysisId",
        source,
        p.id AS "sourceId",
        "voteScore",
        "publishedAt"
	FROM "Analysis" a
	JOIN "RedditPost" p
		ON a."sourceId" = p.id
	WHERE a.source::text = 'REDDIT_POST'
)
UNION
(
	SELECT
        a.id AS "analysisId",
        source,
        rc.id AS "sourceId",
        score AS "voteScore",
        "publishedAt"
	FROM "Analysis" a
	JOIN "RedditComment" rc
		ON a."sourceId" = rc.id
	WHERE a.source::text = 'REDDIT_COMMENT'
)
UNION
(
	SELECT
        a.id AS "analysisId",
        source,
        ytc.id AS "sourceId",
        score AS "likeCount",
        "publishedAt"
	FROM "Analysis" a
	JOIN "YoutubeComment" ytc
		ON a."sourceId" = ytc.id
	WHERE a.source::text = 'YOUTUBE_COMMENT'
)
UNION
(
	SELECT
        a.id AS "analysisId",
        source,
        sfp.id AS "sourceId",
        0 AS "likeCount",
        "publishedAt"
	FROM "Analysis" a
	JOIN "SalesforcePost" sfp
		ON a."sourceId" = sfp.id
	WHERE a.source = 'AVR_FREAKS' or a.source = 'MICROCHIP_CLASSIC'
)
UNION
(
	SELECT
        a.id AS "analysisId",
        source,
        aacp.id AS "sourceId",
        0 AS "likeCount",
        "publishedAt"
	FROM "Analysis" a
	JOIN "AllAboutCircuitsPost" aacp
		ON a."sourceId" = aacp.id
	WHERE a.source = 'ALL_ABOUT_CIRCUITS'
)
;