-- CreateTable
CREATE TABLE "_AnalysisToUser" (
    "A" INTEGER NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_AnalysisToUser_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateIndex
CREATE INDEX "_AnalysisToUser_B_index" ON "_AnalysisToUser"("B");

-- AddForeignKey
ALTER TABLE "_AnalysisToUser" ADD CONSTRAINT "_AnalysisToUser_A_fkey" FOREIGN KEY ("A") REFERENCES "Analysis"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_AnalysisToUser" ADD CONSTRAINT "_AnalysisToUser_B_fkey" FOREIGN KEY ("B") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
