/*
  Warnings:

  - The primary key for the `Share` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `sentimentAnalysisId` on the `Share` table. All the data in the column will be lost.
  - You are about to drop the `SentimentAnalysis` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `_SentimentAnalysisToTopic` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `_SentimentAnalysisToUser` table. If the table is not empty, all the data it contains will be lost.
  - Added the required column `analysisId` to the `Share` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "SentimentAnalysis" DROP CONSTRAINT "SentimentAnalysis_narrativeId_fkey";

-- DropForeignKey
ALTER TABLE "Share" DROP CONSTRAINT "Share_sentimentAnalysisId_fkey";

-- DropForeignKey
ALTER TABLE "_SentimentAnalysisToTopic" DROP CONSTRAINT "_SentimentAnalysisToTopic_A_fkey";

-- DropForeignKey
ALTER TABLE "_SentimentAnalysisToTopic" DROP CONSTRAINT "_SentimentAnalysisToTopic_B_fkey";

-- DropForeignKey
ALTER TABLE "_SentimentAnalysisToUser" DROP CONSTRAINT "_SentimentAnalysisToUser_A_fkey";

-- DropForeignKey
ALTER TABLE "_SentimentAnalysisToUser" DROP CONSTRAINT "_SentimentAnalysisToUser_B_fkey";

-- AlterTable
ALTER TABLE "Share" DROP CONSTRAINT "Share_pkey",
DROP COLUMN "sentimentAnalysisId",
ADD COLUMN     "analysisId" INTEGER NOT NULL,
ADD CONSTRAINT "Share_pkey" PRIMARY KEY ("analysisId", "groupId", "userId");

-- DropTable
DROP TABLE "SentimentAnalysis";

-- DropTable
DROP TABLE "_SentimentAnalysisToTopic";

-- DropTable
DROP TABLE "_SentimentAnalysisToUser";

-- CreateTable
CREATE TABLE "Analysis" (
    "id" SERIAL NOT NULL,
    "aspect" "NarrativeAspect" NOT NULL,
    "sentiment" INTEGER NOT NULL,
    "sentimentReasoning" TEXT,
    "summary" TEXT NOT NULL,
    "relevance" INTEGER,
    "relevanceReasoning" TEXT,
    "longSummary" TEXT,
    "isActionable" BOOLEAN NOT NULL DEFAULT false,
    "actionableReasoning" TEXT,
    "tipsAndActions" TEXT,
    "source" "DataSource" NOT NULL,
    "sourceId" TEXT NOT NULL,
    "narrativeId" INTEGER,

    CONSTRAINT "Analysis_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "_AnalysisToTopic" (
    "A" INTEGER NOT NULL,
    "B" INTEGER NOT NULL,

    CONSTRAINT "_AnalysisToTopic_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_AnalysisToUser" (
    "A" INTEGER NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_AnalysisToUser_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateIndex
CREATE UNIQUE INDEX "Analysis_sourceId_key" ON "Analysis"("sourceId");

-- CreateIndex
CREATE INDEX "_AnalysisToTopic_B_index" ON "_AnalysisToTopic"("B");

-- CreateIndex
CREATE INDEX "_AnalysisToUser_B_index" ON "_AnalysisToUser"("B");

-- AddForeignKey
ALTER TABLE "Analysis" ADD CONSTRAINT "Analysis_narrativeId_fkey" FOREIGN KEY ("narrativeId") REFERENCES "Narrative"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Share" ADD CONSTRAINT "Share_analysisId_fkey" FOREIGN KEY ("analysisId") REFERENCES "Analysis"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_AnalysisToTopic" ADD CONSTRAINT "_AnalysisToTopic_A_fkey" FOREIGN KEY ("A") REFERENCES "Analysis"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_AnalysisToTopic" ADD CONSTRAINT "_AnalysisToTopic_B_fkey" FOREIGN KEY ("B") REFERENCES "Topic"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_AnalysisToUser" ADD CONSTRAINT "_AnalysisToUser_A_fkey" FOREIGN KEY ("A") REFERENCES "Analysis"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_AnalysisToUser" ADD CONSTRAINT "_AnalysisToUser_B_fkey" FOREIGN KEY ("B") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
