-- AlterTable
ALTER TABLE "public"."Tag" ADD COLUMN     "createdByUserId" TEXT,
ADD COLUMN     "isPersonal" BOOLEAN NOT NULL DEFAULT false;

-- AddForeignKey
ALTER TABLE "public"."Tag" ADD CONSTRAINT "Tag_createdByUserId_fkey" FOREIGN KEY ("createdByUserId") REFERENCES "public"."User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- DropIndex
DROP INDEX "public"."Tag_tenantId_name_key";

-- Manually enforce uniqueness
CREATE UNIQUE INDEX tag_user_unique_name
  ON "Tag" ("tenantId", "createdByUserId", "name")
  WHERE "isPersonal" = true;

CREATE UNIQUE INDEX tag_global_unique_name
  ON "Tag" ("tenantId", "name")
  WHERE "isPersonal" = false;
