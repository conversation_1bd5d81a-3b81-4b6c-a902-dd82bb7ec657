/*
  Warnings:

  - You are about to drop the column `subreddit` on the `RedditPost` table. All the data in the column will be lost.
  - You are about to drop the column `channelHandle` on the `YoutubeVideo` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE "RedditPost" DROP COLUMN "subreddit",
ADD COLUMN     "subredditName" TEXT;

-- AlterTable
ALTER TABLE "YoutubeVideo" DROP COLUMN "channelHandle",
ADD COLUMN     "channelId" TEXT,
ADD COLUMN     "channelid" TEXT;

-- CreateTable
CREATE TABLE "YoutubeChannel" (
    "id" TEXT NOT NULL,
    "channelHandle" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "YoutubeChannel_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Subreddit" (
    "name" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL
);

-- CreateIndex
CREATE UNIQUE INDEX "YoutubeChannel_channelHandle_key" ON "YoutubeChannel"("channelHandle");

-- CreateIndex
CREATE UNIQUE INDEX "Subreddit_name_key" ON "Subreddit"("name");

-- AddForeignKey
ALTER TABLE "YoutubeVideo" ADD CONSTRAINT "YoutubeVideo_channelid_fkey" FOREIGN KEY ("channelid") REFERENCES "YoutubeChannel"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RedditPost" ADD CONSTRAINT "RedditPost_subredditName_fkey" FOREIGN KEY ("subredditName") REFERENCES "Subreddit"("name") ON DELETE SET NULL ON UPDATE CASCADE;
