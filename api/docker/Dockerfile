# Install dependencies and generate Prisma client
FROM node:22-slim AS node_modules-builder
WORKDIR /usr/src/app
COPY package*.json ./
RUN npm ci --omit=dev
COPY prisma prisma
RUN npm run prisma:generate

# Build
FROM node:22-slim AS builder
WORKDIR /usr/src/app
COPY --from=node_modules-builder /usr/src/app/node_modules ./node_modules
COPY . .
RUN npm run build

# Runner
FROM node:22-slim

RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        openssl \
        curl \
        ca-certificates \
    && rm -rf /var/lib/apt/lists/*

RUN curl -L https://github.com/yt-dlp/yt-dlp/releases/latest/download/yt-dlp_linux \
    -o /usr/local/bin/yt-dlp && \
    chmod a+rx /usr/local/bin/yt-dlp

WORKDIR /usr/src/app

COPY --from=builder /usr/src/app/dist ./dist
COPY --from=builder /usr/src/app/node_modules ./node_modules
COPY --from=builder /usr/src/app/prisma ./prisma
COPY --from=builder /usr/src/app/tsconfig.json ./tsconfig.json
COPY --from=builder /usr/src/app/package*.json ./
COPY --from=builder /usr/src/app/docker/entrypoint.sh ./docker/entrypoint.sh

EXPOSE 3000

ENTRYPOINT [ "./docker/entrypoint.sh" ]
