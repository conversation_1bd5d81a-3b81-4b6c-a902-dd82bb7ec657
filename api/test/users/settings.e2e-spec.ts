import { INestApplication } from "@nestjs/common";

import * as supertest from "supertest";
import TestAgent from "supertest/lib/agent";

import { URL_BASE } from "../constants";
import { createApp, createAndOnboardNewUser, stopApp } from "../util";

describe("User settings", () => {
    let app: INestApplication;

    beforeAll(async () => {
        app = await createApp();
    });

    afterAll(async () => {
        await stopApp(app);
    });

    describe("PUT /users/me/password", () => {
        let userSession: TestAgent;
        let email;
        let currentPassword;

        beforeEach(async () => {
            const user = await createAndOnboardNewUser(URL_BASE);
            email = user.email;
            currentPassword = user.password;

            userSession = supertest.agent(URL_BASE);

            await userSession
                .post("/login")
                .send({ username: email, password: currentPassword })
                .expect(204);
        });

        afterEach(() => {
            currentPassword = undefined;
            userSession = undefined;
        });

        it("Validates existing password", async () => {
            // Try to set the new password
            const newPassword = "Str0ngP@55";
            const response = await userSession
                .put("/users/me/password")
                .send({ currentPassword: "bad value", newPassword })
                .expect(400);

            expect(response.body).toMatchObject({
                message: [
                    {
                        field: "currentPassword",
                    },
                ],
            });

            await userSession.post("/logout").expect(204);

            // Try to use the new password (unsuccessfully)
            await userSession
                .post("/login")
                .send({ username: email, password: newPassword })
                .expect(401);
        });

        it("Validates password strength", async () => {
            // Try to set the new password
            const newPassword = "password";
            const response = await userSession
                .put("/users/me/password")
                .send({ currentPassword, newPassword: "bad value" })
                .expect(400);

            expect(response.body).toMatchObject({
                message: [
                    {
                        field: "newPassword",
                    },
                ],
            });

            await userSession.post("/logout").expect(204);

            // Try to use the new password (unsuccessfully)
            await userSession
                .post("/login")
                .send({ username: email, password: newPassword })
                .expect(401);
        });

        it("Updates password", async () => {
            // Try to set the new password
            const newPassword = "Str0ngP@55";
            await userSession
                .put("/users/me/password")
                .send({ currentPassword, newPassword })
                .expect(204);

            await userSession.post("/logout").expect(204);

            // Use the new password
            await userSession
                .post("/login")
                .send({ username: email, password: newPassword })
                .expect(204);
        });
    });
});
