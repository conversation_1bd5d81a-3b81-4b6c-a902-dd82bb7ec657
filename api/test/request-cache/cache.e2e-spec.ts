import { INestApplication } from "@nestjs/common";

import { PrismaClient } from "@prisma/client";
import { randomUUID } from "crypto";

import { createApp, loginAsAdmin } from "../util";

describe("Request cache", () => {
    let app: INestApplication;
    let prisma: PrismaClient;

    beforeAll(async () => {
        app = await createApp();
        prisma = new PrismaClient();
    });

    afterAll(async () => {
        await app.close();
        await prisma.$disconnect();
    });

    it("should clear cache entries matching the pattern", async () => {
        const testId = randomUUID();

        // Create two cache entries
        const keyId1 = randomUUID();
        const key1 = `test-${keyId1}-${testId}`;
        await prisma.requestCache.create({
            data: {
                key: key1,
                value: "test",
                metadata: "",
            },
        });

        const keyId2 = randomUUID();
        const key2 = `test-${keyId2}-${testId}`;
        await prisma.requestCache.create({
            data: {
                key: key2,
                value: "test",
                metadata: "",
            },
        });

        // Clear cache entries matching the pattern
        const adminSession = await loginAsAdmin();
        const deleteResponse = await adminSession
            .delete(`/cache?pattern=${key1}`)
            .expect(200);

        expect(deleteResponse.body).toMatchObject({
            deletedCount: 1,
        });

        // Check only second entry exists
        const cacheEntries = await prisma.requestCache.findMany({
            where: {
                key: {
                    contains: testId,
                },
            },
        });

        expect(cacheEntries).toMatchObject([
            {
                key: key2,
            },
        ]);
    });
});
