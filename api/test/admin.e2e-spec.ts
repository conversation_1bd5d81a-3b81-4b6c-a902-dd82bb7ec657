import { INestApplication } from "@nestjs/common";

import { URL_BASE } from "./constants";
import { createApp, login, loginAsAdmin, stopApp } from "./util";

describe("Admin", () => {
    let app: INestApplication;

    beforeAll(async () => {
        app = await createApp();
    });

    afterAll(async () => {
        await stopApp(app);
    });

    it("GET /users 403", async () => {
        const request = await login(URL_BASE);
        await request.get("/users").expect(403);
    });

    it("GET /users 200", async () => {
        const request = await loginAsAdmin(URL_BASE);
        const allUsersResponse = await request.get("/users").expect(200);

        expect(allUsersResponse.body.items.length).toBeGreaterThan(0);
        expect(allUsersResponse.body.items[0]).toHaveProperty("id");
        expect(allUsersResponse.body.items[0]).toHaveProperty("email");
    });
});
