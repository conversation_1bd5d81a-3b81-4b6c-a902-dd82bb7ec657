import { randomUUID } from "crypto";
import { SalesforceGetTopicsParams } from "src/salesforce-api/types";

import { SalesforceApiServiceInterface } from "../../src/salesforce-api/salesforce-api.service.interface";

export class MockSalesforceApiService implements SalesforceApiServiceInterface {
    async getAccessToken(): Promise<unknown> {
        return {
            access_token:
                "00DWD000005bV3a!AQEAQHZ.J_YE4Y8tQjDvQOZn4l.u5fxsPY7uoSpAFcCwm3Oq9H8AVr8WeFqfzBVLmF5lZtE7BgYztB2UzKIPUhjd.jn_fy5Y",
        };
    }

    async getForums(): Promise<unknown> {
        return {
            RecordCount: 197,
            records: [
                {
                    createdDate: "2023-02-24T21:02:18.000Z",
                    forumTopics: null,
                    forumPosts: null,
                    forumParentName: null,
                    forumLevel: "Site",
                    forumName: "AVR Freaks",
                    id: "a553l000000J2puAAC",
                },
                {
                    createdDate: "2023-02-24T21:02:18.000Z",
                    forumTopics: 72843,
                    forumPosts: 423946,
                    forumParentName: "AVR Freaks",
                    forumLevel: "Forum",
                    forumName: "AVR Tools Miscellaneous",
                    id: "a553l000000J2pvAAC",
                },
                {
                    createdDate: "2023-02-24T21:02:18.000Z",
                    forumTopics: 2767,
                    forumPosts: 11088,
                    forumParentName: "AVR Tools Miscellaneous",
                    forumLevel: "Sub Forum",
                    forumName: "Off Topic",
                    id: "a553l000000J2pwAAC",
                },
            ],
        };
    }

    async getTopics(
        _accessToken: string,
        params: SalesforceGetTopicsParams,
    ): Promise<unknown> {
        // Don't return a second page of results
        if (params.lastId)
            return {
                hasMore: false,
                pageSize: 2000,
                recordcount: 0,
                records: [],
            };

        const topicId = randomUUID();

        return {
            nextRecordsUrl:
                "https://microchip--mcuat.sandbox.my.salesforce.com/services/apexrest/api/getForumTopics/?pageSize=2000&lastId=a5C3l000000BqTXEA0",
            hasMore: false,
            pageSize: 2000,
            recordcount: 1,
            records: [
                {
                    topicViewCount: 505,
                    topicTitle:
                        "Cannot send anything to Data Visualizer from PIC16F18855",
                    tags: null,
                    url: '<a href="https://microchip--mcuat.sandbox.my.site.com/microchip/s/topic/a5C3l0000003iob" target="_blank">New Cannot send anything to Data Visualizer from PIC16F18855</a>',
                    userScreenName: "84Vic",
                    createdDate: "2023-03-28T09:28:35.000Z",
                    name: "T-389629",
                    topicid: topicId,
                    userId: "0053l00000KOw8bAAD",
                    forumUserSite: "Microchip Classic",
                    forumUserJoinDate: "2023-03-16T05:06:25.000Z",
                    forumUserFirstName: "Thomas",
                    forumUserExperienceLevel: null,
                    forumUserEmail: "<EMAIL>",
                    isforumUserActive: false,
                    forumUserId: "a543l00000098uMAAQ",
                },
            ],
        };
    }

    async getPosts(): Promise<unknown> {
        const postId1 = randomUUID();
        const postId2 = randomUUID();

        return {
            nextRecordsUrl:
                "https://microchip--mcuat.sandbox.my.salesforce.com/services/apexrest/api/getAllTopicPosts?pageSize=1&lastId=a583l0000000x1SAAQ",
            hasMore: true,
            pageSize: 1,
            recordcount: 2809908,
            records: [
                {
                    isStarterPost: true,
                    isMarkedAsSolution: false,
                    replyFrom: null,
                    guestDislikeCount: 1,
                    memberDislikeCount: 2,
                    guestLikeCount: 10,
                    memberLikeCount: 20,
                    userScreenName: "71Str",
                    textContent:
                        "<p>I am an electronics hobbyist currently moving away from Arduino UNO and Arduino IDE towards atmega328 and C. I am using WinAVR (I know it is old) and USBasp as programmer. I can write code, compile and make hex. files in WinAVR without any problems. I can program the atmega328 using Command Prompt or AVRDUDESS, again without any problems.  When I try to program from Programmer&#39;s Notepad in WinAVR, I get the following message: No rule to make target &#39;program&#39; Stop. Any suggestions? Thank you for your help.</p>",
                    createdDate: "2023-01-18T22:50:14.000Z",
                    name: "P-1643776",
                    postid: postId1,
                    userId: "0053l00000K00OeAAJ",
                    forumUserSite: "AVR Freaks",
                    forumUserJoinDate: "2023-01-18T22:24:15.000Z",
                    userRanking: "New Member",
                    isforumUserActive: false,
                    forumUserId: "a543l0000000PzcAAE",
                },
                {
                    isStarterPost: false,
                    isMarkedAsSolution: false,
                    replyFrom: null,
                    guestDislikeCount: 10,
                    memberDislikeCount: 9,
                    guestLikeCount: 8,
                    memberLikeCount: 2,
                    userScreenName: "ka7ehk",
                    textContent:
                        "<p>Cliff -</p><p><br></p><p>Thanks for the alternate site information. May just try it out. I did watch reddit for a while very early on, but got a creepy feel about the place. Then, it seemed mostly about job hunting, and that was not my thing. </p><p><br></p><p>Jim</p>",
                    createdDate: "2023-01-18T22:20:28.000Z",
                    name: "P-1643774",
                    postid: postId2,
                    userId: "0051N000005VcXLQA0",
                    forumUserSite: "AVR Freaks",
                    forumUserJoinDate: "2002-11-22T18:31:39.000Z",
                    forumUserFirstName: "Jim",
                    forumUserExperienceLevel: null,
                    forumUserEmail: "<EMAIL>",
                    isforumUserActive: true,
                    forumUserId: "a543l000000CjCOAA0",
                },
            ],
        };
    }
}
