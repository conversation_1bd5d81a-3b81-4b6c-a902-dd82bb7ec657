import { randomUUID } from "crypto";

import { RedditApiService } from "../../src/reddit-api/reddit-api.service";
import {
    CommaSeparatedRedditPostFullnames,
    RedditPostFullname,
} from "../../src/reddit-api/types";
import { RawComments } from "../../src/reddit/util";

const FAKE_ACCESS_TOKEN = "123abc.456xyz";

export class MockRedditApiService extends RedditApiService {
    async getAccessToken() {
        const authenticationResponse = { access_token: FAKE_ACCESS_TOKEN };
        return new Response(JSON.stringify(authenticationResponse));
    }

    async getPosts() {
        const topPostsResponse = {
            data: {
                children: [
                    {
                        data: {
                            id: `post-${randomUUID()}`,
                            title: "test post",
                            selftext: "test post selftext",
                            created_utc: new Date().valueOf() / 1000,
                            score: 500,
                            num_comments: 123,
                        },
                    },
                ],
            },
        };
        return new Response(JSON.stringify(topPostsResponse));
    }

    async getComments() {
        const commentsResponse: RawComments = [
            {
                kind: "Listing",
                data: {
                    children: [
                        {
                            kind: "t3",
                            data: {
                                selftext: `selftext ${randomUUID()}`,
                            },
                        },
                    ],
                },
            },
            {
                kind: "Listing",
                data: {
                    children: [
                        {
                            kind: "t1",
                            data: {
                                id: randomUUID(),
                                body: `comment ${randomUUID()}`,
                                created_utc: new Date().valueOf() / 1000,
                                author: "test-user-1",
                                score: -1,
                                reply_count: 2,
                                replies: {
                                    kind: "Listing",
                                    data: {
                                        children: [
                                            {
                                                kind: "t1",
                                                data: {
                                                    id: randomUUID(),
                                                    kind: "t1",
                                                    body: `comment reply ${randomUUID()}`,
                                                    created_utc:
                                                        new Date().valueOf() /
                                                        1000,
                                                    author: "test-user-2",
                                                    score: 1,
                                                    reply_count: 0,
                                                },
                                            },
                                            {
                                                kind: "t1",
                                                data: {
                                                    id: randomUUID(),
                                                    kind: "t1",
                                                    body: `comment reply ${randomUUID()}`,
                                                    created_utc:
                                                        new Date().valueOf() /
                                                        1000,
                                                    author: "test-user-3",
                                                    score: 9,
                                                    reply_count: 0,
                                                },
                                            },
                                        ],
                                    },
                                },
                            },
                        },
                    ],
                },
            },
        ];

        return new Response(JSON.stringify(commentsResponse));
    }

    async getPostInfo(
        _accessToken: string,
        idList: CommaSeparatedRedditPostFullnames,
    ) {
        const ids = idList.split(",");
        const response = {
            data: {
                children: ids.map((id: RedditPostFullname) => ({
                    data: {
                        id: id.slice(3),
                        title: "test post",
                        selftext: "test post selftext",
                        created_utc: new Date().valueOf() / 1000,
                        score: 500,
                        num_comments: 123,
                    },
                })),
            },
        };
        return new Response(JSON.stringify(response));
    }
}
