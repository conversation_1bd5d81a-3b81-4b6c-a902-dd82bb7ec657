import { youtube_v3 } from "@googleapis/youtube";
import { randomUUID } from "crypto";
import { GaxiosResponse } from "gaxios";

import { YoutubeApiService } from "../../src/youtube-api/youtube-api.service";

export class MockYoutubeApiService extends YoutubeApiService {
    async searchChannels(chanelHandle: string) {
        return {
            config: {},
            status: 200,
            statusText: "",
            headers: {},
            request: { responseURL: "" },
            data: {
                items: [
                    {
                        chanelHandle,
                        id: `YTID_${chanelHandle}`,
                        contentDetails: {
                            relatedPlaylists: {
                                uploads: randomUUID(),
                            },
                        },
                    },
                ],
            },
        };
    }

    async searchVideos() {
        return {
            config: {},
            status: 200,
            statusText: "",
            headers: {},
            request: { responseURL: "" },
            data: {
                items: [
                    {
                        snippet: {
                            title: "test title",
                            publishedAt: new Date().toISOString(),
                            resourceId: {
                                videoId: randomUUID(),
                            },
                        },
                    },
                ],
            },
        };
    }

    async getCommentThreads(
        youtubeId: string,
    ): Promise<GaxiosResponse<youtube_v3.Schema$CommentThreadListResponse>> {
        const commentId = randomUUID();
        const replyId = randomUUID();

        return {
            config: {},
            status: 200,
            statusText: "",
            headers: {},
            request: { responseURL: "" },
            data: {
                items: [
                    {
                        snippet: {
                            totalReplyCount: 1,
                            topLevelComment: {
                                id: commentId,
                                snippet: {
                                    videoId: youtubeId,
                                    textOriginal: "Test comment",
                                    textDisplay: "Test comment",
                                    publishedAt: new Date().toISOString(),
                                    likeCount: 10,
                                },
                            },
                        },
                        replies: {
                            comments: [
                                {
                                    id: replyId,
                                    snippet: {
                                        videoId: youtubeId,
                                        parentId: commentId,
                                        textOriginal: "Test reply",
                                        textDisplay: "Test reply",
                                        publishedAt: new Date().toISOString(),
                                        likeCount: -2,
                                    },
                                },
                            ],
                        },
                    },
                ],
            },
        };
    }

    async getVideoListingWithStatistics(
        videoId: string,
    ): Promise<GaxiosResponse<youtube_v3.Schema$VideoListResponse>> {
        return {
            config: {},
            status: 200,
            statusText: "",
            headers: {},
            request: { responseURL: "" },
            data: {
                items: [
                    {
                        id: videoId,
                        statistics: {
                            viewCount: "45",
                            likeCount: "800",
                            commentCount: "42",
                            favoriteCount: "5",
                        },
                    },
                ],
            },
        };
    }
}
