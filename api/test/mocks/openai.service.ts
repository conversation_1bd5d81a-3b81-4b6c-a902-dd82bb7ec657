import { PrismaClient } from "@prisma/client";
import { ChatCompletion, CreateEmbeddingResponse } from "openai/resources";
import { ParsedResponse } from "openai/resources/responses/responses";

import { OpenaiServiceInterface } from "../../src/openai/openai-service.interface";

const MOCK_EMBEDDING_RESPONSE: CreateEmbeddingResponse = {
    object: "list",
    data: [
        {
            object: "embedding",
            embedding: Array(1536).fill(0.1),
            index: 0,
        },
    ],
    model: "text-embedding-3-small",
    usage: {
        prompt_tokens: 0,
        total_tokens: 0,
    },
};

export class MockOpenaiService implements OpenaiServiceInterface {
    private prisma: PrismaClient;

    async onModuleInit() {
        this.prisma = new PrismaClient();
    }

    async onModuleDestroy() {
        await this.prisma.$disconnect();
    }

    async getCompletion(): Promise<ChatCompletion> {
        const topic = await this.prisma.topic.findFirst({
            orderBy: {
                updatedAt: "desc",
            },
        });

        return {
            id: "",
            choices: [
                {
                    finish_reason: "stop",
                    index: 0,
                    logprobs: null,
                    message: {
                        role: "assistant",
                        refusal: null,
                        content: JSON.stringify({
                            longSummary: "mock long summary",
                            summary: "mock summary",
                            topics: [topic?.name ?? "UNKNOWN"],
                            tenant: "mock microchip explanation",
                            narratives: [
                                {
                                    topic: topic?.name ?? "UNKNOWN",
                                    aspect: "EASE_OF_DEBUGGING",
                                    sentiment: {
                                        reasoning: "mock sentiment reasoning",
                                        score: 75,
                                    },
                                },
                            ],
                            sentiment: {
                                reasoning: "mock sentiment reasoning",
                                score: 75,
                            },
                            relevanceReasoning: "mock relevance reasoning",
                            relevance: 90,
                            actionableReasoning: "mock actionable reasoning",
                            isActionable: true,
                            tipsAndActions: "mock tips and actions",
                        }),
                    },
                },
            ],
            model: "",
            created: 0,
            object: "chat.completion",
        };
    }

    async getEmbedding(): Promise<CreateEmbeddingResponse> {
        return MOCK_EMBEDDING_RESPONSE;
    }

    async createResponse<T>(): Promise<ParsedResponse<T>> {
        return undefined;
    }
}
