import { AllAboutCircuitsS3ServiceInterface } from "../../src/all-about-circuits-s3/all-about-circuits-s3-service.interface";

export class MockAllAboutCircuitsS3Service
    implements AllAboutCircuitsS3ServiceInterface
{
    async getForums() {
        return JSON.stringify([
            {
                slug: "general-electronics-chat.5",
                name: "General Electronics Chat",
                url: "https://forum.allaboutcircuits.com/forums/general-electronics-chat.5/",
            },
        ]);
    }

    async getForumsLatestPost() {
        return JSON.stringify({
            "general-electronics-chat.5": new Date().toISOString(),
        });
    }

    async getThreadsMetadata() {
        return JSON.stringify([
            {
                thread_title: "Electronic Tricks and Tips Y2025",
                thread_url:
                    "https://forum.allaboutcircuits.com/threads/electronic-tricks-and-tips-y2025.204710/",
                author: "<PERSON>",
                replies: 2,
                views: 1000,
                start_date: "2025-01-01T16:10:30-0500",
                last_post: new Date().toISOString(),
            },
        ]);
    }

    async getThread() {
        return JSON.stringify({
            thread_url:
                "https://forum.allaboutcircuits.com/threads/electronic-tricks-and-tips-y2025.204710/",
            thread_title: "Electronic Tricks and Tips Y2025",
            post_count: 3,
            posts: [
                {
                    index: 0,
                    author: "Wendy",
                    timestamp: "2025-01-01T16:10:30-0500",
                    content:
                        "This is the seventh thread with this name I have indexed the first 2 in my personal index, Wendy's Index . To keep it from being closed prematurely, I am setting the following rules:\n1. Do not comment just for the sake of posting. Only posts containing a trick or tip will be allowed. This thread will be heavily moderated, anything posted here that do not meet this criteria in the opinion of the moderators will be deleted. If you have a question start your own thread. If you really like the idea use the \"like\" tag (button).\n2. Any repeats of someone else's ideas from this or the previous tricks and tips threads will also be deleted.\n3. At the end of this year This thread will be locked and a new one started next year.\nBe aware of my comment about being heavily moderated. This thread is not meant to be a dumping ground as has happened in the past.\nElectronic Tricks and Tips Y2008\nElectronic Tricks and Tips Y2012\nElectronic Tricks and Tips Y2022\nElectronic Tricks and tips Y2023\nElectronic  Tricks and tips Y2024",
                    post_url:
                        "https://forum.allaboutcircuits.com/threads/electronic-tricks-and-tips-y2025.204710/#post-1958130",
                },
                {
                    index: 1,
                    author: "Jon Chandler",
                    timestamp: "2025-02-13T01:26:03-0500",
                    content:
                        "I added a couple projects to Jon's Imaginarium you might find useful.\nThe first is\n3D Printer Project: Hot End Inspection Mirror\n.  With my recently acquired used 3D printer, I've learned that the hot end may need cleaning when things don't go as planned.  I designed an inspection mirror that makes it easy to see the hot end from a comfortable position.  I've designed two versions, adding an LED to the first version for illumination.  STL and (my crude) OpenSCAD files are available to download.\nThe second article is for\nA Dremel Bit Organizer\nusing corrugated plastic.  It's simple to make and accommodates bits of all sizes.\nI hope you'll find these useful.",
                    post_url:
                        "https://forum.allaboutcircuits.com/threads/electronic-tricks-and-tips-y2025.204710/#post-1967021",
                },
                {
                    index: 2,
                    author: "Wendy",
                    timestamp: "2025-07-20T23:59:07-0400",
                    content:
                        'When making a electronics enclosure box with my 3D printer I use this I found new technique for me to make better ones:\nI quick note about my personal 3  printer preferences, for flat sheets  I use 0.08" (2mm) strong or 0.04" (1mm) weak but adequate for many jobs.. The side pieces  have amounting rail 0.25" high recessed the 0.08" as shown. I use ¼" x #4/40 counter sunk screws as the box fasteners The holes shown on the side piece are 0.1" holes which is the tap size for # 4/40 machine screws, simply screw trhe screws into holes and the make there treads in the soft plastic, the screws can be unscrewed at whim to open things up.\nAfter you plan the top, bottom. front, and back dimensions print with no after you know the dimensions of the top and bottom plates. the only tolerance adjustment is reduce the top an bottom plates by 0.05" on their x,y dimensions. the screw holes are 0.125" from the edges.\n​\n.........................................................................................................................................................................................\nSides\nThis case use used for my\nType K Thermocouple Thermometer\nwhich turned out pretty well if I do say so myself. Definitely a technique I will be using in the future.',
                    post_url:
                        "https://forum.allaboutcircuits.com/threads/electronic-tricks-and-tips-y2025.204710/#post-1993111",
                },
            ],
        });
    }
}
