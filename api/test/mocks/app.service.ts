import { AppServiceInterface } from "../../src/app-service.interface";

export class MockAppService implements AppServiceInterface {
    private isDbHealthy = true;
    private isRedisHealthy = true;

    setIsDbHealthly(isHealthy: boolean) {
        this.isDbHealthy = isHealthy;
    }

    setIsRedisHealthly(isHealthy: boolean) {
        this.isRedisHealthy = isHealthy;
    }

    async isDatabaseConnectionHealthy(): Promise<boolean> {
        return this.isDbHealthy;
    }

    async isRedisConnectionHealthy(): Promise<boolean> {
        return this.isRedisHealthy;
    }
}
