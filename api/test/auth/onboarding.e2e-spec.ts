import { INestApplication } from "@nestjs/common";

import { randomUUID } from "crypto";
import * as supertest from "supertest";

import { URL_BASE } from "../constants";
import {
    createAndOnboardNewUser,
    createApp,
    createNewUser,
    loginAsAdmin,
    readLatestEmailTo,
    stopApp,
    waitFor,
} from "../util";

describe("Onboarding", () => {
    let app: INestApplication;
    let dateNowSpy: jest.SpyInstance;

    beforeAll(async () => {
        app = await createApp();
    });

    afterAll(async () => {
        if (dateNowSpy) dateNowSpy.mockRestore();
        await stopApp(app);
    });

    it("PUT /onboarding 204", async () => {
        // Create a new user
        const adminSession = await loginAsAdmin(URL_BASE);
        const email = `user-${randomUUID()}@example.com`;
        await adminSession
            .post("/users")
            .set({ origin: URL_BASE })
            .send({
                firstName: "First",
                lastName: "Last",
                email,
            })
            .expect(201);

        // Wait for invitation email
        const invitationEmail = await waitFor(() => readLatestEmailTo(email));
        const [link] = invitationEmail.text.match(/http(s?):\/\/\S+/);
        const signupCode = new URL(link).searchParams.get("code");
        const userId = new URL(link).searchParams.get("userId");

        // Complete the onboarding with a strong password
        const request = supertest.agent(URL_BASE);
        const password = "StrongP@ssword123";
        await request
            .put(`/users/${userId}/onboarding/${signupCode}`)
            .send({
                password,
            })
            .expect(204);

        // Log in as the new user
        await request
            .post("/login")
            .send({ username: email, password })
            .expect(204);
    });

    it("GET /onboarding 404", async () => {
        // Try to access a non-existent onboarding code
        const request = supertest.agent(URL_BASE);
        await request.get(`/users/user123/onboarding/123456`).expect(404);
    });

    it("GET /onboarding 200", async () => {
        // Create a new user
        const { email, userId, signupCode } = await createNewUser(URL_BASE, {
            firstName: "Firsty",
            lastName: "McLasty",
        });

        // Access onboarding resource
        const request = supertest.agent(URL_BASE);
        const onboarding = await request
            .get(`/users/${userId}/onboarding/${signupCode}`)
            .expect(200);

        expect(onboarding.body.email).toEqual(email);
        expect(onboarding.body.firstName).toEqual("Firsty");
        expect(onboarding.body.lastName).toEqual("McLasty");
    });

    it("GET /onboarding 404 - consumed code", async () => {
        const { password, userId, signupCode } =
            await createAndOnboardNewUser(URL_BASE);

        // Try to access the onboarding code again
        const request = supertest.agent(URL_BASE);
        await request
            .get(`/users/${userId}/onboarding/${signupCode}`)
            .send({
                password,
            })
            .expect(404);
    });

    it("GET /onboarding 404 - expired code", async () => {
        const { userId, signupCode } = await createNewUser(URL_BASE);

        const thirtyDaysFromNow = Date.now() + 30 * 24 * 60 * 60 * 1000;
        dateNowSpy = jest.spyOn(Date, "now").mockReturnValue(thirtyDaysFromNow);

        // Try to use the onboarding code
        const request = supertest.agent(URL_BASE);
        await request
            .get(`/users/${userId}/onboarding/${signupCode}`)
            .expect(404);
    });

    it("PUT /onboarding 404", async () => {
        const request = supertest.agent(URL_BASE);

        // Try to signup with a non-existent onboarding code
        await request
            .put(`/users/user123/onboarding/123456`)
            .send({
                password: "STRONGpassword123!",
            })
            .expect(404);
    });

    it("PUT /onboarding 400", async () => {
        const { userId, signupCode } = await createNewUser(URL_BASE);

        // Check that password validation is performed
        const request = supertest.agent(URL_BASE);
        await request
            .put(`/users/${userId}/onboarding/${signupCode}`)
            .send({
                password: "weak",
            })
            .expect(400);
    });

    it("PUT /onboarding 404 - consumed code", async () => {
        const { userId, signupCode, password } =
            await createAndOnboardNewUser(URL_BASE);

        // Try to use the same onboarding code again
        const request = supertest.agent(URL_BASE);
        await request
            .put(`/users/${userId}/onboarding/${signupCode}`)
            .send({
                password,
            })
            .expect(404);
    });

    it("PUT /onboarding 404 - expired code", async () => {
        const { userId, signupCode } = await createNewUser(URL_BASE);

        const thirtyDaysFromNow = Date.now() + 30 * 24 * 60 * 60 * 1000;
        dateNowSpy = jest.spyOn(Date, "now").mockReturnValue(thirtyDaysFromNow);

        // Try to use the same onboarding code again
        const request = supertest.agent(URL_BASE);
        const password = "strongPas3s!#";
        await request
            .put(`/users/${userId}/onboarding/${signupCode}`)
            .send({
                password,
            })
            .expect(404);
    });

    it("POST /login 401 - pending onboarding", async () => {
        const { email } = await createNewUser(URL_BASE);

        // Log in as the new user with empty password before onboarding is complete
        const request = supertest.agent(URL_BASE);
        await request
            .post("/login")
            .send({ username: email, password: "" })
            .expect(401);
    });
});
