import { INestApplication } from "@nestjs/common";

import { randomUUID } from "crypto";
import * as supertest from "supertest";

import { URL_BASE } from "../constants";
import {
    readLatestEmailTo,
    waitFor,
    stopApp,
    createAndOnboardNewUser,
    createNewUser,
    createApp,
} from "../util";

describe("Password Reset", () => {
    let app: INestApplication;
    let dateNowSpy: jest.SpyInstance;

    beforeAll(async () => {
        app = await createApp();
    });

    afterAll(async () => {
        if (dateNowSpy) dateNowSpy.mockRestore();
        await stopApp(app);
    });

    it(`GET /forgot-password 404 - expired verification`, async () => {
        const { email } = await createAndOnboardNewUser(URL_BASE);

        // Request password reset
        const request = await supertest.agent(URL_BASE);
        await request
            .post("/forgot-password")
            .send({ username: email })
            .expect(201);

        // Read verification code from email
        const code = await waitFor(async () => {
            const sentEmail = await readLatestEmailTo(email);
            const [code] = sentEmail.text.match(/\w\w\w\w-\w\w\w\w/);
            return code;
        });

        // Move clock forward a week
        const oneWeekFromNow = Date.now() + 7 * 24 * 60 * 60 * 1000;
        dateNowSpy = jest.spyOn(Date, "now").mockReturnValue(oneWeekFromNow);

        await request
            .get(`/forgot-password?username=${email}&code=${code}`)
            .expect(404);
    });

    it("POST /auth/set-password 201", async () => {
        const firstName = `first-${randomUUID()}`;
        const lastName = `last-${randomUUID()}`;
        const { email, password: oldPassword } = await createAndOnboardNewUser(
            URL_BASE,
            { firstName, lastName },
        );

        // Request password reset
        const request = await supertest.agent(URL_BASE);
        await request
            .post("/forgot-password")
            .send({ username: email })
            .expect(201);

        // Read verification code from email
        const code = await waitFor(async () => {
            const sentEmail = await readLatestEmailTo(email);
            const [code] = sentEmail.text.match(/\w\w\w\w-\w\w\w\w/);
            return code;
        });

        // Check that the code is valid
        const forgotPasswordResponse = await request
            .get(`/forgot-password?username=${email}&code=${code}`)
            .expect(200);
        expect(forgotPasswordResponse.body).toEqual({
            email,
            firstName,
            lastName,
        });

        // Set the password
        const newPassword = `St_r0ngp@$$`;
        await request
            .post("/set-password")
            .send({
                email,
                code,
                password: newPassword,
            })
            .expect(201);

        // The code is no longer valid
        await request
            .get(`/forgot-password?username=${email}&code=${code}`)
            .expect(404);

        // The code can no longer be used to set the password
        await request
            .post("/set-password")
            .send({
                email: "<EMAIL>",
                code: "123-abc",
                password: "St_r0ngp@$$",
            })
            .expect(404);

        // Log in with the new password
        await request
            .post("/login")
            .send({ username: email, password: newPassword })
            .expect(204);

        // Logging in with the old password no longer works
        await request
            .post("/login")
            .send({ username: email, password: oldPassword })
            .expect(401);
    });

    it("Case insensitive email", async () => {
        const { email } = await createAndOnboardNewUser(URL_BASE);

        const emailWithDifferentCase = `uSeR` + email.slice(4);

        // Request password reset
        const request = await supertest.agent(URL_BASE);
        await request
            .post("/forgot-password")
            .send({ username: emailWithDifferentCase })
            .expect(201);

        // Read verification code from email
        const code = await waitFor(async () => {
            const sentEmail = await readLatestEmailTo(email);
            const [code] = sentEmail.text.match(/\w\w\w\w-\w\w\w\w/);
            return code;
        });

        // Check that the code is valid
        await request
            .get(`/forgot-password?username=${email}&code=${code}`)
            .expect(200);
    });

    describe("User in onboarding state", () => {
        it("sends a new onboarding link", async () => {
            const { email } = await createNewUser(URL_BASE);

            // Get original onboarding link
            const invitationEmail = await readLatestEmailTo(email);
            const [link] = invitationEmail.text.match(/http(s?):\/\/\S+/);
            const signupCode = new URL(link).searchParams.get("code");
            const userId = new URL(link).searchParams.get("userId");

            // Request a password reset
            const request = await supertest.agent(URL_BASE);
            await request
                .post("/forgot-password")
                .send({ username: email })
                .expect(201);

            // Check that a new onboarding email is sent
            await waitFor(async () => {
                const newInvitationEmail = await readLatestEmailTo(email);
                const [link] =
                    newInvitationEmail.text.match(/http(s?):\/\/\S+/);
                const newSignupCode = new URL(link).searchParams.get("code");
                const newUserId = new URL(link).searchParams.get("userId");

                expect(newSignupCode).not.toEqual(signupCode);
                expect(newUserId).toEqual(userId);
                return true;
            });
        });
    });
});
