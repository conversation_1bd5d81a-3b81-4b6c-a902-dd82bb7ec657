import { INestApplication } from "@nestjs/common";

import * as supertest from "supertest";

import { URL_BASE } from "../constants";
import {
    createAndOnboardNewUser,
    createApp,
    login,
    parseSetCookieHeaders,
    stopApp,
} from "../util";

describe("Logout", () => {
    let app: INestApplication;

    beforeAll(async () => {
        app = await createApp();
    });

    afterAll(async () => {
        await stopApp(app);
    });

    it("/logout 401", async () => {
        const request = await supertest.agent(URL_BASE);

        request.post("/logout").expect(401);
    });

    it("logout 204 - for authenticated user", async () => {
        const { email, password } = await createAndOnboardNewUser();

        // Login
        const request = supertest.agent(URL_BASE);
        const loginResponse = await request
            .post("/login")
            .send({ username: email, password })
            .expect(204);

        const [authCookie, refreshCookie] =
            parseSetCookieHeaders(loginResponse);

        // Logout
        const logoutResponse = await supertest
            .agent(URL_BASE)
            .post("/logout")
            .set(
                "Cookie",
                `auth=${authCookie.value}; refresh=${refreshCookie.value}`,
            )
            .expect(204);

        // Auth and refresh cookies are cleared
        const [newAuthCookie, newRefreshCookie] =
            parseSetCookieHeaders(logoutResponse);
        expect(newAuthCookie.value).toBeFalsy();
        expect(newRefreshCookie.value).toBeFalsy();

        // Refresh token is invalidated
        await supertest
            .agent(URL_BASE)
            .post("/refresh")
            .set("Cookie", `refresh=${refreshCookie.value}`)
            .expect(401);
    });

    it("logout doesn't log out everyone else", async () => {
        // Log in two users
        const firstSession = await login();
        const secondSession = await login();

        // First user logs out
        await firstSession.post("/logout").expect(204);

        // Second user can still refresh their auth token
        await secondSession.post("/refresh").expect(204);
    });
});
