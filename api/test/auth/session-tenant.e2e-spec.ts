import { INestApplication } from "@nestjs/common";

import { randomUUID } from "crypto";
import * as jwt from "jsonwebtoken";
import * as supertest from "supertest";

import { URL_BASE } from "../constants";
import { createApp, parseSetCookieHeaders, stopApp } from "../util";

describe("Session tenant", () => {
    let app: INestApplication;

    beforeAll(async () => {
        app = await createApp();
    });

    afterAll(async () => {
        await stopApp(app);
    });

    it("PUT /users/me/session-tenant", async () => {
        const session = supertest.agent(URL_BASE);

        // Login as the super admin
        const loginResponse = await session.post("/login").send({
            username: process.env["ADMIN_USER"],
            password: process.env["ADMIN_PASS"],
        });

        // Check current session tenant
        let authCookie;
        let refreshCookie;
        [authCookie, refreshCookie] = parseSetCookieHeaders(loginResponse);
        const originalSessionTenantId = jwt.decode(authCookie.value)[
            "tenantId"
        ];
        expect(jwt.decode(refreshCookie.value)["tenantId"]).toBe(
            originalSessionTenantId,
        );

        // Create a new tenant (super admin is implicitly added as a user)
        const tenantResponse = await session.post("/tenants").send({
            name: `tenant-${randomUUID()}`,
        });
        const newTenantId = tenantResponse.body.id;

        // Logout
        await session.post("/logout").expect(204);

        // Login again
        const secondLoginResponse = await session.post("/login").send({
            username: process.env["ADMIN_USER"],
            password: process.env["ADMIN_PASS"],
        });

        // Session tenant is unchanged
        [authCookie, refreshCookie] =
            parseSetCookieHeaders(secondLoginResponse);
        expect(jwt.decode(authCookie.value)["tenantId"]).toBe(
            originalSessionTenantId,
        );
        expect(jwt.decode(refreshCookie.value)["tenantId"]).toBe(
            originalSessionTenantId,
        );

        // Explicitly change session tenant
        const setSessionTenantResponse = await session
            .put(`/session-tenant`)
            .send({
                tenantId: newTenantId,
            })
            .expect(204);

        // Session tenant is changed
        [authCookie, refreshCookie] = parseSetCookieHeaders(
            setSessionTenantResponse,
        );
        expect(jwt.decode(authCookie.value)["tenantId"]).toBe(newTenantId);
        expect(jwt.decode(refreshCookie.value)["tenantId"]).toBe(newTenantId);

        // Refresh auth token
        const refreshResponse = await session.post("/refresh").expect(204);
        [authCookie] = parseSetCookieHeaders(refreshResponse);
        expect(jwt.decode(authCookie.value)["tenantId"]).toBe(newTenantId);
    });
});
