import { INestApplication } from "@nestjs/common";

import * as supertest from "supertest";

import { URL_BASE } from "../constants";
import { createAndOnboardNewUser, createApp, stopApp } from "../util";

describe("Login", () => {
    let app: INestApplication;

    beforeAll(async () => {
        app = await createApp();
    });

    afterAll(async () => {
        await stopApp(app);
    });

    it("case insensitive email field", async () => {
        const { email, password } = await createAndOnboardNewUser();
        const emailWithDifferentCase = `uSeR` + email.slice(4);
        const request = supertest.agent(URL_BASE);
        await request.post("/dne").expect(404);
        await request
            .post("/login")
            .send({ username: emailWithDifferentCase, password })
            .expect(204);
    });
});
