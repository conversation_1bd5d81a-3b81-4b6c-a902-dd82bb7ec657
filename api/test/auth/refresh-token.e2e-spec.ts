import { INestApplication } from "@nestjs/common";

import * as jwt from "jsonwebtoken";
import * as supertest from "supertest";

import { URL_BASE } from "../constants";
import {
    createAndOnboardNewUser,
    createApp,
    parseSetCookieHeaders,
    stopApp,
} from "../util";

describe("Refresh Token", () => {
    let app: INestApplication;

    beforeAll(async () => {
        app = await createApp();
    });

    afterAll(async () => {
        await stopApp(app);
    });

    it("POST /refresh 401 - for unauthenticated user", async () => {
        await supertest.agent(URL_BASE).post("/refresh").expect(401);
    });

    it("POST /refresh 204 - for authenticated user", async () => {
        const { email, password } = await createAndOnboardNewUser();

        const session = supertest.agent(URL_BASE);

        // Login
        const loginResponse = await session
            .post("/login")
            .send({ username: email, password })
            .expect(204);

        // Auth cookies set correctly
        const [authCookie, refreshCookie] =
            parseSetCookieHeaders(loginResponse);
        expect(authCookie.httpOnly).toBe(true);
        expect(authCookie.sameSite).toBe("Strict");
        expect(refreshCookie.httpOnly).toBe(true);
        expect(refreshCookie.sameSite).toBe("Strict");

        // Auth token should expire in less than an hour
        const authExpiry = jwt.decode(authCookie.value)["exp"];
        const fifteenMinutesFromNow = Math.floor(Date.now() / 1000) + 15 * 60;
        expect(authExpiry).toBeCloseTo(fifteenMinutesFromNow);

        // Refresh token should expire in more than a week
        const refreshExpiry = jwt.decode(refreshCookie.value)["exp"];
        const oneMonthFromNow =
            Math.floor(Date.now() / 1000) + 30 * 24 * 60 * 60;
        expect(refreshExpiry).toBeCloseTo(oneMonthFromNow);

        // Create an expired auth token
        const fakeExpiredToken = jwt.sign(
            {
                sub: jwt.decode(authCookie.value).sub,
                iat: Math.floor(Date.now() / 1000) - 60 * 60 * 2, // 2 hours ago
            },
            process.env.JWT_SECRET,
            {
                expiresIn: "1h",
            },
        );

        // Verify that our auth token is expired
        await supertest
            .agent(URL_BASE)
            .get("/users/me")
            .set("Cookie", `auth=${fakeExpiredToken}`)
            .expect(401);

        // Use the refresh token to get a new auth token
        const newSession = supertest.agent(URL_BASE);
        const refreshResponse = await newSession
            .post("/refresh")
            .set("Cookie", `auth=${fakeExpiredToken}`)
            .set("Cookie", `refresh=${refreshCookie.value}`)
            .expect(204);

        // New auth cookies is set correctly
        const [newAuthCookie] = parseSetCookieHeaders(refreshResponse);
        expect(newAuthCookie.httpOnly).toBe(true);
        expect(newAuthCookie.sameSite).toBe("Strict");

        // New auth cookie works
        await newSession.get("/users/me").expect(200);
    });

    it("POST /refresh 401 - for expired refresh token", async () => {
        const { email, password } = await createAndOnboardNewUser();

        const loginResponse = await supertest
            .agent(URL_BASE)
            .post("/login")
            .send({ username: email, password })
            .expect(204);

        // Create an expired refresh token
        const refreshCookie = parseSetCookieHeaders(loginResponse)[1];

        const fakeExpiredToken = jwt.sign(
            {
                sub: jwt.decode(refreshCookie.value).sub,
                sessionId: jwt.decode(refreshCookie.value)["sessionId"],
                iat: Math.floor(Date.now() / 1000) - 60 * 60 * 2, // 2 hours ago
            },
            process.env.JWT_SECRET,
            {
                expiresIn: "1h",
            },
        );

        // Try to use the expired refresh token
        await supertest
            .agent(URL_BASE)
            .post("/refresh")
            .set("Cookie", `auth=${refreshCookie.value}`)
            .set("Cookie", `refresh=${fakeExpiredToken}`)
            .expect(401);
    });
});
