import { INestApplication } from "@nestjs/common";

import { randomUUID } from "crypto";

import { URL_BASE } from "../constants";
import { login, loginAsAdmin, createApp, stopApp } from "../util";

describe("Tags", () => {
    let app: INestApplication;

    beforeAll(async () => {
        app = await createApp();
    });

    afterAll(async () => {
        await stopApp(app);
    });

    it("POST /tags 403", async () => {
        const request = await login(URL_BASE);
        await request
            .post("/tags")
            .send({ name: "non-personal-tag" })
            .expect(403);
    });

    it("POST /tags 409", async () => {
        const request = await loginAsAdmin(URL_BASE);

        // Create a new tag
        const name = `tag-${randomUUID()}`;
        await request.post("/tags").send({ name }).expect(201);

        // Try to create new tag with the same name
        await request.post("/tags").send({ name }).expect(409);
    });

    it("POST /tags 201", async () => {
        const request = await loginAsAdmin(URL_BASE);

        const name = `tag-${randomUUID()}`;
        const description = "test description";
        await request
            .post("/tags")
            .send({
                name,
                description,
            })
            .expect(201);

        const tagsResponse = await request.get("/tags").expect(200);

        const tags = tagsResponse.body.items;
        expect(tags.find((tag) => tag.name === name)).toMatchObject({
            name,
            description,
        });
    });

    it("PUT /tags", async () => {
        const userSession = await login(URL_BASE);
        const adminSession = await loginAsAdmin(URL_BASE);

        // Admin creates a tag
        const name = `tag-${randomUUID()}`;
        const description = "test description";
        const createTagResponse = await adminSession
            .post("/tags")
            .send({
                name,
                description,
            })
            .expect(201);

        const id = createTagResponse.body.id;

        const newName = `new-${name}`;
        const newDescription = `new ${description}`;

        // Regular user cannot edit tags
        await userSession.put(`/tags/${id}`).expect(403);

        // Tag name cannot be changed
        await adminSession
            .put(`/tags/${id}`)
            .send({
                name: newName,
                description: newDescription,
            })
            .expect(400);

        // Admin changes tag description
        await adminSession
            .put(`/tags/${id}`)
            .send({
                description: newDescription,
            })
            .expect(200);

        // Tag description is now changed
        const tagsResponse = await userSession.get("/tags").expect(200);

        const tags = tagsResponse.body.items;
        expect(tags.find((tag) => tag.name === name)).toMatchObject({
            name,
            description: newDescription,
        });
    });

    it("DELETE /tags/:id", async () => {
        const userSession = await login(URL_BASE);
        const adminSession = await loginAsAdmin(URL_BASE);

        // Create a new tag
        const name = `tag-${randomUUID()}`;
        const createTagResponse = await adminSession
            .post("/tags")
            .send({
                name,
            })
            .expect(201);

        const id = createTagResponse.body.id;

        // Regular user can't delete tags
        await userSession.delete(`/tags/${id}`).expect(403);

        // Admin user deletes the tag
        await adminSession.delete(`/tags/${id}`).expect(204);

        // Tag no longer exists for anyone
        {
            const tagsResponse = await userSession.get("/tags").expect(200);
            const tags = tagsResponse.body.items;
            expect(tags.find((tag) => tag.name === name)).toBeUndefined();
        }

        // Re-create the tag
        await adminSession
            .post("/tags")
            .send({
                name,
            })
            .expect(201);

        // Tag exists again for everyone
        {
            const tagsResponse = await userSession.get("/tags").expect(200);
            const tags = tagsResponse.body.items;
            expect(tags.find((tag) => tag.name === name)).toBeDefined();
        }
    });

    it("GET /tags 200 filters tags by topicId", async () => {
        const adminSession = await loginAsAdmin();

        // Create a tag
        const name = `tag-${randomUUID()}`;
        await adminSession.post("/tags").send({ name }).expect(201);

        // Filter by non-existent topic
        const getTagsResponse = await adminSession
            .get("/tags?topicIds=-1")
            .expect(200);

        expect(getTagsResponse.body).toMatchObject({
            items: [],
            total: 0,
            totalPages: 0,
        });
    });

    it("GET /tags 200 filters tags by groupId", async () => {
        const adminSession = await loginAsAdmin();

        // Create a tag
        const name = `tag-${randomUUID()}`;
        await adminSession.post("/tags").send({ name }).expect(201);

        // Create a group
        const groupName = `group-${randomUUID()}`;
        const createGroupResponse = await adminSession
            .post("/groups")
            .send({ name: groupName })
            .expect(201);
        const groupId = createGroupResponse.body.id;

        // Filter by the group
        const getTagsResponse = await adminSession
            .get(`/tags?groupIds=${groupId}`)
            .expect(200);

        expect(getTagsResponse.body).toMatchObject({
            items: [],
            total: 0,
            totalPages: 0,
        });
    });

    describe("personal tags", () => {
        it("Can be created", async () => {
            const userSession1 = await login();
            const userSession2 = await login();

            // Two users can create personal tags with the same name
            const name = `personal-tag-${randomUUID()}`;
            const createTagResponse1 = await userSession1
                .post("/tags")
                .send({ name, isPersonal: true })
                .expect(201);
            const tag1 = createTagResponse1.body;
            const createTagResponse2 = await userSession2
                .post("/tags")
                .send({ name, isPersonal: true })
                .expect(201);
            const tag2 = createTagResponse2.body;

            expect(tag1.id).not.toBe(tag2.id);
        });

        it("Can be filtered", async () => {
            const adminSession = await loginAsAdmin();

            // Create a personal tag
            const personalTagName = `personal-tag-${randomUUID()}`;
            await adminSession
                .post("/tags")
                .send({ name: personalTagName, isPersonal: true })
                .expect(201);

            // Create a non-personal tag
            await adminSession
                .post("/tags")
                .send({
                    name: `non-personal-tag-${randomUUID()}`,
                    isPersonal: false,
                })
                .expect(201);

            // Filter by the user's personal tags
            const getTagsResponse = await adminSession
                .get("/tags?personalTagsOnly=true")
                .expect(200);

            // Only the personal tag is returned
            expect(getTagsResponse.body).toMatchObject({
                items: [expect.objectContaining({ name: personalTagName })],
                total: 1,
            });
        });

        it("Are not visible to other users", async () => {
            const userSession1 = await login();
            const userSession2 = await login();

            // First user creates a personal tag
            const personalTagName = `personal-tag-${randomUUID()}`;
            await userSession1
                .post("/tags")
                .send({ name: personalTagName, isPersonal: true })
                .expect(201);

            // Second user cannot see the personal tag
            const getTagsResponse = await userSession2.get("/tags").expect(200);

            for (const tag of getTagsResponse.body.items) {
                expect(tag.name).not.toBe(personalTagName);
            }
        });

        it("Can be deleted", async () => {
            const userSession1 = await login();
            const userSession2 = await login();

            // First user creates a personal tag
            const personalTagName = `personal-tag-${randomUUID()}`;
            const createTagResponse = await userSession1
                .post("/tags")
                .send({ name: personalTagName, isPersonal: true })
                .expect(201);
            const id = createTagResponse.body.id;

            // Second user cannot delete the personal tag
            await userSession2.delete(`/tags/${id}`).expect(403);

            // First user deletes the personal tag
            await userSession1.delete(`/tags/${id}`).expect(204);

            // Personal tag is no longer visible
            const getTagsResponse = await userSession1.get("/tags").expect(200);
            for (const tag of getTagsResponse.body.items) {
                expect(tag.name).not.toBe(personalTagName);
            }
        });
    });

    it("PATCH /tags/preferences", async () => {
        const userSession = await login();

        // Create a tag
        const name = `tag-${randomUUID()}`;
        const createTagResponse = await userSession
            .post("/tags")
            .send({ name, isPersonal: true })
            .expect(201);
        const tag = createTagResponse.body;

        // Update tag notification preferences
        await userSession
            .patch(`/tags/preferences`)
            .send({
                items: [
                    {
                        id: tag.id,
                        patch: {
                            notificationCadence: "WEEKLY",
                            highRelevanceNotifications: "MONTHLY",
                            negativeSentimentNotifications: "DAILY",
                        },
                    },
                ],
            })
            .expect(204);

        // Check that the tag is updated
        const getTagsResponse = await userSession.get("/tags").expect(200);

        expect(getTagsResponse.body.items[0]).toMatchObject({
            userTagPreferences: {
                notificationCadence: "WEEKLY",
                highRelevanceNotifications: "MONTHLY",
                negativeSentimentNotifications: "DAILY",
            },
        });
    });
});
