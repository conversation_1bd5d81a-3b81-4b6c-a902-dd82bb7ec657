import { INestApplication } from "@nestjs/common";

import { randomUUID } from "node:crypto";

import { login, createApp, stopApp } from "../util";

describe("Bulk topic creation", () => {
    let app: INestApplication;

    beforeAll(async () => {
        app = await createApp();
    });

    afterAll(async () => {
        await stopApp(app);
    });

    it("POST /topics/bulk 201", async () => {
        const topic1 = `test ${randomUUID()}`;
        const topic2 = `test ${randomUUID()}`;

        const request = await login();
        await request.post("/topics/bulk").send([topic1, topic2]).expect(201);

        const response = await request.get("/topics");
        expect(response.body.items).toEqual(
            expect.arrayContaining([
                expect.objectContaining({ name: topic1 }),
                expect.objectContaining({ name: topic2 }),
            ]),
        );
    });
});
