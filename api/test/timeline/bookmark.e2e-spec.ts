import { INestApplication } from "@nestjs/common";

import { PrismaClient } from "@prisma/client";
import { randomUUID } from "crypto";

import { buildApp, login, startApp, stopApp } from "../util";

describe("Bookmarks", () => {
    let app: INestApplication;
    let prisma: PrismaClient;

    beforeAll(async () => {
        app = await buildApp();
        prisma = new PrismaClient();

        // Create test reddit post
        const redditPost = await prisma.redditPost.create({
            data: {
                id: randomUUID(),
                title: `post ${randomUUID()}`,
                text: "Test Reddit Post Text",
                publishedAt: new Date(),
                tenantId: "DEFAULT",
            },
        });

        // Create sentiment analysis
        await prisma.$transaction(async (tx) => {
            const analysis = await prisma.analysis.create({
                data: {
                    source: "REDDIT_POST",
                    sourceId: redditPost.id,
                    sentiment: 25,
                    relevance: 50,
                    summary: "placeholder summary",
                    tenantId: "DEFAULT",
                },
            });

            const topic = await tx.topic.upsert({
                where: {
                    tenantId_name: {
                        tenantId: "DEFAULT",
                        name: "Test Topic",
                    },
                },
                update: {},
                create: {
                    name: "Test Topic",
                    tenant: {
                        connect: {
                            id: "DEFAULT",
                        },
                    },
                },
            });

            const narrative = await tx.narrative.create({
                data: {
                    aspect: "BUSINESS_PRACTICES",
                    topicId: topic.id,
                    summary: "placeholder summary",
                    tenantId: "DEFAULT",
                },
            });

            await tx.analysisToNarrative.create({
                data: {
                    analysisId: analysis.id,
                    narrativeId: narrative.id,
                    sentiment: 25,
                    sentimentReasoning: "placeholder sentiment reasoning",
                },
            });
        });

        await startApp(app);
    });

    afterAll(async () => {
        await stopApp(app);
        await prisma.$disconnect();
    });

    it("Bookmark a post", async () => {
        const request = await login();

        // Get posts
        const insightsResponse = await request
            .get("/insights/timeline")
            .expect(200);
        const analysisId = insightsResponse.body.items[0].id;

        // Bookmark a post
        await request
            .post(`/insights/timeline/${analysisId}/bookmark`)
            .expect(201);

        // Check that the post is bookmarked
        {
            const insightsResponse = await request.get("/insights/timeline");

            expect(insightsResponse.body.items[0]).toMatchObject({
                bookmarked: true,
            });
        }
    });

    it("Un-bookmark a post", async () => {
        const request = await login();

        // Get posts
        const insightsResponse = await request
            .get("/insights/timeline")
            .expect(200);
        const analysisId = insightsResponse.body.items[0].id;

        // Bookmark a post
        await request
            .post(`/insights/timeline/${analysisId}/bookmark`)
            .expect(201);

        // Unbookmark the post
        await request
            .delete(`/insights/timeline/${analysisId}/bookmark`)
            .expect(204);

        // Check that the post is not bookmarked
        {
            const insightsResponse = await request.get("/insights/timeline");
            expect(insightsResponse.body.items[0]).toMatchObject({
                bookmarked: false,
            });
        }
    });

    it("404", async () => {
        const request = await login();
        await request.post("/insights/timeline/-1/bookmark").expect(404);
    });
});
