import { INestApplication } from "@nestjs/common";

import { PrismaClient } from "@prisma/client";
import { randomUUID } from "crypto";
import TestAgent from "supertest/lib/agent";

import { SalesforceApiService } from "../../src/salesforce-api/salesforce-api.service";
import { RedditSourceDto } from "../../src/task/dto/reddit-source.dto";
import {
    CreateYoutubeSourceResponseDto,
    YoutubeSourceDto,
} from "../../src/task/dto/youtube-source.dto";
import { YoutubeApiService } from "../../src/youtube-api/youtube-api.service";
import { MockYoutubeApiService } from "../mocks/youtube-api.service";
import { createApp, login, loginAsAdmin, stopApp } from "../util";
import { MockSalesforceApiService } from "./../mocks/salesforce-api.service";

describe("Task data sources", () => {
    let app: INestApplication;
    let prisma: PrismaClient;
    const mockYoutubeApiService = new MockYoutubeApiService();
    const mockSalesforceApiService = new MockSalesforceApiService();

    beforeAll(async () => {
        app = await createApp({
            providerOverrides: [
                [YoutubeApiService, mockYoutubeApiService],
                [SalesforceApiService, mockSalesforceApiService],
            ],
        });

        prisma = new PrismaClient();
    });

    afterAll(async () => {
        await stopApp(app);
        await prisma.$disconnect();
    });

    describe("/sources/youtube", () => {
        let adminSession: TestAgent;
        let channel: CreateYoutubeSourceResponseDto;
        let channelHandle: string;

        beforeEach(async () => {
            adminSession = await loginAsAdmin();

            channelHandle = `channel ${randomUUID()}`;

            const response = await adminSession
                .post("/sources/youtube")
                .send({ channelHandle })
                .expect(201);

            channel = response.body;
        });

        afterEach(() => {
            channel = undefined;
            channelHandle = undefined;
        });

        it("Requires admin role", async () => {
            const userSession = await login();
            await userSession.post("/sources/youtube").expect(403);
        });

        it("List channels", async () => {
            const adminSession = await loginAsAdmin();

            const youtubeSourcesResponse = await adminSession
                .get("/sources/youtube")
                .expect(200);

            const youtubeSources = youtubeSourcesResponse.body.items;

            expect(youtubeSources[0]).toMatchObject({
                channelHandle,
            });
        });

        it("Delete a channel", async () => {
            // Delete the channel
            await adminSession
                .delete(`/sources/youtube/${channel.id}`)
                .expect(204);

            // Check that it is no longer returned
            const youtubeSourcesResponse = await adminSession
                .get("/sources/youtube")
                .expect(200);

            const youtubeSources: YoutubeSourceDto[] =
                youtubeSourcesResponse.body.items;

            expect(
                youtubeSources.find(
                    (youtubeSource) =>
                        youtubeSource.channelHandle === channelHandle,
                ),
            ).toBeUndefined();
        });

        it("Bulk create youtube sources", async () => {
            const channel1 = `channel-${randomUUID()}`;
            const channel2 = `channel-${randomUUID()}`;

            await adminSession
                .post("/sources/youtube/bulk")
                .send([channel1, channel2])
                .expect(201);

            const youtubeSourcesResponse = await adminSession
                .get("/sources/youtube")
                .expect(200);

            expect(youtubeSourcesResponse.body.items).toEqual(
                expect.arrayContaining([
                    expect.objectContaining({ channelHandle: channel1 }),
                    expect.objectContaining({ channelHandle: channel2 }),
                ]),
            );
        });
    });

    describe("/sources/reddit", () => {
        let adminSession: TestAgent;
        let subreddit: string;

        beforeEach(async () => {
            adminSession = await loginAsAdmin();

            subreddit = `subreddit-${randomUUID()}`;

            await adminSession
                .post("/sources/reddit")
                .send({ name: subreddit })
                .expect(201);
        });

        it("Requires admin role", async () => {
            const userSession = await login();
            await userSession.post("/sources/reddit").expect(403);
        });

        it("List subreddits", async () => {
            const adminSession = await loginAsAdmin();

            const subredditSourcesResponse = await adminSession
                .get("/sources/reddit")
                .expect(200);

            const redditSources = subredditSourcesResponse.body.items;

            expect(redditSources[0]).toMatchObject({
                name: subreddit,
            });
        });

        it("Delete a subreddit", async () => {
            // Delete the subreddit
            await adminSession
                .delete(`/sources/reddit/${subreddit}`)
                .expect(204);

            // Check that it is no longer returned
            const redditSourcesResponse = await adminSession
                .get("/sources/reddit")
                .expect(200);

            const redditSources: RedditSourceDto[] =
                redditSourcesResponse.body.items;

            expect(
                redditSources.find(
                    (redditSource) => redditSource.name === subreddit,
                ),
            ).toBeUndefined();
        });

        it("Bulk create subreddit sources", async () => {
            const subreddit1 = `subreddit-${randomUUID()}`;
            const subreddit2 = `subreddit-${randomUUID()}`;

            await adminSession
                .post("/sources/reddit/bulk")
                .send([subreddit1, subreddit2])
                .expect(201);

            const redditSourcesResponse = await adminSession
                .get("/sources/reddit")
                .expect(200);

            expect(redditSourcesResponse.body.items).toEqual(
                expect.arrayContaining([
                    expect.objectContaining({ name: subreddit1 }),
                    expect.objectContaining({ name: subreddit2 }),
                ]),
            );
        });
    });

    describe("/sources/salesforce", () => {
        let adminSession: TestAgent;

        beforeAll(async () => {
            adminSession = await loginAsAdmin();
        });

        describe("/sources/salesforce/sites", () => {
            beforeAll(async () => {
                // Clear Salesforce site and forums
                await prisma.salesforceSite.deleteMany();

                // Create a site
                await adminSession
                    .post("/sources/salesforce/sites")
                    .send({
                        siteName: "AVR_FREAKS",
                    })
                    .expect(201);
            });

            it("GET /sources/salesforce/sites", async () => {
                const sitesResponse = await adminSession
                    .get("/sources/salesforce/sites")
                    .expect(200);

                expect(sitesResponse.body).toMatchObject({
                    items: expect.arrayContaining([
                        expect.objectContaining({
                            name: "AVR_FREAKS",
                        }),
                    ]),
                });
            });

            it("DELETE /sources/salesforce/sites", async () => {
                // Create Microchip Classic forum site
                await adminSession
                    .post("/sources/salesforce/sites")
                    .send({
                        siteName: "MICROCHIP_CLASSIC",
                    })
                    .expect(201);

                // Delete the site
                await adminSession
                    .delete("/sources/salesforce/sites/MICROCHIP_CLASSIC")
                    .expect(204);

                // Check that it is no longer returned
                const sitesResponse = await adminSession
                    .get("/sources/salesforce/sites")
                    .expect(200);

                const site = sitesResponse.body.items.find(
                    ({ name }) => name === "MICROCHIP_CLASSIC",
                );
                expect(site).toBeUndefined();
            });

            it("Pulls all forums and subforums for a site", async () => {
                await adminSession
                    .post("/sources/salesforce/sites/AVR_FREAKS/pull")
                    .expect(201);

                const sitesResponse = await adminSession
                    .get("/sources/salesforce/sites")
                    .expect(200);

                expect(sitesResponse.body).toMatchObject({
                    items: expect.arrayContaining([
                        expect.objectContaining({
                            name: "AVR_FREAKS",
                            forums: expect.arrayContaining([
                                expect.objectContaining({
                                    name: "AVR Tools Miscellaneous",
                                    subForums: expect.arrayContaining([
                                        expect.objectContaining({
                                            name: "Off Topic",
                                        }),
                                    ]),
                                }),
                            ]),
                        }),
                    ]),
                });
            });
        });

        describe("/sources/salesforce/sites/:name/forums", () => {
            beforeAll(async () => {
                // Clear Salesforce site and forums
                await prisma.salesforceSite.deleteMany();

                // Create a site
                await adminSession
                    .post("/sources/salesforce/sites")
                    .send({
                        siteName: "AVR_FREAKS",
                    })
                    .expect(201);

                // Create forums
                await adminSession
                    .post("/sources/salesforce/sites/AVR_FREAKS/forums")
                    .send({
                        forumName: "test forum 1",
                    })
                    .expect(201);

                await adminSession
                    .post("/sources/salesforce/sites/AVR_FREAKS/forums")
                    .send({
                        forumName: "test forum 2",
                    })
                    .expect(201);
            });

            it("GET /sources/salesforce/sites/:name/forums", async () => {
                const forumsResponse = await adminSession
                    .get("/sources/salesforce/sites/AVR_FREAKS/forums")
                    .expect(200);

                expect(forumsResponse.body).toMatchObject({
                    items: expect.arrayContaining([
                        expect.objectContaining({
                            name: "test forum 1",
                        }),
                        expect.objectContaining({
                            name: "test forum 2",
                        }),
                    ]),
                });
            });

            it("DELETE /sources/salesforce/sites/:name/forums", async () => {
                // Create Microchip Classic site
                await adminSession
                    .post("/sources/salesforce/sites")
                    .send({
                        siteName: "MICROCHIP_CLASSIC",
                    })
                    .expect(201);

                const forumName = `forum ${randomUUID()}`;

                // Create a forum
                const createForumResponse = await adminSession
                    .post("/sources/salesforce/sites/MICROCHIP_CLASSIC/forums")
                    .send({
                        forumName,
                    })
                    .expect(201);
                const forumId = createForumResponse.body.id;

                // Delete the forum
                await adminSession
                    .delete(
                        `/sources/salesforce/sites/MICROCHIP_CLASSIC/forums/${forumId}`,
                    )
                    .expect(204);

                // Check that it is no longer returned
                const forumsResponse = await adminSession
                    .get("/sources/salesforce/sites/MICROCHIP_CLASSIC/forums")
                    .expect(200);

                const forum = forumsResponse.body.items.find(
                    ({ name }) => name === forumName,
                );
                expect(forum).toBeUndefined();
            });

            it("Pulls all forums for a site", async () => {
                // Clear all existing sites and forums
                await adminSession
                    .delete("/sources/salesforce/sites/AVR_FREAKS")
                    .expect(204);

                // Create a site
                await adminSession
                    .post(`/sources/salesforce/sites`)
                    .send({ siteName: "AVR_FREAKS" })
                    .expect(201);

                // Pull new forums
                await adminSession
                    .post(`/sources/salesforce/sites/AVR_FREAKS/forums/pull`)
                    .expect(201);

                // Check forums are returned
                const forumsResponse = await adminSession.get(
                    `/sources/salesforce/sites/AVR_FREAKS/forums/`,
                );

                expect(forumsResponse.body).toMatchObject({
                    items: expect.arrayContaining([
                        expect.objectContaining({
                            name: "AVR Tools Miscellaneous",
                        }),
                    ]),
                });
            });
        });

        describe("/sources/salesforce/sites/:name/forums/:id/subforums", () => {
            let forumId;

            beforeAll(async () => {
                // Clear Salesforce site and forums
                await prisma.salesforceSite.deleteMany();

                // Create a site
                await adminSession
                    .post("/sources/salesforce/sites")
                    .send({
                        siteName: "AVR_FREAKS",
                    })
                    .expect(201);

                // Create a forum
                const createForumResponse = await adminSession
                    .post("/sources/salesforce/sites/AVR_FREAKS/forums")
                    .send({
                        forumName: "AVR Tools Miscellaneous",
                    })
                    .expect(201);
                forumId = createForumResponse.body.id;

                // Create a subforum
                await adminSession
                    .post(
                        `/sources/salesforce/sites/AVR_FREAKS/forums/${forumId}/subforums`,
                    )
                    .send({
                        subforumName: "subforum 1",
                    })
                    .expect(201);
            });

            it("GET /sources/salesforce/sites/:name/forums/:id/subforums", async () => {
                const getSubforumsResponse = await adminSession.get(
                    `/sources/salesforce/sites/AVR_FREAKS/forums/${forumId}/subforums`,
                );

                expect(getSubforumsResponse.body).toMatchObject({
                    items: expect.arrayContaining([
                        expect.objectContaining({
                            name: "subforum 1",
                        }),
                    ]),
                });
            });

            it("DELETE /sources/salesforce/sites/:name/forums/:id/subforums", async () => {
                // Create Microchip Classic site
                await adminSession
                    .post("/sources/salesforce/sites")
                    .send({
                        siteName: "MICROCHIP_CLASSIC",
                    })
                    .expect(201);

                const forumName = `forum ${randomUUID()}`;
                const subforumName = `subforum ${randomUUID()}`;

                // Create a forum
                const createForumResponse = await adminSession
                    .post("/sources/salesforce/sites/MICROCHIP_CLASSIC/forums")
                    .send({
                        forumName,
                    })
                    .expect(201);
                const forumId = createForumResponse.body.id;

                // Create a subforum
                const createSubforumResponse = await adminSession
                    .post(
                        `/sources/salesforce/sites/MICROCHIP_CLASSIC/forums/${forumId}/subforums`,
                    )
                    .send({ subforumName });
                const subforumId = createSubforumResponse.body.id;

                // Delete the subforum
                await adminSession
                    .delete(
                        `/sources/salesforce/sites/MICROCHIP_CLASSIC/forums/${forumId}/subforums/${subforumId}`,
                    )
                    .expect(204);

                // Check that it is no longer returned
                const subforumsResponse = await adminSession
                    .get(
                        "/sources/salesforce/sites/MICROCHIP_CLASSIC/forums/${forumId}/subforums",
                    )
                    .expect(200);

                const subforum = subforumsResponse.body.items.find(
                    ({ name }) => name === subforumName,
                );
                expect(subforum).toBeUndefined();
            });

            it("Pulls all subforums for a forum", async () => {
                await adminSession
                    .post(
                        `/sources/salesforce/sites/AVR_FREAKS/forums/${forumId}/subforums/pull`,
                    )
                    .expect(201);

                const subforumsResponse = await adminSession.get(
                    `/sources/salesforce/sites/AVR_FREAKS/forums/${forumId}/subforums`,
                );

                expect(subforumsResponse.body).toMatchObject({
                    items: expect.arrayContaining([
                        expect.objectContaining({
                            name: "Off Topic",
                        }),
                    ]),
                });
            });
        });
    });
});
