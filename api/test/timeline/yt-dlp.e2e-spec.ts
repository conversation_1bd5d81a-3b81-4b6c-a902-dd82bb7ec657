import { INestApplication } from "@nestjs/common";

import { PrismaClient } from "@prisma/client";
import { randomUUID } from "crypto";
import * as fs from "fs/promises";

import { OpenaiService } from "../../src/openai/openai.service";
import { YoutubeApiService } from "../../src/youtube-api/youtube-api.service";
import { YoutubeSubtitlesService } from "../../src/youtube-subtitles/youtube-subtitles.service";
import { YtDlpService } from "../../src/yt-dlp/yt-dlp.service";
import { YtDlpServiceInterface } from "../../src/yt-dlp/yt-dlp.service.interface";
import { MockOpenaiService } from "../mocks/openai.service";
import { MockYoutubeApiService } from "../mocks/youtube-api.service";
import { MockYoutubeSubtitlesService } from "../mocks/youtube-subtitles.service";
import { loginAsAdmin, buildApp, startApp, stopApp, waitFor } from "../util";

const COOKIES =
    "# Netscape HTTP Cookie File\n# This file is generated by yt-dlp.  Do not edit.\n\n.youtube.com	TRUE	/	TRUE	1746544193	GPS	1";
const COOKIES_PATH = "cookies.txt";

describe("yt-dlp", () => {
    let app: INestApplication;
    let prisma: PrismaClient;
    const mockYoutubeApiService = new MockYoutubeApiService();
    const mockYoutubeSubtitlesService = new MockYoutubeSubtitlesService();
    const mockOpenaiService = new MockOpenaiService();
    let ytDlpService: YtDlpServiceInterface;
    let mockGetSubtitleURL: jest.SpyInstance;

    const channelHandle = randomUUID();

    beforeAll(async () => {
        app = await buildApp({
            providerOverrides: [
                [YoutubeApiService, mockYoutubeApiService],
                [YoutubeSubtitlesService, mockYoutubeSubtitlesService],
                [OpenaiService, mockOpenaiService],
            ],
        });

        ytDlpService = app.get(YtDlpService);
        mockGetSubtitleURL = jest
            .spyOn(ytDlpService, "getSubtitleURL")
            .mockResolvedValue("https://www.youtube.com/api/timedtext");

        prisma = new PrismaClient();

        // Wipe out any existing youtube videos
        await prisma.youtubeVideo.deleteMany();

        await startApp(app);

        // Create a youtube channel source
        const adminSession = await loginAsAdmin();
        await adminSession
            .post("/sources/youtube")
            .send({ channelHandle })
            .expect(201);
    });

    beforeEach(async () => {
        // Clear any existing proxy URL or cookies from database
        await prisma.requestCache.deleteMany();
    });

    afterEach(async () => {
        mockGetSubtitleURL.mockClear();
    });

    afterAll(async () => {
        await stopApp(app);
        await prisma.$disconnect();
    });

    describe("Cookies", () => {
        beforeEach(async () => {
            // Remove any existing cookies file
            await fs.rm(COOKIES_PATH, { force: true });
        });

        afterAll(async () => {
            // Clean up created cookies file
            await fs.rm(COOKIES_PATH, { force: true });
        });

        it("Uses cookies when invoking yt-dlp", async () => {
            const request = await loginAsAdmin();

            // Set cookies
            await request
                .put("/yt-dlp/cookies")
                .set("Content-Type", "text/plain")
                .send(COOKIES)
                .expect(204);

            // Request timeline refresh for youtube videos
            await request
                .post("/task/youtube")
                .send({ channelHandles: [channelHandle] })
                .expect(201);

            // Check the cookies file was written
            await waitFor(async () => {
                const cookies = await fs.readFile(COOKIES_PATH);
                expect(cookies.toString()).toEqual(COOKIES);
                return true;
            });

            // Check yt-dlp was called with the cookies option
            await waitFor(async () => {
                expect(mockGetSubtitleURL).toHaveBeenCalled();
                return true;
            });
            const invokedCookiesOption = mockGetSubtitleURL.mock.lastCall[1];
            expect(invokedCookiesOption).toBeTruthy();
        });
    });

    describe("Proxy", () => {
        it("Sets proxy URL", async () => {
            const request = await loginAsAdmin();

            await request
                .put("/yt-dlp/proxy")
                .send("http://localhost:9090")
                .set("Content-Type", "text/plain")
                .expect(204);

            const proxyUrl = await request.get("/yt-dlp/proxy").expect(200);
            expect(proxyUrl.text).toEqual("http://localhost:9090");
        });

        it("Clears proxy URL", async () => {
            const request = await loginAsAdmin();

            await request
                .put("/yt-dlp/proxy")
                .send("http://localhost:9090")
                .set("Content-Type", "text/plain")
                .expect(204);

            await request.delete("/yt-dlp/proxy").expect(204);

            const proxyUrl = await request.get("/yt-dlp/proxy").expect(200);
            expect(proxyUrl.text).toBeFalsy();
        });

        it("Uses proxy URL when invoking yt-dlp", async () => {
            // Spy on calls to the subtitle service
            const subtitleServiceManifestSpy = jest.spyOn(
                mockYoutubeSubtitlesService,
                "getSubtitles",
            );

            const subtitleServiceDownloadSpy = jest.spyOn(
                mockYoutubeSubtitlesService,
                "getSubtitles",
            );

            const request = await loginAsAdmin();
            const mockGetSubtitleURL = jest
                .spyOn(ytDlpService, "getSubtitleURL")
                .mockResolvedValue("https://www.youtube.com/api/timedtext");

            // Set a proxy URL
            await request
                .put("/yt-dlp/proxy")
                .send("http://localhost:9090")
                .set("Content-Type", "text/plain")
                .expect(204);

            // Request timeline refresh for youtube videos
            await request
                .post("/task/youtube")
                .send({ channelHandles: [channelHandle] })
                .expect(201);

            // Wait for yt-dlp to be called with the proxy URL
            await waitFor(async () => {
                expect(mockGetSubtitleURL).toHaveBeenCalled();
                return true;
            });

            {
                const invokedProxy = mockGetSubtitleURL.mock.lastCall[2];
                expect(invokedProxy).toEqual("http://localhost:9090");
            }

            // Check that the actual subtitle file is downloaded with proxy too
            await waitFor(async () => {
                expect(subtitleServiceManifestSpy).toHaveBeenCalled();
                return true;
            });

            {
                const invokedProxy =
                    subtitleServiceManifestSpy.mock.lastCall[1];
                expect(invokedProxy).toEqual("http://localhost:9090");
            }

            await waitFor(async () => {
                expect(subtitleServiceDownloadSpy).toHaveBeenCalled();
                return true;
            });

            {
                const invokedProxy =
                    subtitleServiceDownloadSpy.mock.lastCall[1];
                expect(invokedProxy).toEqual("http://localhost:9090");
            }
        });
    });
});
