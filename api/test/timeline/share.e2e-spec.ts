import { INestApplication } from "@nestjs/common";

import { PrismaClient } from "@prisma/client";
import { randomUUID } from "crypto";

import { login, buildApp, startApp, stopApp } from "../util";

describe("Share", () => {
    let app: INestApplication;
    let prisma: PrismaClient;
    let groupId: number;

    beforeAll(async () => {
        app = await buildApp();
        prisma = new PrismaClient();

        // Create test reddit post
        const redditPost = await prisma.redditPost.create({
            data: {
                id: randomUUID(),
                title: `post ${randomUUID()}`,
                text: "Test Reddit Post Text",
                publishedAt: new Date(),
                tenantId: "DEFAULT",
            },
        });

        // Create sentiment analysis
        await prisma.$transaction(async (tx) => {
            const analysis = await prisma.analysis.create({
                data: {
                    source: "REDDIT_POST",
                    sourceId: redditPost.id,
                    sentiment: 25,
                    summary: "placeholder summary",
                    relevance: 50,
                    tenantId: "DEFAULT",
                },
            });

            const topic = await tx.topic.upsert({
                where: {
                    tenantId_name: {
                        tenantId: "DEFAULT",
                        name: "Test Topic",
                    },
                },
                update: {},
                create: {
                    name: "Test Topic",
                    tenant: { connect: { id: "DEFAULT" } },
                },
            });

            const narrative = await tx.narrative.create({
                data: {
                    aspect: "BUSINESS_PRACTICES",
                    topicId: topic.id,
                    summary: "placeholder summary",
                    tenantId: "DEFAULT",
                },
            });

            await tx.analysisToNarrative.create({
                data: {
                    analysisId: analysis.id,
                    narrativeId: narrative.id,
                    sentiment: 25,
                    sentimentReasoning: "placeholder sentiment reasoning",
                },
            });
        });

        // Create a group
        const group = await prisma.group.create({
            data: {
                name: `group ${randomUUID()}`,
                tenant: {
                    connect: {
                        id: "DEFAULT",
                    },
                },
            },
        });
        groupId = group.id;

        await startApp(app);
    });

    afterAll(async () => {
        await stopApp(app);
        await prisma.$disconnect();
    });

    it("Share a post with a group", async () => {
        const request = await login();

        // Check the group has no posts
        {
            const insightsResponse = await request
                .get(`/insights/timeline?sharedWithGroupIds=${groupId}`)
                .expect(200);
            expect(insightsResponse.body.items.length).toEqual(0);
        }

        // Get a post
        const insightsResponse = await request
            .get("/insights/timeline")
            .expect(200);
        const analysisId = insightsResponse.body.items[0].id;

        // Share a post with the group
        await request
            .post(`/insights/timeline/${analysisId}/groups/${groupId}`)
            .expect(201);

        // Check the group has the post
        {
            const insightsResponse = await request.get(
                `/insights/timeline?sharedWithGroupIds=${groupId}`,
            );

            expect(insightsResponse.body.items[0]).toMatchObject({
                id: analysisId,
            });
        }
    });
});
