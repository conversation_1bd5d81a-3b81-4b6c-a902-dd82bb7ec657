import { INestApplication } from "@nestjs/common";

import { PrismaClient } from "@prisma/client";
import { randomUUID } from "crypto";
import { ChatCompletion } from "openai/resources/chat/completions";

import { OpenaiService } from "../../src/openai/openai.service";
import { buildApp, loginAsAdmin, startApp, stopApp, waitFor } from "../util";

describe("Narratives", () => {
    let app: INestApplication;
    let prisma: PrismaClient;

    beforeAll(async () => {
        app = await buildApp();

        // Mock OpenAI completion response
        jest.spyOn(app.get(OpenaiService), "getCompletion").mockResolvedValue({
            choices: [
                {
                    message: {
                        role: "assistant",
                        content: '{ "paraphrased": "Test narrative" }',
                    },
                },
            ],
        } as ChatCompletion);

        prisma = new PrismaClient();
    });

    afterAll(async () => {
        await stopApp(app);
        await prisma.$disconnect();
    });

    describe("Narrative generation", () => {
        beforeAll(async () => {
            // Clear posts
            await prisma.redditPost.deleteMany();
            await prisma.youtubeVideo.deleteMany();
            await prisma.salesforceSite.deleteMany();

            // Clear narratives
            await prisma.narrative.deleteMany();

            // Clear data sources to avoid actually pulling any data when requesting timeline refresh
            await prisma.subreddit.deleteMany();
            await prisma.youtubeChannel.deleteMany();
            await prisma.salesforceSite.deleteMany();

            await startApp(app);

            const request = await loginAsAdmin();

            // Create a topic
            await request
                .post("/topics")
                .send({
                    name: "Test Topic",
                })
                .expect(({ status }) => status === 201 || status === 409);
        });

        it("Generates narratives", async () => {
            const request = await loginAsAdmin();

            // Request timeline refresh
            await request
                .post("/task/timeline-refresh")
                .set("x-workflow-key", randomUUID())
                .expect(201);

            // Wait for narratives to exist
            const narratives = await waitFor(async () => {
                const response = await request.get("/narratives").expect(200);
                expect(response.body.total).toBeGreaterThan(0);
                expect(response.body.items.length).toBeGreaterThan(0);
                return response.body.items;
            });

            expect(narratives).toEqual(
                expect.arrayContaining([
                    expect.objectContaining({
                        summary: expect.any(String),
                        topic: expect.objectContaining({
                            id: expect.any(Number),
                            name: expect.any(String),
                        }),
                        aspect: expect.any(String),
                    }),
                ]),
            );

            // Get a single narrative
            const narrativeId = narratives[0].id;
            const response = await request
                .get(`/narratives/${narrativeId}`)
                .expect(200);

            expect(response.body).toMatchObject({
                id: narrativeId,
                summary: expect.any(String),
            });
        });
    });
});
