import { INestApplication } from "@nestjs/common";

import * as supertest from "supertest";

import { URL_BASE } from "./constants";
import { createApp, login, stopApp } from "./util";

// BullBoard does not work seamlessly with NestJS auth guards, so we need to double-check auth is working
describe("BullBoard", () => {
    let app: INestApplication;

    beforeAll(async () => {
        app = await createApp();
    });

    afterAll(async () => {
        await stopApp(app);
    });

    it("GET /queues - 401", async () => {
        const request = supertest.agent(URL_BASE);
        await request.get("/queues").expect(401);
    });

    it("GET /queues - 200", async () => {
        const request = await login(URL_BASE);
        await request.get("/queues").expect(200);
    });
});
