import { INestApplication } from "@nestjs/common";

import { Narrative, PrismaClient } from "@prisma/client";
import { randomUUID } from "crypto";

import { login, buildApp, startApp, stopApp } from "../util";

describe("Narrative detail analytics", () => {
    let app: INestApplication;
    let prisma: PrismaClient;

    let narrative: Narrative;

    beforeAll(async () => {
        app = await buildApp();

        prisma = new PrismaClient();

        // Create a narrative
        narrative = await prisma.narrative.create({
            data: {
                summary: `narrative-${randomUUID()}`,
                tenant: {
                    connect: {
                        id: "DEFAULT",
                    },
                },
            },
        });

        // Create a timeline item
        const redditPost = await prisma.redditPost.create({
            data: {
                id: randomUUID(),
                title: `title-${randomUUID()}`,
                text: "test text",
                publishedAt: new Date(),
                tenantId: "DEFAULT",
            },
        });

        await prisma.analysis.create({
            data: {
                summary: `summary-${randomUUID()}`,
                source: "REDDIT_POST",
                sourceId: redditPost.id,
                sentiment: 77,
                narratives: {
                    create: [
                        {
                            narrativeId: narrative.id,
                            sentiment: 55,
                        },
                    ],
                },
                tenant: {
                    connect: {
                        id: "DEFAULT",
                    },
                },
            },
        });

        await startApp(app);
    });

    afterAll(async () => {
        await stopApp(app);
        await prisma.$disconnect();
    });

    it("Returns detailed narrative analytics", async () => {
        const request = await login();

        const response = await request
            .get(`/insights/analytics/narratives/${narrative.id}`)
            .expect(200);

        expect(response.body).toMatchObject({
            id: narrative.id,
            summary: narrative.summary,
            count: 1,
            sentiment: 55,
            sentimentDistribution: [0, 0, 1, 0, 0],
        });
    });

    it("Returns summary and tipsAndActions", async () => {
        const request = await login();

        const response = await request
            .get(`/narratives/${narrative.id}/breakdown`)
            .expect(200);

        expect(response.body).toMatchObject({
            id: narrative.id,
        });
    });
});
