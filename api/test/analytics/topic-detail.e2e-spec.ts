import { INestApplication } from "@nestjs/common";

import { PrismaClient, Topic } from "@prisma/client";
import { randomUUID } from "crypto";
import TestAgent from "supertest/lib/agent";

import { login, stopApp, createApp } from "../util";

describe("Topic detail analytics", () => {
    let app: INestApplication;
    let prisma: PrismaClient;
    let request: TestAgent;
    let topic: Topic;

    beforeAll(async () => {
        app = await createApp();

        request = await login();
        const user = await request.get("/users/me").expect(200);
        const tenantId = user.body.sessionTenantId;

        prisma = new PrismaClient();

        // Create a topic
        topic = await prisma.topic.create({
            data: {
                name: `topic-${randomUUID()}`,
                tenant: {
                    connect: {
                        id: tenantId,
                    },
                },
            },
        });
    });

    afterAll(async () => {
        await stopApp(app);
        await prisma.$disconnect();
    });

    it("Returns detailed topic analytics", async () => {
        const response = await request
            .get(`/insights/analytics/topics/${topic.id}`)
            .expect(200);

        expect(response.body).toEqual({
            id: topic.id,
            name: topic.name,
            count: 0,
            sentiment: 0,
            sentimentDistribution: [0, 0, 0, 0, 0],
        });
    });
});
