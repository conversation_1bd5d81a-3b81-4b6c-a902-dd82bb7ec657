import { INestApplication } from "@nestjs/common";

import { PrismaClient } from "@prisma/client";
import { randomUUID } from "crypto";
import * as supertest from "supertest";

import { OpenaiService } from "../../src/openai/openai.service";
import { URL_BASE } from "../constants";
import { buildApp, login, loginAsSuperAdmin, startApp, stopApp } from "../util";
import {
    OPENAI_EMBEDDING_RESPONSE,
    RANDOM_OPENAI_MOCK_EMBEDDING_RESPONSE,
    OPENAI_RANKING_RESPONSE,
} from "./constants";

describe("Search", () => {
    let app: INestApplication;
    let prisma: PrismaClient;

    let embeddingSpy: jest.SpyInstance;
    let rankingSpy: jest.SpyInstance;

    beforeAll(async () => {
        app = await buildApp();

        prisma = new PrismaClient();

        await prisma.redditPost.deleteMany();
        await prisma.subreddit.deleteMany();
        await prisma.searchableEntity.deleteMany();
    });

    beforeEach(async () => {
        const openaiService = app.get(OpenaiService);
        embeddingSpy = jest
            .spyOn(openaiService, "getEmbedding")
            .mockResolvedValueOnce(OPENAI_EMBEDDING_RESPONSE);
        rankingSpy = jest
            .spyOn(openaiService, "getCompletion")
            .mockResolvedValueOnce(OPENAI_RANKING_RESPONSE);
    });

    afterAll(async () => {
        await stopApp(app);
        await prisma.$disconnect();
    });

    describe("Reddit posts", () => {
        beforeAll(async () => {
            await prisma.redditPost.deleteMany();
            await prisma.subreddit.deleteMany();
            await prisma.analysis.deleteMany();
            await prisma.searchableEntity.deleteMany();

            // Add a post to the database
            const redditPost = await prisma.redditPost.create({
                data: {
                    title: "What are your favorite resources for learning Arduino?",
                    id: "1jkt43s",
                    text: "I've seen kits and YouTube channels. How did you learn?",
                    publishedAt: new Date("2021-01-01"),
                    subreddit: {
                        create: {
                            name: "arduino",
                            tenantId: "DEFAULT",
                        },
                    },
                    tenant: {
                        connect: {
                            id: "DEFAULT",
                        },
                    },
                },
            });

            // Create an analysis
            await prisma.$transaction(async (tx) => {
                const topic = await prisma.topic.create({
                    data: {
                        name: `topic-${randomUUID()}`,
                        tenant: {
                            connect: {
                                id: "DEFAULT",
                            },
                        },
                    },
                });

                const narrative = await prisma.narrative.create({
                    data: {
                        aspect: "BUG",
                        summary: `summary-${randomUUID()}`,
                        topic: {
                            connect: {
                                tenantId_name: {
                                    tenantId: "DEFAULT",
                                    name: topic.name,
                                },
                            },
                        },
                        tenant: {
                            connect: {
                                id: "DEFAULT",
                            },
                        },
                    },
                });

                const analysis = await tx.analysis.create({
                    data: {
                        source: "REDDIT_POST",
                        sourceId: redditPost.id,
                        summary: `summary-${randomUUID()}`,
                        longSummary: "mock long summary",
                        tipsAndActions: "mock tips and actions",
                        actionableReasoning: "mock actionable reasoning",
                        relevance: 85,
                        relevanceReasoning: "mock relevance reasoning",
                        sentiment: 50,
                        sentimentReasoning: "mock sentiment reasoning",
                        tenantId: "DEFAULT",
                    },
                });

                await tx.analysisToNarrative.create({
                    data: {
                        analysisId: analysis.id,
                        narrativeId: narrative.id,
                        sentiment: 50,
                    },
                });
            });

            // Add a searchable entity to the database
            const embedding = Array.from({ length: 1536 }).fill(0.1);

            await prisma.$executeRaw`
                INSERT INTO "SearchableEntity" ("tenantId", source, "sourceId", embedding, "searchVector")
                VALUES ('DEFAULT', 'REDDIT_POST', '1jkt43s', ${embedding}::vector, 'What are your favorite resources for learning Arduino')
            `;

            await startApp(app);
        });

        it("POST /search 401", async () => {
            const request = await supertest.agent(URL_BASE);
            await request.post("/search").expect(401);
        });

        it("returns relevant results", async () => {
            const request = await login();

            // Search
            const searchResponse = await request
                .post("/search")
                .send({
                    query: "arduino",
                })
                .expect(201);

            // Check results
            const { items } = searchResponse.body;
            expect(items.length).toBeGreaterThan(0);

            expect(items[0]).toMatchObject({
                id: expect.any(Number),
                score: expect.any(Number),
                sentiment: expect.any(Number),
                sentimentReasoning: expect.any(String),
                type: "REDDIT_POST",
                sourceId: "1jkt43s",
                voteScore: expect.any(Number),
                commentCount: expect.any(Number),
                publishedAt: expect.any(String),
                externalUrl: expect.any(String),
                title: "What are your favorite resources for learning Arduino?",
                longSummary: "mock long summary",
                tipsAndActions: "mock tips and actions",
                narratives: expect.arrayContaining([
                    expect.objectContaining({
                        aspect: "BUG",
                        summary: expect.any(String),
                        sentiment: expect.any(Number),
                    }),
                ]),
                relevance: {
                    score: expect.any(Number),
                    reasoning: expect.any(String),
                },
            });
        });

        it("excludes irrelevant results", async () => {
            const request = await login();

            // Set OpenAI embedding response to something that won't match the query
            const openaiService = app.get(OpenaiService);
            embeddingSpy.mockReset();
            embeddingSpy = jest
                .spyOn(openaiService, "getEmbedding")
                .mockResolvedValueOnce(RANDOM_OPENAI_MOCK_EMBEDDING_RESPONSE);
            rankingSpy.mockReset();

            // Search
            const searchResponse = await request
                .post("/search")
                .send({
                    query: "this is unrelated to microchip",
                })
                .expect(201);

            // Check results
            const { items } = searchResponse.body;
            expect(items.length).toEqual(0);
        });

        it("Handles search filters", async () => {
            const request = await login();

            // Search
            await request
                .post("/search?bookmarkedOnly=true&sharedWithGroupIds=1")
                .send({
                    query: "microchip",
                })
                .expect(201);
        });

        it("is tenant isolated", async () => {
            const request = await loginAsSuperAdmin();

            // Change session tenant
            const createTenantResponse = await request.post("/tenants").send({
                name: `tenant-${randomUUID()}`,
            });
            const tenantId = createTenantResponse.body.id;
            await request.put("/session-tenant").send({ tenantId }).expect(204);

            // Search
            const searchResponse = await request
                .post("/search")
                .send({
                    query: "microcontroller documentation",
                })
                .expect(201);

            // Check no results are returned
            const { items } = searchResponse.body;
            expect(items.length).toBe(0);
        });
    });
});
