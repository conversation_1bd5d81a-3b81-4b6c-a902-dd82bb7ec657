import { ChatCompletion, CreateEmbeddingResponse } from "openai/resources";

export const OPENAI_EMBEDDING_RESPONSE: CreateEmbeddingResponse = {
    object: "list",
    data: [
        {
            object: "embedding",
            embedding: Array(1536).fill(0.1),
            index: 0,
        },
    ],
    model: "text-embedding-3-small",
    usage: {
        prompt_tokens: 0,
        total_tokens: 0,
    },
};

export const RANDOM_OPENAI_MOCK_EMBEDDING_RESPONSE: CreateEmbeddingResponse = {
    object: "list",
    data: [
        {
            object: "embedding",
            embedding: Array.from({ length: 1536 }).map(
                () => 2 * Math.random() - 1,
            ),
            index: 0,
        },
    ],
    model: "text-embedding-3-small",
    usage: {
        prompt_tokens: 0,
        total_tokens: 0,
    },
};

export const OPENAI_RANKING_RESPONSE: ChatCompletion = {
    id: "",
    choices: [
        {
            finish_reason: "stop",
            index: 0,
            logprobs: null,
            message: {
                role: "assistant",
                refusal: null,
                content: "1jkt43s",
            },
        },
    ],
    model: "",
    created: 0,
    object: "chat.completion",
};
