import { INestApplication } from "@nestjs/common";

import { randomUUID } from "crypto";

import { URL_BASE } from "./constants";
import { createApp, login, loginAsAdmin, stopApp } from "./util";

describe("Users", () => {
    let app: INestApplication;

    beforeAll(async () => {
        app = await createApp();
    });

    afterAll(async () => {
        await stopApp(app);
    });

    it("POST /groups 403", async () => {
        const request = await login(URL_BASE);
        await request.post("/groups").expect(403);
    });

    it("POST /groups 400", async () => {
        const request = await loginAsAdmin(URL_BASE);
        await request.post("/groups").send({}).expect(400);
    });

    it("POST /groups 409", async () => {
        const request = await loginAsAdmin(URL_BASE);

        // Try to create a group with the same name twice
        const name = `group-${randomUUID()}`;
        const description = "This is a test group";
        await request.post("/groups").send({ name, description }).expect(201);
        await request.post("/groups").send({ name, description }).expect(409);
    });

    it("Group can be created with tags", async () => {
        const request = await loginAsAdmin();

        // Create a tag
        const tagName = `tag-${randomUUID()}`;
        const createTagResponse = await request
            .post("/tags")
            .send({ name: tagName })
            .expect(201);
        const tagId = createTagResponse.body.id;

        // Create a group
        const name = `group-${randomUUID()}`;
        const description = "This is a test group";
        await request
            .post("/groups")
            .send({ name, description, tags: [tagId] })
            .expect(201);

        const groups = await request.get("/groups").expect(200);
        const group = groups.body.items.find((group) => group.name === name);
        expect(group).toMatchObject({
            name,
            description,
            tags: [{ id: tagId, name: tagName }],
        });
    });

    it("GET /groups 200", async () => {
        const adminSession = await loginAsAdmin(URL_BASE);

        // Create a group
        const name = `group-${randomUUID()}`;
        const description = "This is a test group";
        await adminSession
            .post("/groups")
            .send({ name, description })
            .expect(201);

        // Check that the group is visible for all users
        const userSession = await login(URL_BASE);
        const groups = await userSession.get("/groups").expect(200);
        const group = groups.body.items.find((group) => group.name === name);
        expect(group).toMatchObject({ name, description });
    });

    it("GET /groups 200 filters groups by topicIds and tagIds", async () => {
        const adminSession = await loginAsAdmin();

        // Create a group
        const name = `group-${randomUUID()}`;
        const description = "This is a test group";
        await adminSession
            .post("/groups")
            .send({ name, description })
            .expect(201);

        // Filter by non-existent group topicId, tagId
        const getGroupsResponse = await adminSession
            .get("/groups?topicIds=-1&tagIds=-1")
            .expect(200);

        expect(getGroupsResponse.body).toMatchObject({
            items: [],
            total: 0,
            totalPages: 0,
        });
    });

    it("GET /groups 200 filters groups by user's membership", async () => {
        const adminSession = await loginAsAdmin();

        const getGroupsResponse = await adminSession
            .get("/groups?myTeamsOnly=true")
            .expect(200);

        for (const group of getGroupsResponse.body.items) {
            expect(group.isUserMember).toBe(true);
        }
    });

    it("PUT /groups/:id 204", async () => {
        const adminSession = await loginAsAdmin(URL_BASE);

        // Create a group
        const name = `group-${randomUUID()}`;
        const description = "This is a test group";
        const createGroupResponse = await adminSession
            .post("/groups")
            .send({ name, description })
            .expect(201);

        // Update the group
        const groupId = createGroupResponse.body.id;
        const updatedName = `updated-${name}`;
        await adminSession
            .put(`/groups/${groupId}`)
            .send({ name: updatedName, description: "Updated description" })
            .expect(204);

        // Check that the group is updated
        const groups = await adminSession.get("/groups").expect(200);
        const updatedGroup = groups.body.items.find(
            (group) => group.id === groupId,
        );
        expect(updatedGroup).toMatchObject({
            name: updatedName,
            description: "Updated description",
        });
    });

    it("DELETE /groups/:id", async () => {
        const userSession = await login(URL_BASE);
        const adminSession = await loginAsAdmin(URL_BASE);

        // Create a group
        const name = `group-${randomUUID()}`;
        const description = "This is a test group";
        const createGroupResponse = await adminSession
            .post("/groups")
            .send({ name, description })
            .expect(201);

        const groupId = createGroupResponse.body.id;

        // Regular user can't delete the group
        await userSession.delete(`/groups/${groupId}`).expect(403);

        // Admin deletes the group
        await adminSession.delete(`/groups/${groupId}`).expect(204);

        // Check that the group doesn't exist
        const groups = await adminSession.get("/groups").expect(200);
        const group = groups.body.items.find((group) => group.name === name);
        expect(group).toBeUndefined();
    });

    describe("Users can manage their group memberships", () => {
        let groupId: number;

        beforeAll(async () => {
            // Admin creates a group
            const adminSession = await loginAsAdmin();
            const name = `group-${randomUUID()}`;
            const description = "This is a test group";
            const createGroupResponse = await adminSession
                .post("/groups")
                .send({ name, description })
                .expect(201);

            groupId = createGroupResponse.body.id;
        });

        it("PUT /groups/:id/users/me 204", async () => {
            const userSession = await login();

            // User adds themselves to the group
            await userSession.put(`/groups/${groupId}/users/me`).expect(204);

            // Check that the user is in the group
            const userMeResponse = await userSession
                .get("/users/me")
                .expect(200);

            expect(userMeResponse.body).toMatchObject({
                groups: expect.arrayContaining([
                    expect.objectContaining({
                        id: groupId,
                    }),
                ]),
            });
        });

        it("DELETE /groups/:id/users/me 204", async () => {
            const userSession = await login();

            // User adds themselves to the group
            await userSession.put(`/groups/${groupId}/users/me`).expect(204);

            // User removes themselves from the group
            await userSession.delete(`/groups/${groupId}/users/me`).expect(204);

            // Check that the user is not in the group
            // Check that the user is in the group
            const userMeResponse = await userSession
                .get("/users/me")
                .expect(200);

            expect(userMeResponse.body).toMatchObject({
                groups: expect.not.arrayContaining([
                    expect.objectContaining({
                        id: groupId,
                    }),
                ]),
            });
        });
    });

    it("PATCH /groups/preferences", async () => {
        const adminSession = await loginAsAdmin();
        const userSession = await login();

        // Admin creates a group
        const name = `group-${randomUUID()}`;
        const createGroupResponse = await adminSession
            .post("/groups")
            .send({ name })
            .expect(201);
        const group = createGroupResponse.body;

        // User updates group notification preferences
        await userSession
            .patch(`/groups/preferences`)
            .send({
                items: [
                    {
                        id: group.id,
                        patch: {
                            notificationCadence: "WEEKLY",
                            highRelevanceNotifications: "MONTHLY",
                            negativeSentimentNotifications: "DAILY",
                        },
                    },
                ],
            })
            .expect(204);

        // Check that the group is updated
        const getGroupsResponse = await userSession.get("/groups").expect(200);

        expect(getGroupsResponse.body.items[0]).toMatchObject({
            userGroupPreference: {
                notificationCadence: "WEEKLY",
                highRelevanceNotifications: "MONTHLY",
                negativeSentimentNotifications: "DAILY",
            },
        });
    });
});
