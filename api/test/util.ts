import { INestApplication } from "@nestjs/common";
import { Test } from "@nestjs/testing";

import { Queue } from "bullmq";
import { parseSetCookie } from "cookie-es";
import { randomUUID } from "crypto";
import * as supertest from "supertest";

import { setupApp } from "../src/app-config";
import { AppModule } from "../src/app.module";
import {
    AI_ANALYSIS_QUEUE,
    NOTIFICATIONS_PERIODIC_DIGEST_QUEUE,
    REDDIT_POST_QUEUE,
    REDDIT_SUBREDDIT_QUEUE,
} from "../src/task/constants";
import { URL_BASE } from "./constants";

type Class = { new (...args: unknown[]): object };

type ProviderOverride = [provider: Class, value: object];
type ProviderOverrides = ProviderOverride[];

export async function buildApp(options?: {
    providerOverrides?: ProviderOverrides;
}) {
    const { providerOverrides } = options || {};
    const moduleBuilder = await Test.createTestingModule({
        imports: [AppModule],
    });

    const moduleRef = await (providerOverrides || [])
        .reduce(
            (builder, [provider, value]) =>
                builder.overrideProvider(provider).useValue(value),
            moduleBuilder,
        )
        .compile();

    const app = moduleRef.createNestApplication();

    return app;
}

export async function startApp(app: INestApplication) {
    await setupApp(app);
    return app;
}

export async function createApp(options?: {
    providerOverrides?: ProviderOverrides;
}) {
    const app = await buildApp(options);
    await startApp(app);
    return app;
}

export async function stopApp(app: INestApplication) {
    await app.close();
}

export async function loginAsSuperAdmin(host = URL_BASE) {
    const request = supertest.agent(host);

    const username = process.env["ADMIN_USER"];
    const password = process.env["ADMIN_PASS"];

    await request.post("/login").send({ username, password }).expect(204);

    return request;
}

export async function loginAsAdmin(host = URL_BASE) {
    const { email, password } = await createAndOnboardNewUser(host, {
        firstName: "Admin",
        lastName: "User",
        isAdmin: true,
    });

    const request = supertest.agent(host);
    await request
        .post("/login")
        .send({ username: email, password })
        .expect(204);

    return request;
}

export async function createNewUser(
    host: string,
    {
        firstName,
        lastName,
        isAdmin,
    }: {
        firstName: string;
        lastName: string;
        isAdmin?: boolean;
    } = {
        firstName: "First",
        lastName: "Last",
        isAdmin: false,
    },
) {
    const superAdminSession = await loginAsSuperAdmin(host);
    const email = `user-${randomUUID()}@example.com`;

    // Create the user
    await superAdminSession
        .post("/users")
        .set({ origin: host })
        .send({
            firstName,
            lastName,
            email,
            isAdmin,
        })
        .expect(201);

    // Wait for invitation email
    const invitationEmail = await waitFor(() => readLatestEmailTo(email));
    const [link] = invitationEmail.text.match(/http(s?):\/\/\S+/);
    const signupCode = new URL(link).searchParams.get("code");
    const userId = new URL(link).searchParams.get("userId");

    return { email, signupCode, userId };
}

export async function createAndOnboardNewUser(
    host = URL_BASE,
    {
        firstName,
        lastName,
        isAdmin,
    }: {
        firstName: string;
        lastName: string;
        isAdmin?: boolean;
    } = {
        firstName: "First",
        lastName: "Last",
        isAdmin: false,
    },
) {
    const { email, signupCode, userId } = await createNewUser(host, {
        firstName,
        lastName,
        isAdmin,
    });

    // Complete the onboarding with a strong password
    const onboardingSession = supertest.agent(host);
    const password = "StrongP@ssword123";
    await onboardingSession
        .put(`/users/${userId}/onboarding/${signupCode}`)
        .send({
            password,
        })
        .expect(204);

    return { email, password, signupCode, userId };
}

export async function login(host: string = URL_BASE) {
    const { email, password } = await createAndOnboardNewUser(host);

    const request = supertest.agent(host);
    await request
        .post("/login")
        .send({ username: email, password })
        .expect(204);

    return request;
}

export function parseSetCookieHeaders(response: supertest.Response) {
    const setCookieHeader = response.headers["set-cookie"];
    const setCookieHeaders = setCookieHeader as unknown as string[];

    if (!setCookieHeaders) return [];

    return setCookieHeaders.map((cookie) => parseSetCookie(cookie));
}

/**
 * This allows you to wait for something (like an email to be available).
 *
 * It calls the callback every 50ms until it returns a value (and does not throw
 * an error). After the timeout, it will throw the last error that was thrown
 */
export async function waitFor<ReturnValue>(
    cb: () => ReturnValue | Promise<ReturnValue>,
    timeout = 4000,
) {
    const endTime = Date.now() + timeout;
    let lastError;
    while (Date.now() < endTime) {
        try {
            const response = await cb();
            if (response) return response;
        } catch (e: unknown) {
            lastError = e;
        }
        await new Promise((r) => setTimeout(r, 100));
    }
    throw lastError;
}

type Email = {
    from: string;
    to: string;
    subject: string;
    text: string;
    html: string;
};

export async function readLatestEmailTo(
    to: string,
): Promise<Email | undefined> {
    const emails = await fetch("http://localhost:8282/emails").then(
        (response) => response.json(),
    );

    const [lastEmail] = emails.filter((email) => email.to === to).reverse();

    return lastEmail;
}

export async function printFailedBullMQJobs() {
    const queueNames = [
        REDDIT_SUBREDDIT_QUEUE,
        REDDIT_POST_QUEUE,
        AI_ANALYSIS_QUEUE,
        NOTIFICATIONS_PERIODIC_DIGEST_QUEUE,
    ];

    for (const queueName of queueNames) {
        try {
            const queue = new Queue(queueName, {
                connection: {
                    host: process.env.REDIS_HOST,
                    port: process.env.REDIS_PORT,
                },
                prefix: "{BULLMQ}",
            });
            const failedJobs = await queue.getJobs(["failed"], 0, 10);

            if (failedJobs.length > 0) {
                process.stdout.write(`--------------------------------\n`);
                process.stdout.write(
                    `${failedJobs.length} failed ${queueName} jobs:\n`,
                );
                process.stdout.write(`--------------------------------\n\n`);
                for (const job of failedJobs) {
                    process.stdout.write(`${job.name}\n\n`);
                    process.stdout.write(
                        `Failed reason: ${job.failedReason}\n\n`,
                    );
                    process.stdout.write(
                        `${JSON.stringify(job.data, null, 2)}\n\n`,
                    );
                    const { logs } = await queue.getJobLogs(job.id);
                    process.stdout.write(`Logs:\n\n`);
                    for (let i = 0; i < logs.length; i++) {
                        const log = logs[i];
                        process.stdout.write(`${i + 1}. ${log}\n`);
                    }
                    process.stdout.write(
                        `\n--------------------------------\n\n`,
                    );
                }
            }

            await queue.close();
        } catch (error) {
            console.error(
                `Error checking failed jobs for queue ${queueName}:`,
                error,
            );
        }
    }
}
