import nestPlugin from "@darraghor/eslint-plugin-nestjs-typed";
import pluginJs from "@eslint/js";
import globals from "globals";
import tseslint from "typescript-eslint";

export default [
    { files: ["**/*.{js,mjs,cjs,ts}"] },
    {
        languageOptions: {
            globals: {
                ...globals.node,
                ...globals.jest,
            },
            parser: tseslint.parser,
            ecmaVersion: 2022,
            sourceType: "module",
            parserOptions: {
                project: "tsconfig.json",
            },
        },
    },
    pluginJs.configs.recommended,
    ...tseslint.configs.recommended,
    ...nestPlugin.configs.flatRecommended,
    {
        rules: {
            "@darraghor/nestjs-typed/sort-module-metadata-arrays": ["warn"],
        },
    },
    {
        files: ["**/*controller.ts"],
        rules: {
            "@typescript-eslint/explicit-function-return-type": ["error"],
        },
    },
];
